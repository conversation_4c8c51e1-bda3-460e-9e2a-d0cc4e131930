from .stt import <PERSON><PERSON><PERSON><PERSON>
from .custom_handler import <PERSON><PERSON><PERSON><PERSON><PERSON>
from .sphinx_handler import <PERSON><PERSON>nx<PERSON><PERSON><PERSON>
from .witai_handler import WitAIHandler
from .googlesr_handler import GoogleSRHandler
from .vosk_handler import <PERSON>osk<PERSON>and<PERSON>
from .whisper_handler import <PERSON>his<PERSON><PERSON><PERSON><PERSON> 
from .groqsr_handler import <PERSON><PERSON>q<PERSON><PERSON><PERSON><PERSON>
from .openaisr_handler import OpenAIS<PERSON><PERSON><PERSON><PERSON>
from .whispercpp_handler import Whisper<PERSON><PERSON><PERSON>andler

__all__ = [
    "STTHandler",
    "CustomSRHandler",
    "SphinxHandler",
    "WitA<PERSON>Handler",
    "GoogleSRHandler",
    "<PERSON>osk<PERSON><PERSON><PERSON>",
    "<PERSON>his<PERSON><PERSON><PERSON><PERSON>",
    "<PERSON>roqSRHand<PERSON>",
    "OpenAISRH<PERSON>ler",
    "<PERSON>hisper<PERSON><PERSON><PERSON>and<PERSON>"
]
