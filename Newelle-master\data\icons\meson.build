application_id = 'io.github.qwersyk.Newelle'

scalable_dir = join_paths('hicolor', 'scalable', 'apps')
install_data(
  join_paths(scalable_dir, ('@0@.svg').format(application_id)),
  install_dir: join_paths(get_option('datadir'), 'icons', scalable_dir)
)

symbolic_dir = join_paths('hicolor', 'symbolic', 'apps')
install_data(
  join_paths(symbolic_dir, ('@<EMAIL>').format(application_id)),
  install_dir: join_paths(get_option('datadir'), 'icons', symbolic_dir)
)

symbolic_dir = join_paths(get_option('datadir'), 'icons/hicolor/symbolic/apps')
install_data (
    'internet-symbolic.svg',
    'view-show-symbolic.svg',
    'plus-symbolic.svg',
    'attach-symbolic.svg',
    'circle-crossed-symbolic.svg',
    'check-plain-symbolic.svg',
    'user-trash-symbolic.svg',
    'info-outline-symbolic.svg',
    'document-edit-symbolic.svg',
    'zoom-in-symbolic.svg',
    'zoom-out-symbolic.svg',
    'brain-augemnted-symbolic.svg',
    'question-round-outline-symbolic.svg',
    'controls-big-symbolic.svg',
    'vcard-symbolic.svg',
    'settings-symbolic.svg',
    'sidebar-show-right-symbolic.svg',
    'go-home-symbolic.svg',
    'detach-symbolic.svg',
    'sidebar-show-left-symbolic.svg',
    'magic-wand-symbolic.svg',
    install_dir: symbolic_dir
)
install_data (
    'warning-outline-symbolic.svg',
    install_dir: symbolic_dir
)
install_data (
    'star-filled-rounded-symbolic.svg',
    install_dir: symbolic_dir
)
install_data (
    'update-symbolic.svg',
    install_dir: symbolic_dir
)
install_data (
    'search-folder-symbolic.svg',
    install_dir: symbolic_dir
)
install_data (
  'right-large-symbolic.svg',
  install_dir: symbolic_dir
)
install_data (
  'left-large-symbolic.svg',
  install_dir: symbolic_dir
)
install_data (
  'gnome-terminal-symbolic.svg',
  install_dir: symbolic_dir
)
