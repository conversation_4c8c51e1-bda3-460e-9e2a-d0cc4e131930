msgid ""
msgstr ""
"Project-Id-Version: 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-07-03 09:00+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Swedish <<EMAIL>>\n"
"Language: sv\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "API-slutpunkt"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr "API-bas-URL, ändra detta för att använda interferens-API:er"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid "Automatically Serve"
msgstr "Servera automatiskt"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"Kör automatiskt ollama serve i bakgrunden vid behov om den inte redan körs. "
"Du kan avsluta den med killall ollama"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
msgid "Custom Model"
msgstr "Anpassad modell"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
msgid "Use a custom model"
msgstr "Använd en anpassad modell"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
msgid "Ollama Model"
msgstr "Ollama-modell"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
msgid "Name of the Ollama Model"
msgstr "Namnet på Ollama-modellen"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "API-nyckel"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
msgid "API Key for "
msgstr "API-nyckel för "

#: src/handlers/embeddings/openai_handler.py:38
msgid "API base url, change this to use different APIs"
msgstr "API-bas-URL, ändra detta för att använda olika API:er"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
msgid "Use Custom Model"
msgstr "Använd anpassad modell"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "Modell"

#: src/handlers/embeddings/openai_handler.py:44
msgid "Name of the Embedding Model to use"
msgstr "Namnet på inbäddningsmodellen att använda"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
msgid " Model"
msgstr " Modell"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
msgid "The API key to use"
msgstr "API-nyckeln att använda"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
msgid "The model to use"
msgstr "Modellen att använda"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "Max antal tokens"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "Det maximala antalet tokens att generera"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
msgid "Message Streaming"
msgstr "Meddelandeströmning"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "Strömma gradvis meddelandeutdata"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "Kommando att köra för att få botens utdata"

#: src/handlers/llm/custom_handler.py:21
#, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr ""
"Kommando att köra för att få botens svar, {0} kommer att ersättas med en "
"JSON-fil innehållande chatten, {1} med systemprompten"

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "Kommando att köra för att få botens förslag"

#: src/handlers/llm/custom_handler.py:22
#, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"Kommando att köra för att få chattförslag, {0} kommer att ersättas med en "
"JSON-fil innehållande chatten, {1} med de extra prompterna, {2} med antalet "
"förslag att generera. Måste returnera en JSON-array innehållande förslagen "
"som strängar"

#: src/handlers/llm/gpt4all_handler.py:67
msgid "RAM Required: "
msgstr "RAM krävs: "

#: src/handlers/llm/gpt4all_handler.py:68
msgid "Parameters: "
msgstr "Parametrar: "

#: src/handlers/llm/gpt4all_handler.py:69
msgid "Size: "
msgstr "Storlek: "

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
msgid "Model to use"
msgstr "Modell att använda"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
msgid "Name of the model to use"
msgstr "Namnet på modellen att använda"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "Model Manager"
msgstr "Modellhanterare"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "Lista över tillgängliga modeller"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr "Uppdatera G4F"

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "Integritetspolicy"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "Öppna webbplatsen för integritetspolicy"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "Aktivera tänkande"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr "Tillåt tänkande i modellen, endast vissa modeller stöds"

#: src/handlers/llm/ollama_handler.py:176
msgid "Add custom model"
msgstr "Lägg till anpassad modell"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"Lägg till valfri modell i denna lista genom att ange namn:storlek\n"
"Eller valfri gguf från hf med hf.co/användarnamn/modell"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr "Uppdatera Ollama"

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "API-nyckel (obligatorisk)"

#: src/handlers/llm/gemini_handler.py:94
msgid "API key got from ai.google.dev"
msgstr "API-nyckel från ai.google.dev"

#: src/handlers/llm/gemini_handler.py:98
msgid "AI Model to use"
msgstr "AI-modell att använda"

#: src/handlers/llm/gemini_handler.py:103
msgid "Enable System Prompt"
msgstr "Aktivera systemprompt"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr ""
"Vissa modeller stöder inte systemprompt (eller utvecklarinstruktioner), "
"inaktivera det om du får felmeddelanden om det"

#: src/handlers/llm/gemini_handler.py:107
msgid "Inject system prompt"
msgstr "Injicera systemprompt"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr ""
"Även om modellen inte stöder systemprompter, placera prompterna överst i "
"användarmeddelandet"

#: src/handlers/llm/gemini_handler.py:109
msgid "Thinking Settings"
msgstr "Inställningar för tänkande"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "Inställningar för tänkande modeller"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr "Visa tänkande, inaktivera det om din modell inte stöder det"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "Aktivera budget för tänkande"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "Om budget för tänkande ska aktiveras"

#: src/handlers/llm/gemini_handler.py:112
msgid "Thinking Budget"
msgstr "Budget för tänkande"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "Hur mycket tid som ska ägnas åt tänkande"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "Bildutdata"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr "Aktivera bildutdata, stöds endast av gemini-2.0-flash-exp"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "Aktivera säkerhetsinställningar"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr ""
"Aktivera Googles säkerhetsinställningar för att undvika att generera "
"skadligt innehåll"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
msgid "Advanced Parameters"
msgstr "Avancerade parametrar"

#: src/handlers/llm/gemini_handler.py:135
msgid "Enable advanced parameters"
msgstr "Aktivera avancerade parametrar"

#: src/handlers/llm/openai_handler.py:81
#, fuzzy
msgid "Include parameters like Top-P, Temperature, etc."
msgstr "Inkludera parametrar som Max Tokens, Top-P, Temperatur, etc."

#: src/handlers/llm/openai_handler.py:84
msgid "Name of the LLM Model to use"
msgstr "Namnet på LLM-modellen att använda"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "Top-P"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr "Ett alternativ till sampling med temperatur, kallat kärnsampling"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "Temperatur"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr ""
"Vilken samplingstemperatur som ska användas. Högre värden gör utdata mer "
"slumpmässig"

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "Frekvensstraff"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr ""
"Tal mellan -2.0 och 2.0. Positiva värden minskar modellens sannolikhet att "
"upprepa samma rad ordagrant"

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "Närvarostraff"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr ""
"Tal mellan -2.0 och 2.0. Positiva värden minskar modellens sannolikhet att "
"prata om nya ämnen"

#: src/handlers/llm/openai_handler.py:108
#, fuzzy
msgid "Custom Options"
msgstr "Anpassad prompt"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr ""

#: src/handlers/llm/openrouter_handler.py:14
msgid "Provider Sorting"
msgstr "Leverantörssortering"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "Välj leverantörer baserat på prissättning/genomströmning eller latens"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "Pris"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "Genomströmning"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "Latens"

#: src/handlers/llm/openrouter_handler.py:15
msgid "Providers Order"
msgstr "Leverantörsordning"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr ""
"Ange ordning för leverantörer att använda, namn separerade med komma.\n"
"Lämna tomt för att inte specificera"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "Tillåt återfall"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "Tillåt återfall till andra leverantörer"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "Indexera dina dokument"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"Indexera alla dokument i din dokumentmapp. Du måste köra denna åtgärd varje "
"gång du redigerar/skapar ett dokument, ändrar dokumentanalysator eller "
"ändrar inbäddningsmodell"

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "Kommando att köra"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "{0} kommer att ersättas med modellens fullständiga sökväg"

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr ""
"API-nyckel för Google SR, skriv 'default' för att använda standardnyckeln"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "Språk"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "Språket för texten som ska kännas igen i IETF"

#: src/handlers/stt/groqsr_handler.py:14
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr ""
"API-nyckel för Groq SR, skriv 'default' för att använda standardnyckeln"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "Groq-modell"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "Namnet på Groq-modellen"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Ange språket för transkription. Använd ISO 639-1 språkkoder (t.ex. \"en\" "
"för engelska, \"fr\" för franska, etc.). "

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "Slutpunkt för OpenAI-förfrågningar"

#: src/handlers/stt/openaisr_handler.py:18
msgid "API Key for OpenAI"
msgstr "API-nyckel för OpenAI"

#: src/handlers/stt/openaisr_handler.py:25
msgid "Whisper Model"
msgstr "Whisper-modell"

#: src/handlers/stt/openaisr_handler.py:26
msgid "Name of the OpenAI model"
msgstr "Namnet på OpenAI-modellen"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Valfritt: Ange språket för transkription. Använd ISO 639-1 språkkoder (t.ex. "
"\"en\" för engelska, \"fr\" för franska, etc.). "

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "Modellsökväg"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "Absolut sökväg till VOSK-modellen (uppackad)"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
msgid "Name of the Whisper model"
msgstr "Namnet på Whisper-modellen"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "Serveråtkomsttoken för wit.ai"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "Kunde inte förstå ljudet"

#: src/handlers/stt/whispercpp_handler.py:47
#, fuzzy
msgid "Language of the recognition. For example en, it..."
msgstr "Språk för igenkänningen."

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Model Library"
msgstr "Modellbibliotek"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Manage Whisper models"
msgstr "Hantera Whisper-modeller"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "Advanced Settings"
msgstr "Avancerade inställningar"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "More advanced settings"
msgstr "Fler avancerade inställningar"

#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature to use"
msgstr "Temperatur att använda"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt for the recognition"
msgstr "Prompt för igenkänningen"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr "Prompt att använda för igenkänningen"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Endpoint"
msgstr "Slutpunkt"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "Anpassad slutpunkt för tjänsten att använda"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "Röst"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
msgid "The voice to use"
msgstr "Rösten att använda"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "Instruktioner"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr ""
"Instruktioner för röstgenerering. Lämna tomt för att undvika detta fält"

#: src/handlers/tts/custom_handler.py:17
#, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr "{0} kommer att ersättas med filens fullständiga sökväg, {1} med texten"

#: src/handlers/tts/elevenlabs_handler.py:11
msgid "API Key for ElevenLabs"
msgstr "API-nyckel för ElevenLabs"

#: src/handlers/tts/elevenlabs_handler.py:19
msgid "Voice ID to use"
msgstr "Röst-ID att använda"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "Stabilitet"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "röstens stabilitet"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "Likhetsförstärkning"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "Förstärker övergripande röstklarhet och talar-likhet"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "Stilöverdrift"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr "Höga värden rekommenderas om talets stil måste överdrivas"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "Välj önskad röst"

#: src/handlers/websearch/tavily.py:20
msgid "Token"
msgstr "Token"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "Tavily API-nyckel"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "Max antal resultat"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "Antal resultat att beakta"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "Sökdjupet"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"Sökdjupet. Avancerad sökning är skräddarsydd för att hämta de mest relevanta "
"källorna och innehållssnuttarna för din fråga, medan grundläggande sökning "
"ger generiska innehållssnuttar från varje källa. En grundläggande sökning "
"kostar 1 API-kredit, medan en avancerad sökning kostar 2 API-krediter."

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "Sökkategorin"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"Sökkategorin. Nyheter är användbart för att hämta realtidsuppdateringar, "
"särskilt om politik, sport och stora aktuella händelser som täcks av "
"mainstream media. Allmänt är för bredare, mer allmänna sökningar som kan "
"inkludera ett brett utbud av källor."

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "Delar per källa"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr ""
"Antalet innehållsdelar att hämta från varje källa. Varje dels längd är "
"maximalt 500 tecken. Endast tillgängligt när sökdjupet är avancerat."

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "Antal dagar tillbaka från aktuellt datum att inkludera"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "Endast tillgängligt om ämnet är nyheter."

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "Inkludera svar"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"Inkludera ett LLM-genererat svar på den angivna frågan. Grundläggande "
"sökning returnerar ett snabbt svar. Avancerad sökning returnerar ett mer "
"detaljerat svar."

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "Inkludera rått innehåll"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr ""
"Inkludera det rensade och analyserade HTML-innehållet från varje sökresultat."

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "Inkludera bilder"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr "Utför en bildsökning och inkludera resultaten i svaret."

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "Inkludera bildbeskrivningar"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr ""
"När 'Inkludera bilder' är aktiverat, lägg också till en beskrivande text för "
"varje bild."

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "Inkludera domäner"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr "En lista över domäner att specifikt inkludera i sökresultaten."

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "Exkludera domäner"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr "En lista över domäner att specifikt exkludera från sökresultaten."

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "Region"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "Region för sökresultaten"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "Inställningar"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "Profilnamn"

#: src/ui/profile.py:58
msgid "Copied Settings"
msgstr "Kopierade inställningar"

#: src/ui/profile.py:58
msgid "Settings that will be copied to the new profile"
msgstr "Inställningar som kommer att kopieras till den nya profilen"

#: src/ui/profile.py:70
msgid "Create Profile"
msgstr "Skapa profil"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "Importera profil"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
msgid "Edit Profile"
msgstr "Redigera profil"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "Exportera profil"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "Exportera lösenord"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "Exportera även lösenordsliknande fält"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "Exportera profilbild"

#: src/ui/profile.py:89
msgid "Also export the profile picture"
msgstr "Exportera även profilbilden"

#: src/ui/profile.py:109 src/ui/explorer.py:692
msgid "Create"
msgstr "Skapa"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "Verkställ"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr ""
"Inställningarna för den aktuella profilen kommer att kopieras till den nya"

#: src/ui/profile.py:122 src/ui/profile.py:128
msgid "Newelle Profiles"
msgstr "Newelle Profiler"

#: src/ui/profile.py:123
msgid "Export"
msgstr "Exportera"

#: src/ui/profile.py:129
msgid "Import"
msgstr "Importera"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "Ställ in profilbild"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "Trådredigering"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "Inga trådar körs"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "Trådnummer: "

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "Välj profil"

#: src/ui/widgets/profilerow.py:53
msgid "Delete Profile"
msgstr "Ta bort profil"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "Tankegångar"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "Expandera för att se detaljer"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "Tänker..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr "LLM tänker... Expandera för att se tankeprocessen"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "Ingen tankeprocess registrerad"

#: src/ui/widgets/tipscarousel.py:41
msgid "Newelle Tips"
msgstr "Newelle Tips"

#: src/ui/explorer.py:192
msgid "Folder is Empty"
msgstr "Mappen är tom"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr "Filen hittades inte"

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "Öppna i ny flik"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "Öppna i integrerad redigerare"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "Öppna i filhanteraren"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
msgid "Rename"
msgstr "Byt namn"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "Ta bort"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "Kopiera fullständig sökväg"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
msgid "Failed to open file manager"
msgstr "Kunde inte öppna filhanteraren"

#: src/ui/explorer.py:436
msgid "New name:"
msgstr "Nytt namn:"

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "Avbryt"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "Namnbyte lyckades"

#: src/ui/explorer.py:476
#, python-brace-format
msgid "Failed to rename: {}"
msgstr "Misslyckades att byta namn: {}"

#: src/ui/explorer.py:497
msgid "Delete File?"
msgstr "Ta bort fil?"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "Är du säker på att du vill ta bort \"{}\"?"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "Borttagning lyckades"

#: src/ui/explorer.py:525
#, python-brace-format
msgid "Failed to delete: {}"
msgstr "Misslyckades att ta bort: {}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "Sökvägen kopierad till urklipp"

#: src/ui/explorer.py:542
msgid "Failed to copy path"
msgstr "Misslyckades att kopiera sökväg"

#: src/ui/explorer.py:580
msgid "Create new folder"
msgstr "Skapa ny mapp"

#: src/ui/explorer.py:583
msgid "Create new file"
msgstr "Skapa ny fil"

#: src/ui/explorer.py:586
msgid "Open Terminal Here"
msgstr "Öppna terminal här"

#: src/ui/explorer.py:640
msgid "Create New Folder"
msgstr "Skapa ny mapp"

#: src/ui/explorer.py:640
msgid "Folder name:"
msgstr "Mappnamn:"

#: src/ui/explorer.py:644
msgid "Create New File"
msgstr "Skapa ny fil"

#: src/ui/explorer.py:644
msgid "File name:"
msgstr "Filnamn:"

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "Mappen skapades framgångsrikt"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "Filen skapades framgångsrikt"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "En fil eller mapp med det namnet finns redan"

#: src/ui/explorer.py:728
msgid "folder"
msgstr "mapp"

#: src/ui/explorer.py:728
msgid "file"
msgstr "fil"

#: src/ui/explorer.py:730
#, python-brace-format
msgid "Failed to create {}: {}"
msgstr "Kunde inte skapa {}: {}"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "Hjälp"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "Genvägar"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "Ladda om chatt"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "Ladda om mapp"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "Ny flik"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "Klistra in bild"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "Fokusera meddelanderuta"

#: src/ui/shortcuts.py:18
msgid "Start/stop recording"
msgstr "Starta/stoppa inspelning"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "Spara"

#: src/ui/shortcuts.py:20
msgid "Stop TTS"
msgstr "Stoppa TTS"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr "Zooma in"

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr "Zooma ut"

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr "Programutdataövervakare"

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr "Rensa utdata"

#: src/ui/stdout_monitor.py:61
msgid "Start/Stop monitoring"
msgstr "Starta/Stoppa övervakning"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr "Övervakning: Aktiv"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr "Övervakning: Stoppad"

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr "Rader: {}"

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr "Rader: 0"

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "Tillägg"

#: src/ui/extension.py:50
msgid "Installed Extensions"
msgstr "Installerade tillägg"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "Användarhandbok för tillägg"

#: src/ui/extension.py:89
msgid "Download new Extensions"
msgstr "Ladda ner nya tillägg"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "Installera tillägg från fil..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "Newelle"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "Chatten är öppen i minifönster"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "Välkommen till Newelle"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "Din ultimata virtuella assistent."

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "Github-sida"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "Välj din favorit AI-språkmodell"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"Newelle kan användas med flera modeller och leverantörer!\n"
"<b>Obs: Det rekommenderas starkt att läsa sidan Guide till LLM</b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "Guide till LLM"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "Chatta med dina dokument"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr ""
"Newelle kan hämta relevant information från dokument du skickar i chatten "
"eller från dina egna filer! Information relevant för din fråga kommer att "
"skickas till LLM."

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "Kommando virtualisering"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"Newelle kan användas för att köra kommandon på ditt system, men var noga med "
"vad du kör! <b>LLM är inte under vår kontroll, så den kan generera skadlig "
"kod!</b>\n"
"Som standard kommer dina kommandon att vara <b>virtualiserade i Flatpak-"
"miljön</b>, men var uppmärksam!"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "Du kan utöka Newelles funktionalitet med tillägg!"

#: src/ui/presentation.py:136
msgid "Download extensions"
msgstr "Ladda ner tillägg"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "Behörighetsfel"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr ""
"Newelle har inte tillräckliga behörigheter för att köra kommandon på ditt "
"system."

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "Börja använda appen"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "Börja chatta"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "Allmänt"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "LLM"

#: src/ui/settings.py:49 src/constants.py:528
msgid "Prompts"
msgstr "Prompter"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "Kunskap"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "Språkmodell"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "Andra LLM:er"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "Andra tillgängliga LLM-leverantörer"

#: src/ui/settings.py:73
msgid "Advanced LLM Settings"
msgstr "Avancerade LLM-inställningar"

#: src/ui/settings.py:77
msgid "Secondary Language Model"
msgstr "Sekundär språkmodell"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr ""
"Modell som används för sekundära uppgifter, som erbjudande, chattnamn och "
"minnesgenerering"

#: src/ui/settings.py:94
msgid "Embedding Model"
msgstr "Inbäddningsmodell"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"Inbäddning används för att omvandla text till vektorer. Används av "
"långtidsminne och RAG. Att ändra den kan kräva att du omindexerar dokument "
"eller återställer minnet."

#: src/ui/settings.py:105 src/window.py:647
msgid "Long Term Memory"
msgstr "Långtidsminne"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "Behåll minnet av gamla konversationer"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "Webbsökning"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "Sök information på webben"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "Text till tal-program"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "Välj vilken text till tal-motor som ska användas"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "Tal till text-motor"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "Välj vilken taligenkänningsmotor du vill använda"

#: src/ui/settings.py:150
msgid "Automatic Speech To Text"
msgstr "Automatisk tal till text"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr "Starta automatiskt om tal till text i slutet av en text/TTS"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "Promptkontroll"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "Gränssnitt"

#: src/ui/settings.py:162
msgid "Interface Size"
msgstr "Gränssnittsstorlek"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "Justera gränssnittets storlek"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "Redigerarens färgschema"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "Ändra färgschemat för redigeraren och kodblock"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "Dolda filer"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "Visa dolda filer"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "Skicka med ENTER"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"Om aktiverat skickas meddelanden med ENTER, för att gå till en ny rad använd "
"CTRL+ENTER. Om inaktiverat skickas meddelanden med SHIFT+ENTER, och ny rad "
"med ENTER"

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "Ta bort tänkande från historik"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr ""
"Skicka inte gamla tankeblock för resonemangsmodeller för att minska "
"tokenanvändningen"

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "Visa LaTeX"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "Visa LaTeX-formler i chatt"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "Omvänd chattordning"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr ""
"Visa de senaste chatterna överst i chattlistan (byt chatt för att tillämpa)"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "Generera chattnamn automatiskt"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr "Generera chattnamn automatiskt efter de första två meddelandena"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "Antal erbjudanden"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "Antal meddelandeförslag att skicka till chatt "

#: src/ui/settings.py:224
msgid "Username"
msgstr "Användarnamn"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr ""
"Ändra etiketten som visas före ditt meddelande\n"
"Denna information skickas inte till LLM som standard\n"
"Du kan lägga till den i en prompt med variabeln {USER}"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "Neuralt nätverkskontroll"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "Kör kommandon i en virtuell maskin"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "Extern terminal"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr "Välj den externa terminalen där konsolkommandona ska köras"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "Programminne"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "Hur länge programmet kommer ihåg chatten "

#: src/ui/settings.py:266
msgid "Developer"
msgstr "Utvecklare"

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr ""
"Övervaka programutdata i realtid, användbart för felsökning och för att se "
"nedladdningsförlopp"

#: src/ui/settings.py:270
msgid "Open"
msgstr "Öppna"

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr "Ta bort pip-sökväg"

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr "Ta bort de extra installerade beroendena"

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr ""

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr ""

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "Kör kommandon automatiskt"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "Kommandon som boten skriver kommer att köras automatiskt"

#: src/ui/settings.py:313
msgid "Max number of commands"
msgstr "Max antal kommandon"

#: src/ui/settings.py:313
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr ""
"Maximalt antal kommandon som boten kommer att skriva efter en enda "
"användarförfrågan"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "Webbläsare"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "Inställningar för webbläsaren"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "Använd extern webbläsare"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr ""
"Använd en extern webbläsare för att öppna länkar istället för den integrerade"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "Behåll webbläsarsession"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr ""
"Behåll webbläsarsessionen mellan omstarter. Att stänga av detta kräver en "
"omstart av programmet"

#: src/ui/settings.py:361
msgid "Delete browser data"
msgstr "Ta bort webbläsardata"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "Ta bort webbläsarsession och data"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "Startsida för webbläsare"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "Sidan där webbläsaren startar"

#: src/ui/settings.py:375
msgid "Search string"
msgstr "Söksträng"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr "Söksträngen som används i webbläsaren, %s ersätts med frågan"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "Dokumentkällor (RAG)"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "Inkludera innehåll från dina dokument i svaren"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "Dokumentanalysator"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr ""
"Dokumentanalysatorn använder flera tekniker för att extrahera relevant "
"information om dina dokument"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "Läs dokument om de inte stöds"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr ""
"Om LLM inte stöder läsning av dokument, kommer relevant information om "
"dokument som skickats i chatten att ges till LLM med hjälp av din "
"dokumentanalysator."

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "Maximalt antal tokens för RAG"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"Den maximala mängden tokens som ska användas för RAG. Om dokumenten inte "
"överskrider detta tokenantal,\n"
"dumpa alla i kontexten"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "Dokumentmapp"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr ""
"Lägg dokumenten du vill fråga i din dokumentmapp. Dokumentanalysatorn kommer "
"att hitta relevant information i dem om detta alternativ är aktiverat"

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr "Lägg alla dokument du vill indexera i denna mapp"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "Tystnadströskel"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr ""
"Tystnadströskel i sekunder, procentandel av volymen som ska betraktas som "
"tystnad"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "Tystnadstid"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr "Tystnadstid i sekunder innan inspelningen stoppas automatiskt"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "Otillräckliga behörigheter"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr ""
"Newelle har inte tillräckliga behörigheter för att köra kommandon på ditt "
"system, vänligen kör följande kommando"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "Förstått"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr "Pip-sökväg borttagen"

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr ""
"Pip-sökvägen har tagits bort, du kan nu installera om beroendena. Denna "
"åtgärd kräver en omstart av applikationen."

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "Newelle Demo API"

#: src/constants.py:26
msgid "GPT4Free"
msgstr "GPT4Free"

#: src/constants.py:34
msgid "Local Model"
msgstr "Lokal modell"

#: src/constants.py:35
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr ""
"INGET GPU-STÖD, ANVÄND OLLAMA ISTÄLLET. Kör en LLM-modell lokalt, mer "
"sekretess men långsammare"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "Ollama-instans"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "Kör enkelt flera LLM-modeller på din egen hårdvara"

#: src/constants.py:47
msgid "Groq"
msgstr "Groq"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "Google Gemini API"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "OpenAI API"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr ""
"OpenAI API. Anpassade slutpunkter stöds. Använd detta för anpassade "
"leverantörer"

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "Anthropic Claude"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr ""
"Officiella API:er för Anthropic Claudes modeller, med bild- och filstöd, "
"kräver en API-nyckel"

#: src/constants.py:73
msgid "Mistral"
msgstr "Mistral"

#: src/constants.py:74
msgid "Mistral API"
msgstr "Mistral API"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "Openrouter.ai API, stöder många modeller"

#: src/constants.py:87
msgid "Deepseek"
msgstr "Deepseek"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "Deepseek API, starkaste öppen källkodsmodeller"

#: src/constants.py:94 src/constants.py:203
msgid "Custom Command"
msgstr "Anpassat kommando"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "Använd utdata från ett anpassat kommando"

#: src/constants.py:104
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr "Fungerar offline. Optimerad Whisper-implementation skriven i C++"

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "Fungerar offline. Endast engelska stöds"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "Google Taligenkänning"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "Google Taligenkänning online"

#: src/constants.py:124
msgid "Groq Speech Recognition"
msgstr "Groq Taligenkänning"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit AI"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr "wit.ai taligenkänning gratis API (språk valt på webbplatsen)"

#: src/constants.py:137
msgid "Vosk API"
msgstr "Vosk API"

#: src/constants.py:138
msgid "Works Offline"
msgstr "Fungerar offline"

#: src/constants.py:144
msgid "Whisper API"
msgstr "Whisper API"

#: src/constants.py:145
msgid "Uses OpenAI Whisper API"
msgstr "Använder OpenAI Whisper API"

#: src/constants.py:151
msgid "Custom command"
msgstr "Anpassat kommando"

#: src/constants.py:152
msgid "Runs a custom command"
msgstr "Kör ett anpassat kommando"

#: src/constants.py:161
msgid "Google TTS"
msgstr "Google TTS"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "Googles text till tal"

#: src/constants.py:167
msgid "Kokoro TTS"
msgstr "Kokoro TTS"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr "Lätt och snabb öppen källkods TTS-motor. ~3GB beroenden, 400MB modell"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "ElevenLabs TTS"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "Naturligt låtande TTS"

#: src/constants.py:179 src/constants.py:180
msgid "OpenAI TTS"
msgstr "OpenAI TTS"

#: src/constants.py:185
msgid "Groq TTS"
msgstr "Groq TTS"

#: src/constants.py:186
msgid "Groq TTS API"
msgstr "Groq TTS API"

#: src/constants.py:191 src/constants.py:192
msgid "Custom OpenAI TTS"
msgstr "Anpassad OpenAI TTS"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "Espeak TTS"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "Offline TTS"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr ""
"Använd ett anpassat kommando som TTS, {0} kommer att ersättas med texten"

#: src/constants.py:212
msgid "WordLlama"
msgstr "WordLlama"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr ""
"Lätt lokal inbäddningsmodell baserad på llama. Fungerar offline, mycket låg "
"resursanvändning"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "Ollama inbäddning"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr ""
"Använd Ollama-modeller för inbäddning. Fungerar offline, mycket låg "
"resursanvändning"

#: src/constants.py:231
msgid "Use Google Gemini API to get embeddings"
msgstr "Använd Google Gemini API för att få inbäddningar"

#: src/constants.py:239
msgid "User Summary"
msgstr "Användarsammanfattning"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "Generera en sammanfattning av användarens konversation"

#: src/constants.py:245
msgid "Memoripy"
msgstr "Memoripy"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"Extrahera meddelanden från tidigare konversationer med hjälp av kontextuell "
"minneshämtning, minnesförfall, konceptutvinning och andra avancerade "
"tekniker. Gör 1 LLM-anrop per meddelande."

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "Användarsammanfattning + Memoripy"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "Använd båda teknikerna för långtidsminne"

#: src/constants.py:260
msgid "Document reader"
msgstr "Dokumentläsare"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr ""
"Klassisk RAG-metod – dela upp dokument och bädda in dem, jämför dem sedan "
"med frågan och returnera de mest relevanta dokumenten"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr "SearXNG - Privat och självhostad sökmotor"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "DuckDuckGo-sökning"

#: src/constants.py:281
msgid "Tavily"
msgstr "Tavily"

#: src/constants.py:282
msgid "Tavily search"
msgstr "Tavily-sökning"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "Hjälpsam assistent"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr "Generell prompt för att förbättra LLM-svaren och ge mer sammanhang"

#: src/constants.py:384
msgid "Console access"
msgstr "Konsolåtkomst"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "Kan programmet köra terminalkommandon på datorn"

#: src/constants.py:392
msgid "Current directory"
msgstr "Aktuell katalog"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "Vad är den aktuella katalogen"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "Tillåt LLM att söka på internet"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "Grundläggande funktionalitet"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "Visar tabeller och kod (*kan fungera utan det)"

#: src/constants.py:419
msgid "Graphs access"
msgstr "Grafåtkomst"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "Kan programmet visa grafer"

#: src/constants.py:428
msgid "Show image"
msgstr "Visa bild"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "Visa bild i chatt"

#: src/constants.py:437
msgid "Custom Prompt"
msgstr "Anpassad prompt"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "Lägg till din egen anpassade prompt"

#: src/constants.py:480
msgid "LLM and Secondary LLM settings"
msgstr "LLM och sekundära LLM-inställningar"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "TTS"

#: src/constants.py:485
msgid "Text to Speech settings"
msgstr "Inställningar för text till tal"

#: src/constants.py:488
msgid "STT"
msgstr "STT"

#: src/constants.py:490
msgid "Speech to Text settings"
msgstr "Inställningar för tal till text"

#: src/constants.py:493
msgid "Embedding"
msgstr "Inbäddning"

#: src/constants.py:495
msgid "Embedding settings"
msgstr "Inställningar för inbäddning"

#: src/constants.py:498
msgid "Memory"
msgstr "Minne"

#: src/constants.py:500
msgid "Memory settings"
msgstr "Minnesinställningar"

#: src/constants.py:503
msgid "Websearch"
msgstr "Webbsökning"

#: src/constants.py:505
msgid "Websearch settings"
msgstr "Inställningar för webbsökning"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
msgid "Document analyzer settings"
msgstr "Inställningar för dokumentanalysator"

#: src/constants.py:515
msgid "Extensions settings"
msgstr "Inställningar för tillägg"

#: src/constants.py:518
msgid "Inteface"
msgstr "Gränssnitt"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr "Gränssnittsinställningar, dolda filer, omvänd ordning, zoom..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr ""
"Allmänna inställningar, virtualisering, erbjudanden, minneslängd, generera "
"chattnamn automatiskt, aktuell mapp..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr "Promptinställningar, anpassad extra prompt, anpassade prompter..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "Chatt "

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "Terminaltrådar körs fortfarande i bakgrunden"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "När du stänger fönstret kommer de att avslutas automatiskt"

#: src/main.py:210
msgid "Close"
msgstr "Stäng"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "Chatten är omstartad"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "Mappen är omstartad"

#: src/main.py:254
msgid "Chat is created"
msgstr "Chatten är skapad"

#: src/window.py:120
msgid "Keyboard shorcuts"
msgstr "Tangentbordsgenvägar"

#: src/window.py:121
msgid "About"
msgstr "Om"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "Chatt"

#: src/window.py:170
msgid "History"
msgstr "Historik"

#: src/window.py:191
msgid "Create a chat"
msgstr "Skapa en chatt"

#: src/window.py:196
msgid "Chats"
msgstr "Chattar"

#: src/window.py:267
msgid " Stop"
msgstr " Stoppa"

#: src/window.py:282
msgid " Clear"
msgstr " Rensa"

#: src/window.py:297
msgid " Continue"
msgstr " Fortsätt"

#: src/window.py:310
msgid " Regenerate"
msgstr " Återskapa"

#: src/window.py:376
msgid "Send a message..."
msgstr "Skicka ett meddelande..."

#: src/window.py:467
msgid "Explorer Tab"
msgstr "Utforskareflik"

#: src/window.py:468
msgid "Terminal Tab"
msgstr "Terminalflik"

#: src/window.py:469
msgid "Browser Tab"
msgstr "Webbläsarflik"

#: src/window.py:589
msgid "Ask about a website"
msgstr "Fråga om en webbplats"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr ""
"Skriv #https://website.com i chatten för att fråga information om en "
"webbplats"

#: src/window.py:590
msgid "Check out our Extensions!"
msgstr "Kolla in våra tillägg!"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "Vi har många tillägg för olika saker. Kolla in dem!"

#: src/window.py:591
msgid "Chat with documents!"
msgstr "Chatta med dokument!"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr ""
"Lägg till dina dokument i din dokumentmapp och chatta med informationen i "
"dem!"

#: src/window.py:592
msgid "Surf the web!"
msgstr "Surfa på webben!"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr ""
"Aktivera webbsökning för att tillåta LLM att surfa på webben och ge "
"uppdaterade svar"

#: src/window.py:593
msgid "Mini Window"
msgstr "Minifönster"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "Ställ frågor direkt med hjälp av minifönsterläget"

#: src/window.py:594
msgid "Text to Speech"
msgstr "Text till tal"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr "Newelle stöder text till tal! Aktivera det i inställningarna"

#: src/window.py:595
msgid "Keyboard Shortcuts"
msgstr "Tangentbordsgenvägar"

#: src/window.py:595
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "Kontrollera Newelle med tangentbordsgenvägar"

#: src/window.py:596
msgid "Prompt Control"
msgstr "Promptkontroll"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr ""
"Newelle ger dig 100% promptkontroll. Anpassa dina prompter för din "
"användning."

#: src/window.py:597
msgid "Thread Editing"
msgstr "Trådredigering"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "Kontrollera programmen och processerna du kör från Newelle"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr "Programmerbara prompter"

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr ""
"Du kan lägga till dynamiska prompter i Newelle, med villkor och sannolikheter"

#: src/window.py:605
msgid "New Chat"
msgstr "Ny chatt"

#: src/window.py:623
msgid "Provider Errror"
msgstr "Leverantörsfel"

#: src/window.py:646
msgid "Local Documents"
msgstr "Lokala dokument"

#: src/window.py:650
msgid "Web search"
msgstr "Webbsökning"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "Denna leverantör har ingen modelllista"

#: src/window.py:901
msgid " Models"
msgstr " Modeller"

#: src/window.py:904
msgid "Search Models..."
msgstr "Sök modeller..."

#: src/window.py:1132
msgid "Create new profile"
msgstr "Skapa ny profil"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "Kunde inte känna igen din röst"

#: src/window.py:1303
msgid "Images"
msgstr "Bilder"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "LLM-stödda filer"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "RAG-stödda filer"

#: src/window.py:1333
msgid "Supported Files"
msgstr "Filer som stöds"

#: src/window.py:1337
msgid "All Files"
msgstr "Alla filer"

#: src/window.py:1343
msgid "Attach file"
msgstr "Bifoga fil"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "Filen kan inte skickas förrän programmet är klart"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "Filen känns inte igen"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "Du kan inte längre fortsätta meddelandet."

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "Du kan inte längre återskapa meddelandet."

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "Chatten är rensad"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "Meddelandet avbröts och togs bort från historiken"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr "Meddelandet kan inte skickas förrän programmet är klart"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr "Du kan inte redigera ett meddelande medan programmet körs."

#: src/window.py:3080
msgid "Prompt content"
msgstr "Promptinnehåll"

#: src/window.py:3339
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"Det neurala nätverket har åtkomst till din dator och all data i denna chatt "
"och kan köra kommandon, var försiktig, vi ansvarar inte för det neurala "
"nätverket. Dela inte känslig information."

#: src/window.py:3368
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"Det neurala nätverket har åtkomst till all data i denna chatt, var "
"försiktig, vi ansvarar inte för det neurala nätverket. Dela inte känslig "
"information."

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "Fel mappssökväg"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "Tråden har inte slutförts, trådnummer: "

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "Kunde inte öppna mappen"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "Chatten är tom"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr ""
"Träna Newelle att göra mer med anpassade tillägg och nya AI-moduler, vilket "
"ger din chatbot oändliga möjligheter."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "AI-chattbot"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "Snabb profilval"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
msgid "Message Editing"
msgstr "Meddelanderedigering"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "Fler än 10 standard AI-leverantörer"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "Bugfixar"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
#, fuzzy
msgid "Minor bug fixes"
msgstr "Bugfixar"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "Små förbättringar"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "Förbättra läsning och laddning av lokala dokument"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "Lägg till alternativ för att skicka med CTRL+Enter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "Förbättra kodblock"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
msgid "Fix Kokoro TTS"
msgstr "Fixa Kokoro TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "Ta bort emoji från TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "Ställ in API-nycklar som lösenordsfält"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "Lägg till tänkandestöd för Gemini"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "Uppdaterade översättningar"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "Nya funktioner tillagda"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr "Webbplatsläsning och webbsökning med SearXNG, DuckDuckGo och Tavily"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "Förbättrad LaTeX-rendering och dokumenthantering"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "Nytt tänkande-widget och OpenRouter-hanterare"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "Visionstöd för Llama4 på Groq"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "Nya översättningar (Traditionell kinesiska, bengali, hindi)"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "Många buggar åtgärdade, några funktioner tillagda!"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "Stöd för nya funktioner och buggfixar"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "Många nya funktioner och buggfixar har lagts till"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "Nya funktioner och buggfixar har lagts till"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr ""
"Uppdaterade g4f-biblioteket med versionshantering, lade till "
"användarmanualer, förbättrade bläddring av tillägg och förbättrad "
"modellhantering."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"Bugfixar och nya funktioner har implementerats. Vi har ändrat "
"tilläggsarkitekturen, lagt till nya modeller och introducerat visionstöd, "
"tillsammans med fler funktioner."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "Stabil version"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
msgid "Added extension"
msgstr "Tillägg tillagt"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
msgid "Blacklist of commands"
msgstr "Svartlista över kommandon"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "Lokalisering"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "Omdesign"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "Qwersyk"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "Newelle: Din avancerade chattbot"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "ai;assistent;chatt;chatgpt;gpt;llm;ollama;"

#~ msgid "max Tokens"
#~ msgstr "Max antal tokens"

#~ msgid "Max tokens of the generated text"
#~ msgstr "Max antal tokens för den genererade texten"
