#
# Piotr <PERSON> <orzechowski.tech>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-08-17 17:33+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON> <orzechowski.tech>\n"
"Language-Team: Polish\n"
"Language: pl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n==1 ? 0 : n%10>=2 && n%10<=4 && (n%100<10 "
"|| n%100>=20) ? 1 : 2);\n"
"X-Generator: Gtranslator 48.0\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "Punkt końcowy API"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr "Bazowy URL API. Zmień go, aby używać API inferencji"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid "Automatically Serve"
msgstr "Automatyczne uruchamianie serwera"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"Automatycznie uruchamiaj ollama serve w tle, jeśli jest to potrzebne i nie "
"jest uruchomione. Możesz to zatrzymać za pomocą killall ollama"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
msgid "Custom Model"
msgstr "Niestandardowy model"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
msgid "Use a custom model"
msgstr "Użyj niestandardowego modelu"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
msgid "Ollama Model"
msgstr "Model Ollama"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
msgid "Name of the Ollama Model"
msgstr "Nazwa modelu Ollama"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "Klucz API"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
msgid "API Key for "
msgstr "Klucz API "

#: src/handlers/embeddings/openai_handler.py:38
msgid "API base url, change this to use different APIs"
msgstr "Bazowy URL API. Zmień go, aby używać różnych API"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
msgid "Use Custom Model"
msgstr "Użyj niestandardowego modelu"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "Model"

#: src/handlers/embeddings/openai_handler.py:44
msgid "Name of the Embedding Model to use"
msgstr "Nazwa modelu osadzania do użycia"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
msgid " Model"
msgstr " Model"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
msgid "The API key to use"
msgstr "Klucz API do użycia"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
msgid "The model to use"
msgstr "Model do użycia"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "Maksymalna liczba tokenów"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "Maksymalna liczba tokenów do wygenerowania"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
msgid "Message Streaming"
msgstr "Przesyłanie wiadomości strumieniowo"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "Stopniowo przesyłaj wyjście wiadomości"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "Polecenie do wykonania, aby uzyskać odpowiedź bota"

#: src/handlers/llm/custom_handler.py:21
#, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr ""
"Polecenie do wykonania, aby uzyskać odpowiedź bota. {0} zostanie zastąpione "
"plikiem JSON zawierającym czat, {1} - podpowiedzią systemową"

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "Polecenie do wykonania, aby uzyskać sugestie bota"

#: src/handlers/llm/custom_handler.py:22
#, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"Polecenie do wykonania, aby uzyskać sugestie czatu. {0} zostanie zastąpione "
"plikiem JSON zawierającym czat, {1} - dodatkowymi podpowiedziami, {2} - "
"liczbą sugestii do wygenerowania. Musi zwrócić tablicę JSON zawierającą "
"sugestie jako ciągi znaków"

#: src/handlers/llm/gpt4all_handler.py:67
msgid "RAM Required: "
msgstr "Wymagana pamięć RAM: "

#: src/handlers/llm/gpt4all_handler.py:68
msgid "Parameters: "
msgstr "Parametry: "

#: src/handlers/llm/gpt4all_handler.py:69
msgid "Size: "
msgstr "Rozmiar: "

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
msgid "Model to use"
msgstr "Model do użycia"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
msgid "Name of the model to use"
msgstr "Nazwa modelu do użycia"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "Model Manager"
msgstr "Menedżer modeli"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "Lista dostępnych modeli"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr "Aktualizuj G4F"

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "Polityka prywatności"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "Otwórz stronę internetową z polityką prywatności"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "Włącz myślenie"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr "Zezwól na myślenie w modelu - tylko niektóre modele są obsługiwane"

#: src/handlers/llm/ollama_handler.py:176
msgid "Add custom model"
msgstr "Dodaj niestandardowy model"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"Dodaj dowolny model do tej listy, wpisując nazwę:rozmiar\n"
"Lub dowolny gguf z hf z hf.co/nazwa_użytkownika/model"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr "Aktualizuj Ollama"

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "Klucz API (wymagany)"

#: src/handlers/llm/gemini_handler.py:94
msgid "API key got from ai.google.dev"
msgstr "Klucz API uzyskany z ai.google.dev"

#: src/handlers/llm/gemini_handler.py:98
msgid "AI Model to use"
msgstr "Model SI do użycia"

#: src/handlers/llm/gemini_handler.py:103
msgid "Enable System Prompt"
msgstr "Włącz podpowiedź systemową"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr ""
"Niektóre modele nie obsługują podpowiedzi systemowej (lub instrukcji "
"programistów). Wyłącz to, jeśli pojawią się błędy z tym związane"

#: src/handlers/llm/gemini_handler.py:107
msgid "Inject system prompt"
msgstr "Wstrzyknij podpowiedź systemową"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr ""
"Nawet jeśli model nie obsługuje podpowiedzi systemowych, dodawaj je powyżej "
"wiadomości użytkownika"

#: src/handlers/llm/gemini_handler.py:109
msgid "Thinking Settings"
msgstr "Ustawienia myślenia"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "Ustawienia modeli myślących"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr "Pokazuj myślenie. Wyłącz, jeśli Twój model tego nie obsługuje"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "Włącz budżet myślenia"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "Czy włączyć budżet myślenia"

#: src/handlers/llm/gemini_handler.py:112
msgid "Thinking Budget"
msgstr "Budżet myślenia"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "Ile czasu poświęcić na myślenie"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "Wyjście obrazu"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr "Włącz wyjście obrazu. Obsługiwane tylko przez gemini-2.0-flash-exp"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "Włącz ustawienia bezpieczeństwa"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr ""
"Włącz ustawienia bezpieczeństwa Google, aby uniknąć generowania szkodliwych "
"treści"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
msgid "Advanced Parameters"
msgstr "Zaawansowane parametry"

#: src/handlers/llm/gemini_handler.py:135
msgid "Enable advanced parameters"
msgstr "Włącz zaawansowane parametry"

#: src/handlers/llm/openai_handler.py:81
msgid "Include parameters like Top-P, Temperature, etc."
msgstr "Włącz parametry takie jak top-p, temperatura itp."

#: src/handlers/llm/openai_handler.py:84
msgid "Name of the LLM Model to use"
msgstr "Nazwa modelu do użycia"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "Top-P"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr "Alternatywa dla próbkowania z temperaturą, zwana próbkowaniem jądrowym"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "Temperatura"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr ""
"Jaka temperatura próbkowania ma być użyta. Wyższe wartości sprawią, że wynik "
"będzie bardziej losowy"

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "Kara za częstość"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr ""
"Liczba między -2,0 a 2,0. Wartości dodatnie zmniejszają prawdopodobieństwo, "
"że model powtórzy tę samą linię dosłownie"

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "Kara za obecność"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr ""
"Liczba między -2,0 a 2,0. Wartości dodatnie zmniejszają prawdopodobieństwo, "
"że model będzie mówił o nowych tematach"

#: src/handlers/llm/openai_handler.py:108
#, fuzzy
msgid "Custom Options"
msgstr "Niestandardowa podpowiedź"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr ""

#: src/handlers/llm/openrouter_handler.py:14
msgid "Provider Sorting"
msgstr "Sortowanie dostawców"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "Wybierz dostawców na podstawie cen/przepustowości lub opóźnień"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "Cena"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "Przepustowość"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "Opóźnienie"

#: src/handlers/llm/openrouter_handler.py:15
msgid "Providers Order"
msgstr "Kolejność dostawców"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr ""
"Dodaj kolejność dostawców do użycia, nazwy oddzielone przecinkami.\n"
"Puste, aby nie określać"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "Zezwól na dostawców rezerwowych"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "Zezwól na przełączanie na dostawców rezerwowych"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "Zindeksuj swoje dokumenty"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"Zindeksuj wszystkie dokumenty w folderze dokumentów. Musisz uruchamiać tę "
"operację za każdym razem, gdy edytujesz/tworzysz dokument, zmieniasz "
"analizator dokumentów lub zmieniasz model osadzania"

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "Polecenie do wykonania"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "{0} zostanie zastąpione pełną ścieżką do modelu"

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr "Klucz API Google SR. Wpisz „default”, aby użyć domyślnego"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "Język"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "Język tekstu do rozpoznania, w formacie IETF"

#: src/handlers/stt/groqsr_handler.py:14
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr "Klucz API Groq SR. Wpisz „default”, aby użyć domyślnego"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "Model Groq"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "Nazwa modelu Groq"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Określ język transkrypcji. Użyj kodów językowych ISO 639-1 (np. „en” dla "
"angielskiego, „fr” dla francuskiego itd.). "

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "Punkt końcowy żądań OpenAI"

#: src/handlers/stt/openaisr_handler.py:18
msgid "API Key for OpenAI"
msgstr "Klucz API OpenAI"

#: src/handlers/stt/openaisr_handler.py:25
msgid "Whisper Model"
msgstr "Model Whisper"

#: src/handlers/stt/openaisr_handler.py:26
msgid "Name of the OpenAI model"
msgstr "Nazwa modelu OpenAI"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Opcjonalnie: Określ język transkrypcji. Użyj kodów językowych ISO 639-1 (np. "
"„en” dla angielskiego, „fr” dla francuskiego itd.). "

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "Ścieżka do modelu"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "Bezwzględna ścieżka do modelu VOSK (rozpakowanego)"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
msgid "Name of the Whisper model"
msgstr "Nazwa modelu Whisper"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "Token dostępu do serwera wit.ai"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "Nie można zrozumieć dźwięku"

#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language of the recognition. For example en, it..."
msgstr "Język rozpoznawania, np. en, it..."

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Model Library"
msgstr "Biblioteka modeli"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Manage Whisper models"
msgstr "Zarządzaj modelami Whisper"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "Advanced Settings"
msgstr "Ustawienia zaawansowane"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "More advanced settings"
msgstr "Więcej ustawień zaawansowanych"

#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature to use"
msgstr "Temperatura do użycia"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt for the recognition"
msgstr "Podpowiedź rozpoznawania"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr "Podpowiedź do użycia przy rozpoznawaniu"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Endpoint"
msgstr "Punkt końcowy"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "Niestandardowy punkt końcowy usługi do użycia"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "Głos"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
msgid "The voice to use"
msgstr "Głos do użycia"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "Instrukcje"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr ""
"Instrukcje dotyczące generowania głosu. Pozostaw puste, aby zignorować to "
"pole"

#: src/handlers/tts/custom_handler.py:17
#, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr "{0} zostanie zastąpione pełną ścieżką do pliku, {1} - tekstem"

#: src/handlers/tts/elevenlabs_handler.py:11
msgid "API Key for ElevenLabs"
msgstr "Klucz API ElevenLabs"

#: src/handlers/tts/elevenlabs_handler.py:19
msgid "Voice ID to use"
msgstr "Identyfikator głosu do użycia"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "Stabilność"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "stabilność głosu"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "Wzmocnienie podobieństwa"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "Zwiększa ogólną klarowność głosu i podobieństwo mówcy"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "Przejaskrawienie stylu"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr ""
"Zalecane są wysokie wartości, jeśli styl wypowiedzi ma być przejaskrawiony"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "Wybierz preferowany głos"

#: src/handlers/websearch/tavily.py:20
msgid "Token"
msgstr "Token"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "Klucz API Tavily"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "Maks. wyników"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "Liczba wyników do rozważenia"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "Dogłębność wyszukiwania"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"Dogłębność wyszukiwania. Wyszukiwanie zaawansowane jest dostosowane do "
"pobierania najbardziej odpowiednich źródeł i fragmentów treści dla Twojego "
"zapytania, podczas gdy wyszukiwanie podstawowe dostarcza ogólne fragmenty "
"treści z każdego źródła. Podstawowe wyszukiwanie kosztuje 1 kredyt API, a "
"zaawansowane - 2 kredyty."

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "Kategoria wyszukiwania"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"Kategoria wyszukiwania. „Wiadomości” są przydatne do pobierania nowinek w "
"czasie rzeczywistym, zwłaszcza dotyczących polityki, sportu i ważnych, "
"bieżących wydarzeń relacjonowanych przez główne media. „Ogólne” służy do "
"szerszych, bardziej ogólnych wyszukiwań, które mogą obejmować szeroki zakres "
"źródeł."

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "Bloki na źródło"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr ""
"Liczba bloków treści do pobrania z każdego źródła. Długość każdego bloku "
"wynosi maksymalnie 500 znaków. Dostępne tylko przy zaawansowanej dogłębności "
"wyszukiwania."

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "Liczba dni wstecz od bieżącej daty do uwzględnienia"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "Dostępne tylko dla kategorii „wiadomości”."

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "Dołącz odpowiedź"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"Dołącz odpowiedź wygenerowaną przez model do podanego zapytania. Podstawowe "
"wyszukiwanie zwraca szybką odpowiedź. Zaawansowane zwraca bardziej "
"szczegółową odpowiedź."

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "Dołącz nieobrobioną treść"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr ""
"Dołącz oczyszczoną i przeanalizowaną treść HTML każdego wyniku wyszukiwania."

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "Dołącz obrazy"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr "Wykonaj wyszukiwanie obrazów i dołącz wyniki do odpowiedzi."

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "Dołącz opisy obrazów"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr ""
"Gdy opcja „Dołącz obrazy” jest włączona, dodaj również opis tekstowy każdego "
"obrazu."

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "Dołącz domeny"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr "Lista domen, które mają być uwzględnione w wynikach wyszukiwania."

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "Wyklucz domeny"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr "Lista domen, które mają być wykluczone z wyników wyszukiwania."

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "Region"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "Region wyników wyszukiwania"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "Ustawienia"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "Nazwa profilu"

#: src/ui/profile.py:58
msgid "Copied Settings"
msgstr "Ustawienia do skopiowania"

#: src/ui/profile.py:58
msgid "Settings that will be copied to the new profile"
msgstr "Ustawienia, które zostaną skopiowane do nowego profilu"

#: src/ui/profile.py:70
msgid "Create Profile"
msgstr "Utwórz profil"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "Importuj profil"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
msgid "Edit Profile"
msgstr "Edytuj profil"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "Eksportuj profil"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "Eksportuj hasła"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "Eksportuj również pola podobne do haseł"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "Eksportuj zdjęcie profilowe"

#: src/ui/profile.py:89
msgid "Also export the profile picture"
msgstr "Eksportuj również zdjęcie profilowe"

#: src/ui/profile.py:109 src/ui/explorer.py:692
msgid "Create"
msgstr "Utwórz"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "Zastosuj"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr "Ustawienia bieżącego profilu zostaną skopiowane do nowego profilu"

#: src/ui/profile.py:122 src/ui/profile.py:128
msgid "Newelle Profiles"
msgstr "Profile Newelle"

#: src/ui/profile.py:123
msgid "Export"
msgstr "Eksportuj"

#: src/ui/profile.py:129
msgid "Import"
msgstr "Importuj"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "Ustaw zdjęcie profilowe"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "Edycja wątków"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "Nie uruchomiono żadnych wątków"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "Numer wątku: "

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "Wybierz profil"

#: src/ui/widgets/profilerow.py:53
msgid "Delete Profile"
msgstr "Usuń profil"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "Myśli"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "Rozwiń, aby zobaczyć szczegóły"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "Myślenie..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr "Model myśli... Rozwiń, aby zobaczyć proces myślowy"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "Nie zarejestrowano żadnego procesu myślowego"

#: src/ui/widgets/tipscarousel.py:41
msgid "Newelle Tips"
msgstr "Wskazówki Newelle"

#: src/ui/explorer.py:192
msgid "Folder is Empty"
msgstr "Folder jest pusty"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr "Nie odnaleziono pliku"

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "Otwórz w nowej karcie"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "Otwórz w zintegrowanym edytorze"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "Otwórz w menedżerze plików"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
msgid "Rename"
msgstr "Zmień nazwę"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "Usuń"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "Kopiuj pełną ścieżkę"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
msgid "Failed to open file manager"
msgstr "Nie udało się otworzyć menedżera plików"

#: src/ui/explorer.py:436
msgid "New name:"
msgstr "Nowa nazwa:"

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "Anuluj"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "Pomyślnie zmieniono nazwę"

#: src/ui/explorer.py:476
#, python-brace-format
msgid "Failed to rename: {}"
msgstr "Nie udało się zmienić nazwy: {}"

#: src/ui/explorer.py:497
msgid "Delete File?"
msgstr "Usunąć plik?"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "Czy na pewno chcesz usunąć „{}”?"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "Pomyślnie usunięto"

#: src/ui/explorer.py:525
#, python-brace-format
msgid "Failed to delete: {}"
msgstr "Nie udało się usunąć: {}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "Ścieżka skopiowana do schowka"

#: src/ui/explorer.py:542
msgid "Failed to copy path"
msgstr "Nie udało się skopiować ścieżki"

#: src/ui/explorer.py:580
msgid "Create new folder"
msgstr "Utwórz nowy folder"

#: src/ui/explorer.py:583
msgid "Create new file"
msgstr "Utwórz nowy plik"

#: src/ui/explorer.py:586
msgid "Open Terminal Here"
msgstr "Otwórz tutaj terminal"

#: src/ui/explorer.py:640
msgid "Create New Folder"
msgstr "Utwórz nowy folder"

#: src/ui/explorer.py:640
msgid "Folder name:"
msgstr "Nazwa folderu:"

#: src/ui/explorer.py:644
msgid "Create New File"
msgstr "Utwórz nowy plik"

#: src/ui/explorer.py:644
msgid "File name:"
msgstr "Nazwa pliku:"

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "Pomyślnie utworzono folder"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "Pomyślnie utworzono plik"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "Plik lub folder o tej nazwie już istnieje"

#: src/ui/explorer.py:728
msgid "folder"
msgstr "folder"

#: src/ui/explorer.py:728
msgid "file"
msgstr "plik"

#: src/ui/explorer.py:730
#, python-brace-format
msgid "Failed to create {}: {}"
msgstr "Nie udało się utworzyć {}: {}"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "Pomoc"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "Skróty klawiaturowe"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "Ponownie wczytaj czat"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "Ponownie wczytaj folder"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "Nowa karta"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "Wklej obraz"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "Ustaw na polu wiadomości"

#: src/ui/shortcuts.py:18
msgid "Start/stop recording"
msgstr "Rozpocznij/zatrzymaj nagrywanie"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "Zapisz"

#: src/ui/shortcuts.py:20
msgid "Stop TTS"
msgstr "Zatrzymaj TTS"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr "Powiększ"

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr "Pomniejsz"

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr "Śledzenie wyjścia programu"

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr "Wyczyść wyjście"

#: src/ui/stdout_monitor.py:61
msgid "Start/Stop monitoring"
msgstr "Rozpocznij/Zatrzymaj śledzenie"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr "Śledzenie: Aktywne"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr "Śledzenie: Zatrzymane"

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr "Linii: {}"

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr "Linii: 0"

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "Rozszerzenia"

#: src/ui/extension.py:50
msgid "Installed Extensions"
msgstr "Zainstalowane rozszerzenia"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "Instrukcja obsługi rozszerzeń"

#: src/ui/extension.py:89
msgid "Download new Extensions"
msgstr "Pobierz nowe rozszerzenia"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "Zainstaluj rozszerzenie z pliku..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "Newelle"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "Czat jest otwarty w mini oknie"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "Witaj w Newelle"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "Twój najlepszy wirtualny asystent."

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "Strona GitHub"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "Wybierz swój ulubiony model językowy SI"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"Newelle może być używany z wieloma modelami i dostawcami!\n"
"<b>Uwaga: Zdecydowanie zaleca się zapoznanie się ze stroną Przewodnika po "
"dużych modelach językowych</b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "Przewodnik po dużych modelach językowych"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "Rozmawiaj ze swoimi dokumentami"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr ""
"Newelle może pobierać istotne informacje z dokumentów, które wysyłasz w "
"czacie lub z własnych plików! Informacje związane z Twoim zapytaniem zostaną "
"przesłane do modelu."

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "Wirtualizacja poleceń"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"Newelle może być używane do uruchamiania poleceń w Twoim systemie, ale zwróć "
"uwagę na to, co uruchamiasz! <b>Duży model językowy nie jest pod naszą "
"kontrolą, więc może generować złośliwy kod!</b>\n"
"Domyślnie Twoje polecenia będą <b>wirtualizowane w środowisku Flatpak</b>, "
"ale bądź ostrożny!"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "Możesz rozszerzyć funkcjonalność Newelle za pomocą rozszerzeń!"

#: src/ui/presentation.py:136
msgid "Download extensions"
msgstr "Pobierz rozszerzenia"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "Błąd uprawnień"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr ""
"Newelle nie ma wystarczających uprawnień do uruchamiania poleceń w Twoim "
"systemie."

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "Rozpocznij korzystanie z programu"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "Rozpocznij czat"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "Ogólne"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "LLM"

#: src/ui/settings.py:49 src/constants.py:528
msgid "Prompts"
msgstr "Podpowiedzi"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "Wiedza"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "Duży model językowy (LLM)"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "Inne duże modele językowe"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "Inni dostępni dostawcy dużych modeli językowych"

#: src/ui/settings.py:73
msgid "Advanced LLM Settings"
msgstr "Zaawansowane ustawienia dużego modelu językowego"

#: src/ui/settings.py:77
msgid "Secondary Language Model"
msgstr "Poboczny model językowy"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr ""
"Model używany do zadań pobocznych, takich jak sugestie, nazwa czatu i "
"generowanie pamięci"

#: src/ui/settings.py:94
msgid "Embedding Model"
msgstr "Model osadzania"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"Osadzanie służy do przekształcania tekstu w wektory. Używane przez pamięć "
"długoterminową i RAG. Zmiana może wymagać ponownego zindeksowania dokumentów "
"lub zresetowania pamięci."

#: src/ui/settings.py:105 src/window.py:647
msgid "Long Term Memory"
msgstr "Pamięć długoterminowa"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "Zachowaj pamięć starych rozmów"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "Wyszukiwanie w sieci"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "Szukaj informacji w sieci"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "Program zamiany tekstu na mowę (TTS)"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "Wybierz który program zamiany tekstu na mowę ma być używany"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "Mechanizm zamiany mowy na tekst (STT)"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "Wybierz mechanizm rozpoznawania mowy, którego chcesz użyć"

#: src/ui/settings.py:150
msgid "Automatic Speech To Text"
msgstr "Automatyczna zamiana mowy na tekst (STT)"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr "Automatycznie wznawiaj zamianę mowy na tekst po zakończeniu tekstu/TTS"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "Kontrola podpowiedzi"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "Interfejs"

#: src/ui/settings.py:162
msgid "Interface Size"
msgstr "Rozmiar interfejsu"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "Dostosuj rozmiar interfejsu"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "Schemat kolorów edytora"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "Zmień schemat kolorów edytora i bloków kodu"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "Ukryte pliki"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "Pokazuj ukryte pliki"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "Wysyłaj za pomocą klawisza ENTER"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"Jeśli włączone, wiadomości będą wysyłane za pomocą klawisza ENTER; aby "
"przejść do nowej linii, użyj kombinacji CTRL+ENTER. Jeśli wyłączone, "
"wiadomości będą wysyłane za pomocą kombinacji SHIFT+ENTER, a nowa linia - "
"klawiszem ENTER"

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "Usuwaj myśli z historii"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr ""
"Nie wysyłaj starych bloków myślenia modeli wnioskujących, aby zmniejszyć "
"zużycie tokenów"

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "Wyświetlaj LaTeX"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "Wyświetlaj formuły LaTeX w czacie"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "Odwróć kolejność czatów"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr ""
"Pokazuj najnowsze czaty na górze listy czatów (zmień czat, aby zastosować)"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "Automatycznie generuj nazwy czatów"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr "Generuj nazwy czatów automatycznie po pierwszych dwóch wiadomościach"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "Liczba sugestii"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "Liczba sugerowanych wiadomości do wysłania na czat "

#: src/ui/settings.py:224
msgid "Username"
msgstr "Nazwa użytkownika"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr ""
"Zmień etykietę, która pojawia się przed Twoją wiadomością\n"
"Ta informacja nie jest domyślnie wysyłana do dużego modelu językowego\n"
"Możesz dodać ją do podpowiedzi za pomocą zmiennej {USER}"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "Kontrola sieci neuronowej"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "Uruchamiaj polecenia w maszynie wirtualnej"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "Zewnętrzny terminal"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr "Wybierz zewnętrzny terminal, w którym mają być uruchamiane polecenia"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "Pamięć programu"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "Jak długo program pamięta czat "

#: src/ui/settings.py:266
msgid "Developer"
msgstr "Deweloper"

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr ""
"Śledź wyjście programu w czasie rzeczywistym. Przydatne do debugowania i "
"śledzenia postępu pobierania"

#: src/ui/settings.py:270
msgid "Open"
msgstr "Otwórz"

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr "Usuń ścieżkę pip"

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr "Usuń dodatkowe zainstalowane zależności"

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr ""

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr ""

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "Automatyczne uruchamianie poleceń"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "Polecenia napisane przez bota będą uruchamiane automatycznie"

#: src/ui/settings.py:313
msgid "Max number of commands"
msgstr "Maksymalna liczba poleceń"

#: src/ui/settings.py:313
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr ""
"Maksymalna liczba poleceń, które bot napisze po pojedynczym żądaniu "
"użytkownika"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "Przeglądarka"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "Ustawienia przeglądarki"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "Używaj zewnętrznej przeglądarki"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr ""
"Używaj zewnętrznej przeglądarki do otwierania linków, zamiast zintegrowanej"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "Zachowuj sesję przeglądarki"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr ""
"Zachowuj sesję przeglądarki między ponownymi uruchomieniami. Wyłączenie tej "
"opcji wymaga ponownego uruchomienia programu"

#: src/ui/settings.py:361
msgid "Delete browser data"
msgstr "Usuń dane przeglądarki"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "Usuń sesję i dane przeglądarki"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "Strona początkowa przeglądarki"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "Strona, która będzie wyświetlana po uruchomieniu przeglądarki"

#: src/ui/settings.py:375
msgid "Search string"
msgstr "Ciąg wyszukiwania"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr ""
"Ciąg wyszukiwania używany w przeglądarce, %s jest zastępowany zapytaniem"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "Źródła dokumentów (używane w RAG)"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "Dołączaj treści z dokumentów do odpowiedzi"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "Analizator dokumentów"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr ""
"Analizator dokumentów wykorzystuje wiele technik do wyodrębniania istotnych "
"informacji z Twoich dokumentów"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "Czytaj dokumenty, jeśli nieobsługiwane"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr ""
"Jeśli model nie obsługuje czytania dokumentów, istotne informacje o "
"dokumentach wysłanych na czacie zostaną przekazane modelowi za pomocą "
"analizatora dokumentów."

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "Maksymalna liczba tokenów w RAG"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"Maksymalna liczba tokenów do użycia przez RAG. Jeśli dokumenty nie "
"przekraczają tej liczby tokenów,\n"
"umieść je wszystkie w kontekście"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "Folder dokumentów"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr ""
"Umieść dokumenty, które chcesz przetwarzać, w folderze dokumentów. "
"Analizator dokumentów znajdzie w nich istotne informacje, jeśli ta opcja "
"jest włączona"

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr "Umieść wszystkie dokumenty, które chcesz zindeksować, w tym folderze"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "Próg ciszy"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr "Próg ciszy w sekundach, procent głośności do uznania za ciszę"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "Czas trwania ciszy"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr ""
"Czas trwania ciszy w sekundach, po którym nagrywanie zatrzymuje się "
"automatycznie"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "Brak wystarczających uprawnień"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr ""
"Newelle nie ma wystarczających uprawnień do uruchamiania poleceń w Twoim "
"systemie. Proszę uruchomić następujące polecenie"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "Rozumiem"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr "Ścieżka pip usunięta"

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr ""
"Ścieżka pip została usunięta, możesz teraz ponownie zainstalować zależności. "
"Ta operacja wymaga ponownego uruchomienia programu."

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "API demonstracyjne Newelle"

#: src/constants.py:26
msgid "GPT4Free"
msgstr "GPT4Free"

#: src/constants.py:34
msgid "Local Model"
msgstr "Model lokalny"

#: src/constants.py:35
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr ""
"BRAK OBSŁUGI GPU, ZAMIAST TEGO UŻYJ OLLAMA. Uruchom model lokalnie - więcej "
"prywatności, ale wolniejsze"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "Instancja Ollama"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "Łatwo uruchamiaj wiele modeli na własnym sprzęcie"

#: src/constants.py:47
msgid "Groq"
msgstr "Groq"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "API Google Gemini"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "API OpenAI"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr ""
"API OpenAI. Obsługiwane niestandardowe punkty końcowe. Użyj tego dla "
"niestandardowych dostawców"

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "Anthropic Claude"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr ""
"Oficjalne API modeli Anthropic Claude, z obsługą obrazów i plików; wymaga "
"klucza API"

#: src/constants.py:73
msgid "Mistral"
msgstr "Mistral"

#: src/constants.py:74
msgid "Mistral API"
msgstr "API Mistral"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "API openrouter.ai, obsługuje wiele modeli"

#: src/constants.py:87
msgid "Deepseek"
msgstr "DeepSeek"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "API DeepSeek - najsilniejszych modeli open source"

#: src/constants.py:94 src/constants.py:203
msgid "Custom Command"
msgstr "Niestandardowe polecenie"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "Używaj wyjścia niestandardowego polecenia"

#: src/constants.py:104
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr ""
"Działa bez dostępu do sieci. Zoptymalizowana implementacja Whisper, napisana "
"w C++"

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "Działa bez dostępu do sieci. Obsługiwany tylko język angielski"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "Rozpoznawanie mowy Google"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "Zdalne rozpoznawanie mowy Google"

#: src/constants.py:124
msgid "Groq Speech Recognition"
msgstr "Rozpoznawanie mowy Groq"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit AI"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr ""
"Darmowe API rozpoznawania mowy wit.ai (język wybierany na stronie "
"internetowej)"

#: src/constants.py:137
msgid "Vosk API"
msgstr "API Vosk"

#: src/constants.py:138
msgid "Works Offline"
msgstr "Działa bez dostępu do sieci"

#: src/constants.py:144
msgid "Whisper API"
msgstr "API Whisper"

#: src/constants.py:145
msgid "Uses OpenAI Whisper API"
msgstr "Używa API OpenAI Whisper"

#: src/constants.py:151
msgid "Custom command"
msgstr "Niestandardowe polecenie"

#: src/constants.py:152
msgid "Runs a custom command"
msgstr "Uruchamia niestandardowe polecenie"

#: src/constants.py:161
msgid "Google TTS"
msgstr "Google"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "Zamiana tekstu na mowę Google"

#: src/constants.py:167
msgid "Kokoro TTS"
msgstr "Kokoro"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr ""
"Lekki i szybki otwartoźródłowy mechanizm TTS. ~3 GiB zależności; model 400 "
"MiB"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "ElevenLabs"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "Naturalnie brzmiąca zamiana tekstu na mowę"

#: src/constants.py:179 src/constants.py:180
msgid "OpenAI TTS"
msgstr "OpenAI"

#: src/constants.py:185
msgid "Groq TTS"
msgstr "Groq"

#: src/constants.py:186
msgid "Groq TTS API"
msgstr "API Groq"

#: src/constants.py:191 src/constants.py:192
msgid "Custom OpenAI TTS"
msgstr "Niestandardowe API OpenAI"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "Espeak"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "Rozpoznawanie mowy bez dostępu do sieci"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr ""
"Użyj niestandardowego polecenia jako TTS. {0} zostanie zastąpione tekstem"

#: src/constants.py:212
msgid "WordLlama"
msgstr "WordLlama"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr ""
"Lekki lokalny model osadzania oparty na llama. Działa bez dostępu do sieci; "
"bardzo niskie zużycie zasobów"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "Osadzanie Ollama"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr ""
"Użyj modeli Ollama do osadzania. Działa bez dostępu do sieci; bardzo niskie "
"zużycie zasobów"

#: src/constants.py:231
msgid "Use Google Gemini API to get embeddings"
msgstr "Użyj API Google Gemini, aby uzyskać osadzenia"

#: src/constants.py:239
msgid "User Summary"
msgstr "Podsumowanie użytkownika"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "Generuj podsumowanie rozmowy użytkownika"

#: src/constants.py:245
msgid "Memoripy"
msgstr "Memoripy"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"Wyodrębniaj wiadomości z poprzednich rozmów za pomocą kontekstowego "
"odzyskiwania pamięci, zanikania pamięci, ekstrakcji pojęć i innych "
"zaawansowanych technik. Wykonuje 1 wywołanie modelu na wiadomość."

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "Podsumowanie użytkownika + Memoripy"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "Używaj obu technologii do obsługi pamięci długoterminowej"

#: src/constants.py:260
msgid "Document reader"
msgstr "Czytnik dokumentów"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr ""
"Klasyczne podejście RAG - dziel dokumenty na fragmenty i osadzaj je, a "
"następnie porównuj z zapytaniem i zwracaj najbardziej pasujące dokumenty"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr "SearXNG - Prywatna i samodzielnie hostowana wyszukiwarka"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "Wyszukiwanie DuckDuckGo"

#: src/constants.py:281
msgid "Tavily"
msgstr "Tavily"

#: src/constants.py:282
msgid "Tavily search"
msgstr "Wyszukiwanie Tavily"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "Pomocny asystent"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr ""
"Podpowiedź ogólnego przeznaczenia do ulepszania odpowiedzi modelu i "
"dostarczania szerszego kontekstu"

#: src/constants.py:384
msgid "Console access"
msgstr "Dostęp do konsoli"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "Czy program może uruchamiać polecenia terminala na komputerze"

#: src/constants.py:392
msgid "Current directory"
msgstr "Bieżący katalog"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "Jaki jest bieżący katalog"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "Zezwól modelowi na wyszukiwanie w Internecie"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "Podstawowa funkcjonalność"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "Pokazywanie tabel i kodu (*może działać bez tego)"

#: src/constants.py:419
msgid "Graphs access"
msgstr "Dostęp do wykresów"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "Czy program może wyświetlać wykresy"

#: src/constants.py:428
msgid "Show image"
msgstr "Pokazuj obrazy"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "Pokazuj obrazy w czacie"

#: src/constants.py:437
msgid "Custom Prompt"
msgstr "Niestandardowa podpowiedź"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "Dodawaj własną niestandardową podpowiedź"

#: src/constants.py:480
msgid "LLM and Secondary LLM settings"
msgstr "Ustawienia modelu i pomocniczego modelu"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "TTS"

#: src/constants.py:485
msgid "Text to Speech settings"
msgstr "Ustawienia zamiany tekstu na mowę"

#: src/constants.py:488
msgid "STT"
msgstr "STT"

#: src/constants.py:490
msgid "Speech to Text settings"
msgstr "Ustawienia zamiany mowy na tekst"

#: src/constants.py:493
msgid "Embedding"
msgstr "Osadzanie"

#: src/constants.py:495
msgid "Embedding settings"
msgstr "Ustawienia osadzania"

#: src/constants.py:498
msgid "Memory"
msgstr "Pamięć"

#: src/constants.py:500
msgid "Memory settings"
msgstr "Ustawienia pamięci"

#: src/constants.py:503
msgid "Websearch"
msgstr "Wyszukiwanie w sieci"

#: src/constants.py:505
msgid "Websearch settings"
msgstr "Ustawienia wyszukiwania w sieci"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
msgid "Document analyzer settings"
msgstr "Ustawienia analizatora dokumentów"

#: src/constants.py:515
msgid "Extensions settings"
msgstr "Ustawienia rozszerzeń"

#: src/constants.py:518
msgid "Inteface"
msgstr "Interfejs"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr ""
"Ustawienia interfejsu, ukryte pliki, odwrotna kolejność, powiększenie..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr ""
"Ustawienia ogólne, wirtualizacja, sugestie, długość pamięci, automatyczne "
"generowanie nazwy czatu, bieżący folder..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr ""
"Ustawienia podpowiedzi, niestandardowa dodatkowa podpowiedź, niestandardowe "
"podpowiedzi..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "Czat "

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "Wątki terminala nadal działają w tle"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "Gdy zamkniesz okno, zostaną one automatycznie zakończone"

#: src/main.py:210
msgid "Close"
msgstr "Zamknij"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "Czat został ponownie wczytany"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "Folder został ponownie wczytany"

#: src/main.py:254
msgid "Chat is created"
msgstr "Czat został utworzony"

#: src/window.py:120
msgid "Keyboard shorcuts"
msgstr "Skróty klawiaturowe"

#: src/window.py:121
msgid "About"
msgstr "O programie"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "Czat"

#: src/window.py:170
msgid "History"
msgstr "Historia"

#: src/window.py:191
msgid "Create a chat"
msgstr "Rozpocznij nowy czat"

#: src/window.py:196
msgid "Chats"
msgstr "Czaty"

#: src/window.py:267
msgid " Stop"
msgstr " Zatrzymaj"

#: src/window.py:282
msgid " Clear"
msgstr " Wyczyść"

#: src/window.py:297
msgid " Continue"
msgstr " Kontynuuj"

#: src/window.py:310
msgid " Regenerate"
msgstr " Wygeneruj ponownie"

#: src/window.py:376
msgid "Send a message..."
msgstr "Wyślij wiadomość..."

#: src/window.py:467
msgid "Explorer Tab"
msgstr "Zakładka eksploratora"

#: src/window.py:468
msgid "Terminal Tab"
msgstr "Zakładka terminala"

#: src/window.py:469
msgid "Browser Tab"
msgstr "Zakładka przeglądarki"

#: src/window.py:589
msgid "Ask about a website"
msgstr "Zapytaj o stronę internetową"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr ""
"Wpisz #https://strona.com na czacie, aby zapytać o informacje o stronie "
"internetowej"

#: src/window.py:590
msgid "Check out our Extensions!"
msgstr "Wypróbuj nasze rozszerzenia!"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "Mamy wiele rozszerzeń do różnych zastosowań. Wypróbuj je!"

#: src/window.py:591
msgid "Chat with documents!"
msgstr "Rozmawiaj z dokumentami!"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr ""
"Dodaj swoje dokumenty do folderu dokumentów i rozmawiaj, używając informacji "
"w nich zawartych!"

#: src/window.py:592
msgid "Surf the web!"
msgstr "Przeglądaj sieć!"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr ""
"Włącz wyszukiwanie w sieci, aby umożliwić modelowi przeglądanie Internetu i "
"dostarczanie aktualnych odpowiedzi"

#: src/window.py:593
msgid "Mini Window"
msgstr "Mini okno"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "Zadawaj pytania na bieżąco, używając trybu mini okna"

#: src/window.py:594
msgid "Text to Speech"
msgstr "Zamiana tekstu na mowę"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr "Newelle obsługuje zamianę tekstu na mowę! Włącz ją w ustawieniach"

#: src/window.py:595
msgid "Keyboard Shortcuts"
msgstr "Skróty klawiaturowe"

#: src/window.py:595
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "Kontroluj Newelle za pomocą skrótów klawiaturowych"

#: src/window.py:596
msgid "Prompt Control"
msgstr "Kontrola podpowiedzi"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr ""
"Newelle daje Ci 100% kontroli nad podpowiedziami. Dostosuj je do swoich "
"potrzeb."

#: src/window.py:597
msgid "Thread Editing"
msgstr "Edycja wątków"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "Sprawdź programy i procesy, które uruchamiasz z Newelle"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr "Programowalne podpowiedzi"

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr ""
"Możesz dodawać dynamiczne podpowiedzi do Newelle, z warunkami i "
"prawdopodobieństwami"

#: src/window.py:605
msgid "New Chat"
msgstr "Nowy czat"

#: src/window.py:623
msgid "Provider Errror"
msgstr "Błąd dostawcy"

#: src/window.py:646
msgid "Local Documents"
msgstr "Dokumenty lokalne"

#: src/window.py:650
msgid "Web search"
msgstr "Wyszukiwanie w sieci"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "Ten dostawca nie posiada listy modeli"

#: src/window.py:901
msgid " Models"
msgstr " Modele"

#: src/window.py:904
msgid "Search Models..."
msgstr "Szukaj modeli..."

#: src/window.py:1132
msgid "Create new profile"
msgstr "Utwórz nowy profil"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "Nie rozpoznano Twojego głosu"

#: src/window.py:1303
msgid "Images"
msgstr "Obrazy"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "Pliki obsługiwane przez model"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "Pliki obsługiwane przez RAG"

#: src/window.py:1333
msgid "Supported Files"
msgstr "Obsługiwane pliki"

#: src/window.py:1337
msgid "All Files"
msgstr "Wszystkie pliki"

#: src/window.py:1343
msgid "Attach file"
msgstr "Dołącz plik"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "Plik nie może zostać wysłany, dopóki program nie zakończy działania"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "Nie rozpoznano pliku"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "Nie możesz już kontynuować wiadomości."

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "Nie możesz już ponownie wygenerować wiadomości."

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "Czat został wyczyszczony"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "Wiadomość została anulowana i usunięta z historii"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr ""
"Wiadomość nie może zostać wysłana, dopóki program nie zakończy działania"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr "Nie możesz edytować wiadomości, gdy program jest uruchomiony."

#: src/window.py:3080
msgid "Prompt content"
msgstr "Treść podpowiedzi"

#: src/window.py:3339
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"Sieć neuronowa ma dostęp do Twojego komputera i wszelkich danych w tym "
"czacie oraz może uruchamiać polecenia. Bądź ostrożny/-a, nie ponosimy "
"odpowiedzialności za sieć neuronową. Nie udostępniaj żadnych poufnych "
"informacji."

#: src/window.py:3368
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"Sieć neuronowa ma dostęp do wszelkich danych w tym czacie. Bądź ostrożny/-a, "
"nie ponosimy odpowiedzialności za sieć neuronową. Nie udostępniaj żadnych "
"poufnych informacji."

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "Błędna ścieżka folderu"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "Wątek nie został ukończony, numer wątku: "

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "Nie udało się otworzyć folderu"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "Czat jest pusty"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr ""
"Trenuj Newelle, aby robił więcej dzięki niestandardowym rozszerzeniom i "
"nowym modułom SI, dając Twojemu botowi czatu nieograniczone możliwości."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "Bot czatu SI"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "Szybki wybór profilu"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
msgid "Message Editing"
msgstr "Edycja wiadomości"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "Ponad 10 standardowych dostawców SI"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "Poprawki błędów"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr ""
"Obsługa mini apek! Rozszerzenia mogą teraz pokazywać specjalne mini apki na "
"panelu bocznym"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr ""
"Dodano zintegrowaną mini apkę „przeglądarka”: przeglądaj sieć bezpośrednio w "
"Newelle i dołączaj strony internetowe"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr ""
"Usprawniony zintegrowany menedżer plików, obsługujący wiele operacji na "
"plikach"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr ""
"Zintegrowany edytor plików: edytuj pliki i bloki kodu bezpośrednio w Newelle"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr ""
"Zintegrowana mini apka „terminal”: otwieraj terminal bezpośrednio w Newelle"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr ""
"Programowalne podpowiedzi: dodawaj dynamiczne treści do podpowiedzi, z "
"warunkami i losowymi napisami"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr "Dodano możliwość ręcznej edycji nazw czatów"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
msgid "Minor bug fixes"
msgstr "Pomniejsze poprawki błędów"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr "Dodano obsługę wielu języków przez TTS Kokoro i Whisper.CPP"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr "Uruchamiaj strony HTML/CSS/JS bezpośrednio w programie"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr "Nowa animacja przy zmianie czatu"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "Drobne ulepszenia"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "Poprawiono wydajność czytania i ładowania lokalnych dokumentów"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "Dodano opcję wysyłania za pomocą CTRL+Enter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "Ulepszono bloki kodu"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
msgid "Fix Kokoro TTS"
msgstr "Naprawiono TTS Kokoro"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "Usunięto emotikony z TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "Ustawianie kluczy API jako pola hasła"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "Dodano obsługę myślenia Gemini"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "Zaktualizowane tłumaczenia"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "Dodano nowe funkcje"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr ""
"Czytanie stron internetowych i wyszukiwanie w sieci za pomocą SearXNG, "
"DuckDuckGo i Tavily"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "Ulepszono wyświetlanie LaTeX i zarządzanie dokumentami"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "Nowy widżet myślenia i obsługa OpenRouter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "Obsługa wizji Llama4 na Groq"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "Nowe tłumaczenia (tradycyjny chiński, bengalski, hindi)"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "Naprawiono wiele błędów, dodano kilka funkcji!"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "Obsługa nowych funkcji i poprawki błędów"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "Dodano wiele nowych funkcji i poprawek błędów"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "Dodano nowe funkcje i poprawki błędów"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr ""
"Zaktualizowano bibliotekę g4f o wersjonowanie, dodano instrukcje obsługi, "
"ulepszono przeglądanie rozszerzeń i poprawiono obsługę modeli."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"Wdrożono poprawki błędów i nowe funkcje. Zmodyfikowano architekturę "
"rozszerzeń, dodano nowe modele i wprowadzono obsługę wizji wraz z innymi "
"możliwościami."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "Stabilna wersja"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
msgid "Added extension"
msgstr "Dodano rozszerzenie"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
msgid "Blacklist of commands"
msgstr "Czarna lista poleceń"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "Regionalizacja"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "Przeprojektowanie"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "Qwersyk"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "Newelle: Twój zaawansowany bot czatu"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "si;asystent;czat;chatgpt;gpt;llm;ollama;"

#~ msgid "max Tokens"
#~ msgstr "maks. tokeny"

#~ msgid "Max tokens of the generated text"
#~ msgstr "Maksymalna liczba tokenów wygenerowanego tekstu"
