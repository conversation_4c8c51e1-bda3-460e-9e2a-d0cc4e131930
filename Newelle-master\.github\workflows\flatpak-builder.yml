on:
  push:
    branches: [ master ]
name: Flatpak Build
jobs:
  flatpak:
    name: "Flatpak"
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/flathub-infra/flatpak-github-actions:gnome-48
      options: --privileged
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - name: Flatpak build
        uses: flatpak/flatpak-github-actions/flatpak-builder@v6
        with:
          bundle: newelle.flatpak
          manifest-path: io.github.qwersyk.Newelle.json
          cache-key: flatpak-builder-${{ github.sha }}