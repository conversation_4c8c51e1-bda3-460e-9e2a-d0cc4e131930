[{"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/Inflector/Inflector-0.11.4.crate", "sha256": "fe438c63458706e03479442743baae6c88256498e6431708f6dfc520a26515d3", "dest": "cargo/vendor/Inflector-0.11.4"}, {"type": "inline", "contents": "{\"package\": \"fe438c63458706e03479442743baae6c88256498e6431708f6dfc520a26515d3\", \"files\": {}}", "dest": "cargo/vendor/Inflector-0.11.4", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/aliasable/aliasable-0.1.3.crate", "sha256": "250f629c0161ad8107cf89319e990051fae62832fd343083bea452d93e2205fd", "dest": "cargo/vendor/aliasable-0.1.3"}, {"type": "inline", "contents": "{\"package\": \"250f629c0161ad8107cf89319e990051fae62832fd343083bea452d93e2205fd\", \"files\": {}}", "dest": "cargo/vendor/aliasable-0.1.3", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/android_system_properties/android_system_properties-0.1.5.crate", "sha256": "819e7219dbd41043ac279b19830f2efc897156490d7fd6ea916720117ee66311", "dest": "cargo/vendor/android_system_properties-0.1.5"}, {"type": "inline", "contents": "{\"package\": \"819e7219dbd41043ac279b19830f2efc897156490d7fd6ea916720117ee66311\", \"files\": {}}", "dest": "cargo/vendor/android_system_properties-0.1.5", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/asn1/asn1-0.13.0.crate", "sha256": "2affba5e62ee09eeba078f01a00c4aed45ac4287e091298eccbb0d4802efbdc5", "dest": "cargo/vendor/asn1-0.13.0"}, {"type": "inline", "contents": "{\"package\": \"2affba5e62ee09eeba078f01a00c4aed45ac4287e091298eccbb0d4802efbdc5\", \"files\": {}}", "dest": "cargo/vendor/asn1-0.13.0", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/asn1_derive/asn1_derive-0.13.0.crate", "sha256": "bfab79c195875e5aef2bd20b4c8ed8d43ef9610bcffefbbcf66f88f555cc78af", "dest": "cargo/vendor/asn1_derive-0.13.0"}, {"type": "inline", "contents": "{\"package\": \"bfab79c195875e5aef2bd20b4c8ed8d43ef9610bcffefbbcf66f88f555cc78af\", \"files\": {}}", "dest": "cargo/vendor/asn1_derive-0.13.0", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/autocfg/autocfg-1.1.0.crate", "sha256": "d468802bab17cbc0cc575e9b053f41e72aa36bfa6b7f55e3529ffa43161b97fa", "dest": "cargo/vendor/autocfg-1.1.0"}, {"type": "inline", "contents": "{\"package\": \"d468802bab17cbc0cc575e9b053f41e72aa36bfa6b7f55e3529ffa43161b97fa\", \"files\": {}}", "dest": "cargo/vendor/autocfg-1.1.0", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/base64/base64-0.13.1.crate", "sha256": "9e1b586273c5702936fe7b7d6896644d8be71e6314cfe09d3167c95f712589e8", "dest": "cargo/vendor/base64-0.13.1"}, {"type": "inline", "contents": "{\"package\": \"9e1b586273c5702936fe7b7d6896644d8be71e6314cfe09d3167c95f712589e8\", \"files\": {}}", "dest": "cargo/vendor/base64-0.13.1", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/bitflags/bitflags-1.3.2.crate", "sha256": "bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a", "dest": "cargo/vendor/bitflags-1.3.2"}, {"type": "inline", "contents": "{\"package\": \"bef38d45163c2f1dde094a7dfd33ccf595c92905c8f8f4fdc18d06fb1037718a\", \"files\": {}}", "dest": "cargo/vendor/bitflags-1.3.2", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/bumpalo/bumpalo-3.10.0.crate", "sha256": "37ccbd214614c6783386c1af30caf03192f17891059cecc394b4fb119e363de3", "dest": "cargo/vendor/bumpalo-3.10.0"}, {"type": "inline", "contents": "{\"package\": \"37ccbd214614c6783386c1af30caf03192f17891059cecc394b4fb119e363de3\", \"files\": {}}", "dest": "cargo/vendor/bumpalo-3.10.0", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/cc/cc-1.0.79.crate", "sha256": "50d30906286121d95be3d479533b458f87493b30a4b5f79a607db8f5d11aa91f", "dest": "cargo/vendor/cc-1.0.79"}, {"type": "inline", "contents": "{\"package\": \"50d30906286121d95be3d479533b458f87493b30a4b5f79a607db8f5d11aa91f\", \"files\": {}}", "dest": "cargo/vendor/cc-1.0.79", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/cfg-if/cfg-if-1.0.0.crate", "sha256": "baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd", "dest": "cargo/vendor/cfg-if-1.0.0"}, {"type": "inline", "contents": "{\"package\": \"baf1de4339761588bc0619e3cbc0120ee582ebb74b53b4efbf79117bd2da40fd\", \"files\": {}}", "dest": "cargo/vendor/cfg-if-1.0.0", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/chrono/chrono-0.4.24.crate", "sha256": "4e3c5919066adf22df73762e50cffcde3a758f2a848b113b586d1f86728b673b", "dest": "cargo/vendor/chrono-0.4.24"}, {"type": "inline", "contents": "{\"package\": \"4e3c5919066adf22df73762e50cffcde3a758f2a848b113b586d1f86728b673b\", \"files\": {}}", "dest": "cargo/vendor/chrono-0.4.24", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/codespan-reporting/codespan-reporting-0.11.1.crate", "sha256": "3538270d33cc669650c4b093848450d380def10c331d38c768e34cac80576e6e", "dest": "cargo/vendor/codespan-reporting-0.11.1"}, {"type": "inline", "contents": "{\"package\": \"3538270d33cc669650c4b093848450d380def10c331d38c768e34cac80576e6e\", \"files\": {}}", "dest": "cargo/vendor/codespan-reporting-0.11.1", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/core-foundation-sys/core-foundation-sys-0.8.3.crate", "sha256": "5827cebf4670468b8772dd191856768aedcb1b0278a04f989f7766351917b9dc", "dest": "cargo/vendor/core-foundation-sys-0.8.3"}, {"type": "inline", "contents": "{\"package\": \"5827cebf4670468b8772dd191856768aedcb1b0278a04f989f7766351917b9dc\", \"files\": {}}", "dest": "cargo/vendor/core-foundation-sys-0.8.3", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/cxx/cxx-1.0.86.crate", "sha256": "51d1075c37807dcf850c379432f0df05ba52cc30f279c5cfc43cc221ce7f8579", "dest": "cargo/vendor/cxx-1.0.86"}, {"type": "inline", "contents": "{\"package\": \"51d1075c37807dcf850c379432f0df05ba52cc30f279c5cfc43cc221ce7f8579\", \"files\": {}}", "dest": "cargo/vendor/cxx-1.0.86", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/cxx-build/cxx-build-1.0.86.crate", "sha256": "5044281f61b27bc598f2f6647d480aed48d2bf52d6eb0b627d84c0361b17aa70", "dest": "cargo/vendor/cxx-build-1.0.86"}, {"type": "inline", "contents": "{\"package\": \"5044281f61b27bc598f2f6647d480aed48d2bf52d6eb0b627d84c0361b17aa70\", \"files\": {}}", "dest": "cargo/vendor/cxx-build-1.0.86", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/cxxbridge-flags/cxxbridge-flags-1.0.86.crate", "sha256": "61b50bc93ba22c27b0d31128d2d130a0a6b3d267ae27ef7e4fae2167dfe8781c", "dest": "cargo/vendor/cxxbridge-flags-1.0.86"}, {"type": "inline", "contents": "{\"package\": \"61b50bc93ba22c27b0d31128d2d130a0a6b3d267ae27ef7e4fae2167dfe8781c\", \"files\": {}}", "dest": "cargo/vendor/cxxbridge-flags-1.0.86", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/cxxbridge-macro/cxxbridge-macro-1.0.86.crate", "sha256": "39e61fda7e62115119469c7b3591fd913ecca96fb766cfd3f2e2502ab7bc87a5", "dest": "cargo/vendor/cxxbridge-macro-1.0.86"}, {"type": "inline", "contents": "{\"package\": \"39e61fda7e62115119469c7b3591fd913ecca96fb766cfd3f2e2502ab7bc87a5\", \"files\": {}}", "dest": "cargo/vendor/cxxbridge-macro-1.0.86", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/foreign-types/foreign-types-0.3.2.crate", "sha256": "f6f339eb8adc052cd2ca78910fda869aefa38d22d5cb648e6485e4d3fc06f3b1", "dest": "cargo/vendor/foreign-types-0.3.2"}, {"type": "inline", "contents": "{\"package\": \"f6f339eb8adc052cd2ca78910fda869aefa38d22d5cb648e6485e4d3fc06f3b1\", \"files\": {}}", "dest": "cargo/vendor/foreign-types-0.3.2", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/foreign-types-shared/foreign-types-shared-0.1.1.crate", "sha256": "00b0228411908ca8685dba7fc2cdd70ec9990a6e753e89b6ac91a84c40fbaf4b", "dest": "cargo/vendor/foreign-types-shared-0.1.1"}, {"type": "inline", "contents": "{\"package\": \"00b0228411908ca8685dba7fc2cdd70ec9990a6e753e89b6ac91a84c40fbaf4b\", \"files\": {}}", "dest": "cargo/vendor/foreign-types-shared-0.1.1", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/iana-time-zone/iana-time-zone-0.1.54.crate", "sha256": "0c17cc76786e99f8d2f055c11159e7f0091c42474dcc3189fbab96072e873e6d", "dest": "cargo/vendor/iana-time-zone-0.1.54"}, {"type": "inline", "contents": "{\"package\": \"0c17cc76786e99f8d2f055c11159e7f0091c42474dcc3189fbab96072e873e6d\", \"files\": {}}", "dest": "cargo/vendor/iana-time-zone-0.1.54", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/iana-time-zone-haiku/iana-time-zone-haiku-0.1.1.crate", "sha256": "0703ae284fc167426161c2e3f1da3ea71d94b21bedbcc9494e92b28e334e3dca", "dest": "cargo/vendor/iana-time-zone-haiku-0.1.1"}, {"type": "inline", "contents": "{\"package\": \"0703ae284fc167426161c2e3f1da3ea71d94b21bedbcc9494e92b28e334e3dca\", \"files\": {}}", "dest": "cargo/vendor/iana-time-zone-haiku-0.1.1", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/indoc/indoc-0.3.6.crate", "sha256": "47741a8bc60fb26eb8d6e0238bbb26d8575ff623fdc97b1a2c00c050b9684ed8", "dest": "cargo/vendor/indoc-0.3.6"}, {"type": "inline", "contents": "{\"package\": \"47741a8bc60fb26eb8d6e0238bbb26d8575ff623fdc97b1a2c00c050b9684ed8\", \"files\": {}}", "dest": "cargo/vendor/indoc-0.3.6", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/indoc-impl/indoc-impl-0.3.6.crate", "sha256": "ce046d161f000fffde5f432a0d034d0341dc152643b2598ed5bfce44c4f3a8f0", "dest": "cargo/vendor/indoc-impl-0.3.6"}, {"type": "inline", "contents": "{\"package\": \"ce046d161f000fffde5f432a0d034d0341dc152643b2598ed5bfce44c4f3a8f0\", \"files\": {}}", "dest": "cargo/vendor/indoc-impl-0.3.6", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/instant/instant-0.1.12.crate", "sha256": "7a5bbe824c507c5da5956355e86a746d82e0e1464f65d862cc5e71da70e94b2c", "dest": "cargo/vendor/instant-0.1.12"}, {"type": "inline", "contents": "{\"package\": \"7a5bbe824c507c5da5956355e86a746d82e0e1464f65d862cc5e71da70e94b2c\", \"files\": {}}", "dest": "cargo/vendor/instant-0.1.12", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/js-sys/js-sys-0.3.61.crate", "sha256": "445dde2150c55e483f3d8416706b97ec8e8237c307e5b7b4b8dd15e6af2a0730", "dest": "cargo/vendor/js-sys-0.3.61"}, {"type": "inline", "contents": "{\"package\": \"445dde2150c55e483f3d8416706b97ec8e8237c307e5b7b4b8dd15e6af2a0730\", \"files\": {}}", "dest": "cargo/vendor/js-sys-0.3.61", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/libc/libc-0.2.140.crate", "sha256": "99227334921fae1a979cf0bfdfcc6b3e5ce376ef57e16fb6fb3ea2ed6095f80c", "dest": "cargo/vendor/libc-0.2.140"}, {"type": "inline", "contents": "{\"package\": \"99227334921fae1a979cf0bfdfcc6b3e5ce376ef57e16fb6fb3ea2ed6095f80c\", \"files\": {}}", "dest": "cargo/vendor/libc-0.2.140", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/link-cplusplus/link-cplusplus-1.0.8.crate", "sha256": "ecd207c9c713c34f95a097a5b029ac2ce6010530c7b49d7fea24d977dede04f5", "dest": "cargo/vendor/link-cplusplus-1.0.8"}, {"type": "inline", "contents": "{\"package\": \"ecd207c9c713c34f95a097a5b029ac2ce6010530c7b49d7fea24d977dede04f5\", \"files\": {}}", "dest": "cargo/vendor/link-cplusplus-1.0.8", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/lock_api/lock_api-0.4.9.crate", "sha256": "435011366fe56583b16cf956f9df0095b405b82d76425bc8981c0e22e60ec4df", "dest": "cargo/vendor/lock_api-0.4.9"}, {"type": "inline", "contents": "{\"package\": \"435011366fe56583b16cf956f9df0095b405b82d76425bc8981c0e22e60ec4df\", \"files\": {}}", "dest": "cargo/vendor/lock_api-0.4.9", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/log/log-0.4.17.crate", "sha256": "abb12e687cfb44aa40f41fc3978ef76448f9b6038cad6aef4259d3c095a2382e", "dest": "cargo/vendor/log-0.4.17"}, {"type": "inline", "contents": "{\"package\": \"abb12e687cfb44aa40f41fc3978ef76448f9b6038cad6aef4259d3c095a2382e\", \"files\": {}}", "dest": "cargo/vendor/log-0.4.17", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/num-integer/num-integer-0.1.45.crate", "sha256": "225d3389fb3509a24c93f5c29eb6bde2586b98d9f016636dff58d7c6f7569cd9", "dest": "cargo/vendor/num-integer-0.1.45"}, {"type": "inline", "contents": "{\"package\": \"225d3389fb3509a24c93f5c29eb6bde2586b98d9f016636dff58d7c6f7569cd9\", \"files\": {}}", "dest": "cargo/vendor/num-integer-0.1.45", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/num-traits/num-traits-0.2.15.crate", "sha256": "578ede34cf02f8924ab9447f50c28075b4d3e5b269972345e7e0372b38c6cdcd", "dest": "cargo/vendor/num-traits-0.2.15"}, {"type": "inline", "contents": "{\"package\": \"578ede34cf02f8924ab9447f50c28075b4d3e5b269972345e7e0372b38c6cdcd\", \"files\": {}}", "dest": "cargo/vendor/num-traits-0.2.15", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/once_cell/once_cell-1.14.0.crate", "sha256": "2f7254b99e31cad77da24b08ebf628882739a608578bb1bcdfc1f9c21260d7c0", "dest": "cargo/vendor/once_cell-1.14.0"}, {"type": "inline", "contents": "{\"package\": \"2f7254b99e31cad77da24b08ebf628882739a608578bb1bcdfc1f9c21260d7c0\", \"files\": {}}", "dest": "cargo/vendor/once_cell-1.14.0", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/openssl/openssl-0.10.48.crate", "sha256": "518915b97df115dd36109bfa429a48b8f737bd05508cf9588977b599648926d2", "dest": "cargo/vendor/openssl-0.10.48"}, {"type": "inline", "contents": "{\"package\": \"518915b97df115dd36109bfa429a48b8f737bd05508cf9588977b599648926d2\", \"files\": {}}", "dest": "cargo/vendor/openssl-0.10.48", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/openssl-macros/openssl-macros-0.1.0.crate", "sha256": "b501e44f11665960c7e7fcf062c7d96a14ade4aa98116c004b2e37b5be7d736c", "dest": "cargo/vendor/openssl-macros-0.1.0"}, {"type": "inline", "contents": "{\"package\": \"b501e44f11665960c7e7fcf062c7d96a14ade4aa98116c004b2e37b5be7d736c\", \"files\": {}}", "dest": "cargo/vendor/openssl-macros-0.1.0", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/openssl-sys/openssl-sys-0.9.83.crate", "sha256": "666416d899cf077260dac8698d60a60b435a46d57e82acb1be3d0dad87284e5b", "dest": "cargo/vendor/openssl-sys-0.9.83"}, {"type": "inline", "contents": "{\"package\": \"666416d899cf077260dac8698d60a60b435a46d57e82acb1be3d0dad87284e5b\", \"files\": {}}", "dest": "cargo/vendor/openssl-sys-0.9.83", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/ouroboros/ouroboros-0.15.6.crate", "sha256": "e1358bd1558bd2a083fed428ffeda486fbfb323e698cdda7794259d592ca72db", "dest": "cargo/vendor/ouroboros-0.15.6"}, {"type": "inline", "contents": "{\"package\": \"e1358bd1558bd2a083fed428ffeda486fbfb323e698cdda7794259d592ca72db\", \"files\": {}}", "dest": "cargo/vendor/ouroboros-0.15.6", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/ouroboros_macro/ouroboros_macro-0.15.6.crate", "sha256": "5f7d21ccd03305a674437ee1248f3ab5d4b1db095cf1caf49f1713ddf61956b7", "dest": "cargo/vendor/ouroboros_macro-0.15.6"}, {"type": "inline", "contents": "{\"package\": \"5f7d21ccd03305a674437ee1248f3ab5d4b1db095cf1caf49f1713ddf61956b7\", \"files\": {}}", "dest": "cargo/vendor/ouroboros_macro-0.15.6", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/parking_lot/parking_lot-0.11.2.crate", "sha256": "7d17b78036a60663b797adeaee46f5c9dfebb86948d1255007a1d6be0271ff99", "dest": "cargo/vendor/parking_lot-0.11.2"}, {"type": "inline", "contents": "{\"package\": \"7d17b78036a60663b797adeaee46f5c9dfebb86948d1255007a1d6be0271ff99\", \"files\": {}}", "dest": "cargo/vendor/parking_lot-0.11.2", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/parking_lot_core/parking_lot_core-0.8.6.crate", "sha256": "60a2cfe6f0ad2bfc16aefa463b497d5c7a5ecd44a23efa72aa342d90177356dc", "dest": "cargo/vendor/parking_lot_core-0.8.6"}, {"type": "inline", "contents": "{\"package\": \"60a2cfe6f0ad2bfc16aefa463b497d5c7a5ecd44a23efa72aa342d90177356dc\", \"files\": {}}", "dest": "cargo/vendor/parking_lot_core-0.8.6", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/paste/paste-0.1.18.crate", "sha256": "45ca20c77d80be666aef2b45486da86238fabe33e38306bd3118fe4af33fa880", "dest": "cargo/vendor/paste-0.1.18"}, {"type": "inline", "contents": "{\"package\": \"45ca20c77d80be666aef2b45486da86238fabe33e38306bd3118fe4af33fa880\", \"files\": {}}", "dest": "cargo/vendor/paste-0.1.18", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/paste-impl/paste-impl-0.1.18.crate", "sha256": "d95a7db200b97ef370c8e6de0088252f7e0dfff7d047a28528e47456c0fc98b6", "dest": "cargo/vendor/paste-impl-0.1.18"}, {"type": "inline", "contents": "{\"package\": \"d95a7db200b97ef370c8e6de0088252f7e0dfff7d047a28528e47456c0fc98b6\", \"files\": {}}", "dest": "cargo/vendor/paste-impl-0.1.18", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/pem/pem-1.1.1.crate", "sha256": "a8835c273a76a90455d7344889b0964598e3316e2a79ede8e36f16bdcf2228b8", "dest": "cargo/vendor/pem-1.1.1"}, {"type": "inline", "contents": "{\"package\": \"a8835c273a76a90455d7344889b0964598e3316e2a79ede8e36f16bdcf2228b8\", \"files\": {}}", "dest": "cargo/vendor/pem-1.1.1", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/pkg-config/pkg-config-0.3.26.crate", "sha256": "6ac9a59f73473f1b8d852421e59e64809f025994837ef743615c6d0c5b305160", "dest": "cargo/vendor/pkg-config-0.3.26"}, {"type": "inline", "contents": "{\"package\": \"6ac9a59f73473f1b8d852421e59e64809f025994837ef743615c6d0c5b305160\", \"files\": {}}", "dest": "cargo/vendor/pkg-config-0.3.26", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/proc-macro-error/proc-macro-error-1.0.4.crate", "sha256": "da25490ff9892aab3fcf7c36f08cfb902dd3e71ca0f9f9517bea02a73a5ce38c", "dest": "cargo/vendor/proc-macro-error-1.0.4"}, {"type": "inline", "contents": "{\"package\": \"da25490ff9892aab3fcf7c36f08cfb902dd3e71ca0f9f9517bea02a73a5ce38c\", \"files\": {}}", "dest": "cargo/vendor/proc-macro-error-1.0.4", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/proc-macro-error-attr/proc-macro-error-attr-1.0.4.crate", "sha256": "a1be40180e52ecc98ad80b184934baf3d0d29f979574e439af5a55274b35f869", "dest": "cargo/vendor/proc-macro-error-attr-1.0.4"}, {"type": "inline", "contents": "{\"package\": \"a1be40180e52ecc98ad80b184934baf3d0d29f979574e439af5a55274b35f869\", \"files\": {}}", "dest": "cargo/vendor/proc-macro-error-attr-1.0.4", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/proc-macro-hack/proc-macro-hack-0.5.20+deprecated.crate", "sha256": "dc375e1527247fe1a97d8b7156678dfe7c1af2fc075c9a4db3690ecd2a148068", "dest": "cargo/vendor/proc-macro-hack-0.5.20+deprecated"}, {"type": "inline", "contents": "{\"package\": \"dc375e1527247fe1a97d8b7156678dfe7c1af2fc075c9a4db3690ecd2a148068\", \"files\": {}}", "dest": "cargo/vendor/proc-macro-hack-0.5.20+deprecated", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/proc-macro2/proc-macro2-1.0.53.crate", "sha256": "ba466839c78239c09faf015484e5cc04860f88242cff4d03eb038f04b4699b73", "dest": "cargo/vendor/proc-macro2-1.0.53"}, {"type": "inline", "contents": "{\"package\": \"ba466839c78239c09faf015484e5cc04860f88242cff4d03eb038f04b4699b73\", \"files\": {}}", "dest": "cargo/vendor/proc-macro2-1.0.53", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/pyo3/pyo3-0.15.2.crate", "sha256": "d41d50a7271e08c7c8a54cd24af5d62f73ee3a6f6a314215281ebdec421d5752", "dest": "cargo/vendor/pyo3-0.15.2"}, {"type": "inline", "contents": "{\"package\": \"d41d50a7271e08c7c8a54cd24af5d62f73ee3a6f6a314215281ebdec421d5752\", \"files\": {}}", "dest": "cargo/vendor/pyo3-0.15.2", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/pyo3-build-config/pyo3-build-config-0.15.2.crate", "sha256": "779239fc40b8e18bc8416d3a37d280ca9b9fb04bda54b98037bb6748595c2410", "dest": "cargo/vendor/pyo3-build-config-0.15.2"}, {"type": "inline", "contents": "{\"package\": \"779239fc40b8e18bc8416d3a37d280ca9b9fb04bda54b98037bb6748595c2410\", \"files\": {}}", "dest": "cargo/vendor/pyo3-build-config-0.15.2", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/pyo3-macros/pyo3-macros-0.15.2.crate", "sha256": "00b247e8c664be87998d8628e86f282c25066165f1f8dda66100c48202fdb93a", "dest": "cargo/vendor/pyo3-macros-0.15.2"}, {"type": "inline", "contents": "{\"package\": \"00b247e8c664be87998d8628e86f282c25066165f1f8dda66100c48202fdb93a\", \"files\": {}}", "dest": "cargo/vendor/pyo3-macros-0.15.2", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/pyo3-macros-backend/pyo3-macros-backend-0.15.2.crate", "sha256": "5a8c2812c412e00e641d99eeb79dd478317d981d938aa60325dfa7157b607095", "dest": "cargo/vendor/pyo3-macros-backend-0.15.2"}, {"type": "inline", "contents": "{\"package\": \"5a8c2812c412e00e641d99eeb79dd478317d981d938aa60325dfa7157b607095\", \"files\": {}}", "dest": "cargo/vendor/pyo3-macros-backend-0.15.2", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/quote/quote-1.0.26.crate", "sha256": "4424af4bf778aae2051a77b60283332f386554255d722233d09fbfc7e30da2fc", "dest": "cargo/vendor/quote-1.0.26"}, {"type": "inline", "contents": "{\"package\": \"4424af4bf778aae2051a77b60283332f386554255d722233d09fbfc7e30da2fc\", \"files\": {}}", "dest": "cargo/vendor/quote-1.0.26", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/redox_syscall/redox_syscall-0.2.16.crate", "sha256": "fb5a58c1855b4b6819d59012155603f0b22ad30cad752600aadfcb695265519a", "dest": "cargo/vendor/redox_syscall-0.2.16"}, {"type": "inline", "contents": "{\"package\": \"fb5a58c1855b4b6819d59012155603f0b22ad30cad752600aadfcb695265519a\", \"files\": {}}", "dest": "cargo/vendor/redox_syscall-0.2.16", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/scopeguard/scopeguard-1.1.0.crate", "sha256": "d29ab0c6d3fc0ee92fe66e2d99f700eab17a8d57d1c1d3b748380fb20baa78cd", "dest": "cargo/vendor/scopeguard-1.1.0"}, {"type": "inline", "contents": "{\"package\": \"d29ab0c6d3fc0ee92fe66e2d99f700eab17a8d57d1c1d3b748380fb20baa78cd\", \"files\": {}}", "dest": "cargo/vendor/scopeguard-1.1.0", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/scratch/scratch-1.0.5.crate", "sha256": "1792db035ce95be60c3f8853017b3999209281c24e2ba5bc8e59bf97a0c590c1", "dest": "cargo/vendor/scratch-1.0.5"}, {"type": "inline", "contents": "{\"package\": \"1792db035ce95be60c3f8853017b3999209281c24e2ba5bc8e59bf97a0c590c1\", \"files\": {}}", "dest": "cargo/vendor/scratch-1.0.5", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/smallvec/smallvec-1.10.0.crate", "sha256": "a507befe795404456341dfab10cef66ead4c041f62b8b11bbb92bffe5d0953e0", "dest": "cargo/vendor/smallvec-1.10.0"}, {"type": "inline", "contents": "{\"package\": \"a507befe795404456341dfab10cef66ead4c041f62b8b11bbb92bffe5d0953e0\", \"files\": {}}", "dest": "cargo/vendor/smallvec-1.10.0", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/syn/syn-1.0.109.crate", "sha256": "72b64191b275b66ffe2469e8af2c1cfe3bafa67b529ead792a6d0160888b4237", "dest": "cargo/vendor/syn-1.0.109"}, {"type": "inline", "contents": "{\"package\": \"72b64191b275b66ffe2469e8af2c1cfe3bafa67b529ead792a6d0160888b4237\", \"files\": {}}", "dest": "cargo/vendor/syn-1.0.109", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/termcolor/termcolor-1.2.0.crate", "sha256": "be55cf8942feac5c765c2c993422806843c9a9a45d4d5c407ad6dd2ea95eb9b6", "dest": "cargo/vendor/termcolor-1.2.0"}, {"type": "inline", "contents": "{\"package\": \"be55cf8942feac5c765c2c993422806843c9a9a45d4d5c407ad6dd2ea95eb9b6\", \"files\": {}}", "dest": "cargo/vendor/termcolor-1.2.0", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/unicode-ident/unicode-ident-1.0.8.crate", "sha256": "e5464a87b239f13a63a501f2701565754bae92d243d4bb7eb12f6d57d2269bf4", "dest": "cargo/vendor/unicode-ident-1.0.8"}, {"type": "inline", "contents": "{\"package\": \"e5464a87b239f13a63a501f2701565754bae92d243d4bb7eb12f6d57d2269bf4\", \"files\": {}}", "dest": "cargo/vendor/unicode-ident-1.0.8", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/unicode-width/unicode-width-0.1.10.crate", "sha256": "c0edd1e5b14653f783770bce4a4dabb4a5108a5370a5f5d8cfe8710c361f6c8b", "dest": "cargo/vendor/unicode-width-0.1.10"}, {"type": "inline", "contents": "{\"package\": \"c0edd1e5b14653f783770bce4a4dabb4a5108a5370a5f5d8cfe8710c361f6c8b\", \"files\": {}}", "dest": "cargo/vendor/unicode-width-0.1.10", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/unindent/unindent-0.1.11.crate", "sha256": "e1766d682d402817b5ac4490b3c3002d91dfa0d22812f341609f97b08757359c", "dest": "cargo/vendor/unindent-0.1.11"}, {"type": "inline", "contents": "{\"package\": \"e1766d682d402817b5ac4490b3c3002d91dfa0d22812f341609f97b08757359c\", \"files\": {}}", "dest": "cargo/vendor/unindent-0.1.11", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/vcpkg/vcpkg-0.2.15.crate", "sha256": "accd4ea62f7bb7a82fe23066fb0957d48ef677f6eeb8215f372f52e48bb32426", "dest": "cargo/vendor/vcpkg-0.2.15"}, {"type": "inline", "contents": "{\"package\": \"accd4ea62f7bb7a82fe23066fb0957d48ef677f6eeb8215f372f52e48bb32426\", \"files\": {}}", "dest": "cargo/vendor/vcpkg-0.2.15", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/version_check/version_check-0.9.4.crate", "sha256": "49874b5167b65d7193b8aba1567f5c7d93d001cafc34600cee003eda787e483f", "dest": "cargo/vendor/version_check-0.9.4"}, {"type": "inline", "contents": "{\"package\": \"49874b5167b65d7193b8aba1567f5c7d93d001cafc34600cee003eda787e483f\", \"files\": {}}", "dest": "cargo/vendor/version_check-0.9.4", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/wasm-bindgen/wasm-bindgen-0.2.84.crate", "sha256": "31f8dcbc21f30d9b8f2ea926ecb58f6b91192c17e9d33594b3df58b2007ca53b", "dest": "cargo/vendor/wasm-bindgen-0.2.84"}, {"type": "inline", "contents": "{\"package\": \"31f8dcbc21f30d9b8f2ea926ecb58f6b91192c17e9d33594b3df58b2007ca53b\", \"files\": {}}", "dest": "cargo/vendor/wasm-bindgen-0.2.84", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/wasm-bindgen-backend/wasm-bindgen-backend-0.2.84.crate", "sha256": "95ce90fd5bcc06af55a641a86428ee4229e44e07033963a2290a8e241607ccb9", "dest": "cargo/vendor/wasm-bindgen-backend-0.2.84"}, {"type": "inline", "contents": "{\"package\": \"95ce90fd5bcc06af55a641a86428ee4229e44e07033963a2290a8e241607ccb9\", \"files\": {}}", "dest": "cargo/vendor/wasm-bindgen-backend-0.2.84", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/wasm-bindgen-macro/wasm-bindgen-macro-0.2.84.crate", "sha256": "4c21f77c0bedc37fd5dc21f897894a5ca01e7bb159884559461862ae90c0b4c5", "dest": "cargo/vendor/wasm-bindgen-macro-0.2.84"}, {"type": "inline", "contents": "{\"package\": \"4c21f77c0bedc37fd5dc21f897894a5ca01e7bb159884559461862ae90c0b4c5\", \"files\": {}}", "dest": "cargo/vendor/wasm-bindgen-macro-0.2.84", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/wasm-bindgen-macro-support/wasm-bindgen-macro-support-0.2.84.crate", "sha256": "2aff81306fcac3c7515ad4e177f521b5c9a15f2b08f4e32d823066102f35a5f6", "dest": "cargo/vendor/wasm-bindgen-macro-support-0.2.84"}, {"type": "inline", "contents": "{\"package\": \"2aff81306fcac3c7515ad4e177f521b5c9a15f2b08f4e32d823066102f35a5f6\", \"files\": {}}", "dest": "cargo/vendor/wasm-bindgen-macro-support-0.2.84", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/wasm-bindgen-shared/wasm-bindgen-shared-0.2.84.crate", "sha256": "0046fef7e28c3804e5e38bfa31ea2a0f73905319b677e57ebe37e49358989b5d", "dest": "cargo/vendor/wasm-bindgen-shared-0.2.84"}, {"type": "inline", "contents": "{\"package\": \"0046fef7e28c3804e5e38bfa31ea2a0f73905319b677e57ebe37e49358989b5d\", \"files\": {}}", "dest": "cargo/vendor/wasm-bindgen-shared-0.2.84", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/winapi/winapi-0.3.9.crate", "sha256": "5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419", "dest": "cargo/vendor/winapi-0.3.9"}, {"type": "inline", "contents": "{\"package\": \"5c839a674fcd7a98952e593242ea400abe93992746761e38641405d28b00f419\", \"files\": {}}", "dest": "cargo/vendor/winapi-0.3.9", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/winapi-i686-pc-windows-gnu/winapi-i686-pc-windows-gnu-0.4.0.crate", "sha256": "ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6", "dest": "cargo/vendor/winapi-i686-pc-windows-gnu-0.4.0"}, {"type": "inline", "contents": "{\"package\": \"ac3b87c63620426dd9b991e5ce0329eff545bccbbb34f3be09ff6fb6ab51b7b6\", \"files\": {}}", "dest": "cargo/vendor/winapi-i686-pc-windows-gnu-0.4.0", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/winapi-util/winapi-util-0.1.5.crate", "sha256": "70ec6ce85bb158151cae5e5c87f95a8e97d2c0c4b001223f33a334e3ce5de178", "dest": "cargo/vendor/winapi-util-0.1.5"}, {"type": "inline", "contents": "{\"package\": \"70ec6ce85bb158151cae5e5c87f95a8e97d2c0c4b001223f33a334e3ce5de178\", \"files\": {}}", "dest": "cargo/vendor/winapi-util-0.1.5", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/winapi-x86_64-pc-windows-gnu/winapi-x86_64-pc-windows-gnu-0.4.0.crate", "sha256": "712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f", "dest": "cargo/vendor/winapi-x86_64-pc-windows-gnu-0.4.0"}, {"type": "inline", "contents": "{\"package\": \"712e227841d057c1ee1cd2fb22fa7e5a5461ae8e48fa2ca79ec42cfc1931183f\", \"files\": {}}", "dest": "cargo/vendor/winapi-x86_64-pc-windows-gnu-0.4.0", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/windows/windows-0.46.0.crate", "sha256": "cdacb41e6a96a052c6cb63a144f24900236121c6f63f4f8219fef5977ecb0c25", "dest": "cargo/vendor/windows-0.46.0"}, {"type": "inline", "contents": "{\"package\": \"cdacb41e6a96a052c6cb63a144f24900236121c6f63f4f8219fef5977ecb0c25\", \"files\": {}}", "dest": "cargo/vendor/windows-0.46.0", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/windows-targets/windows-targets-0.42.2.crate", "sha256": "8e5180c00cd44c9b1c88adb3693291f1cd93605ded80c250a75d472756b4d071", "dest": "cargo/vendor/windows-targets-0.42.2"}, {"type": "inline", "contents": "{\"package\": \"8e5180c00cd44c9b1c88adb3693291f1cd93605ded80c250a75d472756b4d071\", \"files\": {}}", "dest": "cargo/vendor/windows-targets-0.42.2", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/windows_aarch64_gnullvm/windows_aarch64_gnullvm-0.42.2.crate", "sha256": "597a5118570b68bc08d8d59125332c54f1ba9d9adeedeef5b99b02ba2b0698f8", "dest": "cargo/vendor/windows_aarch64_gnullvm-0.42.2"}, {"type": "inline", "contents": "{\"package\": \"597a5118570b68bc08d8d59125332c54f1ba9d9adeedeef5b99b02ba2b0698f8\", \"files\": {}}", "dest": "cargo/vendor/windows_aarch64_gnullvm-0.42.2", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/windows_aarch64_msvc/windows_aarch64_msvc-0.42.2.crate", "sha256": "e08e8864a60f06ef0d0ff4ba04124db8b0fb3be5776a5cd47641e942e58c4d43", "dest": "cargo/vendor/windows_aarch64_msvc-0.42.2"}, {"type": "inline", "contents": "{\"package\": \"e08e8864a60f06ef0d0ff4ba04124db8b0fb3be5776a5cd47641e942e58c4d43\", \"files\": {}}", "dest": "cargo/vendor/windows_aarch64_msvc-0.42.2", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/windows_i686_gnu/windows_i686_gnu-0.42.2.crate", "sha256": "c61d927d8da41da96a81f029489353e68739737d3beca43145c8afec9a31a84f", "dest": "cargo/vendor/windows_i686_gnu-0.42.2"}, {"type": "inline", "contents": "{\"package\": \"c61d927d8da41da96a81f029489353e68739737d3beca43145c8afec9a31a84f\", \"files\": {}}", "dest": "cargo/vendor/windows_i686_gnu-0.42.2", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/windows_i686_msvc/windows_i686_msvc-0.42.2.crate", "sha256": "44d840b6ec649f480a41c8d80f9c65108b92d89345dd94027bfe06ac444d1060", "dest": "cargo/vendor/windows_i686_msvc-0.42.2"}, {"type": "inline", "contents": "{\"package\": \"44d840b6ec649f480a41c8d80f9c65108b92d89345dd94027bfe06ac444d1060\", \"files\": {}}", "dest": "cargo/vendor/windows_i686_msvc-0.42.2", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/windows_x86_64_gnu/windows_x86_64_gnu-0.42.2.crate", "sha256": "8de912b8b8feb55c064867cf047dda097f92d51efad5b491dfb98f6bbb70cb36", "dest": "cargo/vendor/windows_x86_64_gnu-0.42.2"}, {"type": "inline", "contents": "{\"package\": \"8de912b8b8feb55c064867cf047dda097f92d51efad5b491dfb98f6bbb70cb36\", \"files\": {}}", "dest": "cargo/vendor/windows_x86_64_gnu-0.42.2", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/windows_x86_64_gnullvm/windows_x86_64_gnullvm-0.42.2.crate", "sha256": "26d41b46a36d453748aedef1486d5c7a85db22e56aff34643984ea85514e94a3", "dest": "cargo/vendor/windows_x86_64_gnullvm-0.42.2"}, {"type": "inline", "contents": "{\"package\": \"26d41b46a36d453748aedef1486d5c7a85db22e56aff34643984ea85514e94a3\", \"files\": {}}", "dest": "cargo/vendor/windows_x86_64_gnullvm-0.42.2", "dest-filename": ".cargo-checksum.json"}, {"type": "archive", "archive-type": "tar-gzip", "url": "https://static.crates.io/crates/windows_x86_64_msvc/windows_x86_64_msvc-0.42.2.crate", "sha256": "9aec5da331524158c6d1a4ac0ab1541149c0b9505fde06423b02f5ef0106b9f0", "dest": "cargo/vendor/windows_x86_64_msvc-0.42.2"}, {"type": "inline", "contents": "{\"package\": \"9aec5da331524158c6d1a4ac0ab1541149c0b9505fde06423b02f5ef0106b9f0\", \"files\": {}}", "dest": "cargo/vendor/windows_x86_64_msvc-0.42.2", "dest-filename": ".cargo-checksum.json"}, {"type": "inline", "contents": "[source.vendored-sources]\ndirectory = \"cargo/vendor\"\n\n[source.crates-io]\nreplace-with = \"vendored-sources\"\n", "dest": "cargo", "dest-filename": "config"}]