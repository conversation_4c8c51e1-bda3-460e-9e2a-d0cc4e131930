# English translations for PACKAGE package.
# Copyright (C) 2025 THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <PERSON>（曾嘉禾）<<EMAIL>>, 2025.
#
msgid ""
msgstr ""
"Project-Id-Version: Newelle 0.3.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-03-26 19:30\n"
"Last-Translator: <PERSON>（曾嘉禾）<<EMAIL>>\n"
"Language-Team: l10n-tw\n"
"Language: zh_TW\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "API 端點"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr "API 擷取的網址"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid "Automatically Serve"
msgstr "自動開啟 Ollama"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"若尚未執行將自動啟動 Ollama（ollama serve）。執行 killall ollama 以停止"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
msgid "Custom Model"
msgstr "自訂模型"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
msgid "Use a custom model"
msgstr "使用自訂的模型"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
msgid "Ollama Model"
msgstr "Ollama 模型"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
msgid "Name of the Ollama Model"
msgstr "Ollama 模型名稱"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "API 金鑰"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
msgid "API Key for "
msgstr "API 金鑰："

#: src/handlers/embeddings/openai_handler.py:38
msgid "API base url, change this to use different APIs"
msgstr "API 擷取的網址"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
msgid "Use Custom Model"
msgstr "使用自訂模型"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "模型"

#: src/handlers/embeddings/openai_handler.py:44
msgid "Name of the Embedding Model to use"
msgstr "嵌入模型的名稱"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
msgid " Model"
msgstr " 模型"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
msgid "The API key to use"
msgstr "使用的 API 金鑰"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
msgid "The model to use"
msgstr "使用的模型"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "Token 限制"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "使用的最多 Token 數量"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
msgid "Message Streaming"
msgstr "訊息串流"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "逐漸的將訊息輸出串流"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "取得輸出的指令"

#: src/handlers/llm/custom_handler.py:21
#, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr "{0} 參數會被以 JSON 格式儲存的聊天紀錄取代，而 {1} 則是包含系統提示"

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "取得建議的指令"

#: src/handlers/llm/custom_handler.py:22
#, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"{0} 參數會被以 JSON 格式儲存的聊天紀錄取代，{1} 包含系統提示，{2}則包含需要生"
"成的數量。必須傳回以建議（字串）組成的 JSON 陣列"

#: src/handlers/llm/gpt4all_handler.py:67
msgid "RAM Required: "
msgstr "記憶體要求："

#: src/handlers/llm/gpt4all_handler.py:68
msgid "Parameters: "
msgstr "參數："

#: src/handlers/llm/gpt4all_handler.py:69
msgid "Size: "
msgstr "大小："

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
msgid "Model to use"
msgstr "使用的模型"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
msgid "Name of the model to use"
msgstr "使用的模型名稱"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "Model Manager"
msgstr "模型管理器"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "可使用的模型列表"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr "更新 G4F"

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "隱私政策"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "開啟隱私政策網站"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "啟用思考"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr "允許模型能夠進行「推理」，僅支援某些模型"

#: src/handlers/llm/ollama_handler.py:176
msgid "Add custom model"
msgstr "新增自訂模型"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"在此以「模型名稱:大小」的格式新增其他模型\n"
"或是從 HuggingFace（hf.co/username/model）的任何 gguf"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr "更新 Ollama"

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "API 金鑰（必填）"

#: src/handlers/llm/gemini_handler.py:94
msgid "API key got from ai.google.dev"
msgstr "從 ai.google.dev 取得的 API 金鑰"

#: src/handlers/llm/gemini_handler.py:98
msgid "AI Model to use"
msgstr "使用的 AI 模型"

#: src/handlers/llm/gemini_handler.py:103
msgid "Enable System Prompt"
msgstr "啟用系統提示"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr "某些模型不支援此設定"

#: src/handlers/llm/gemini_handler.py:107
msgid "Inject system prompt"
msgstr "注入系統提示"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr "即使使用不支援系統提示的模型仍可以開啟此設定"

#: src/handlers/llm/gemini_handler.py:109
msgid "Thinking Settings"
msgstr "思考設定"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "有關推理模型的設定"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr "顯示思考過程，若您使用的模型不支援請停用此設定"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "啟用思考預算"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "是否啟用思考預算"

#: src/handlers/llm/gemini_handler.py:112
msgid "Thinking Budget"
msgstr "思考預算"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "思考時間的最大值"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "影像輸出"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr "啟用影像輸出，僅支援 gemini-2.0-flash-exp"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "啟用安全設定"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr "啟用 Google 的安全設定以排除不當內容"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
msgid "Advanced Parameters"
msgstr "更多設定"

#: src/handlers/llm/gemini_handler.py:135
msgid "Enable advanced parameters"
msgstr "調整更多參數"

#: src/handlers/llm/openai_handler.py:81
msgid "Include parameters like Top-P, Temperature, etc."
msgstr "包含 Token 限制、Top-P、溫度等參數"

#: src/handlers/llm/openai_handler.py:84
msgid "Name of the LLM Model to use"
msgstr "使用的大型語言模型"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "Top-P"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr "一種調整隨機性的替代的技術"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "溫度"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr "溫度越高隨機性越高"

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "重複懲罰"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr "數值介於±2.0。數值越大將減少輸出重複率"

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "存在懲罰"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr "數值介於±2.0。數值越大將讓模型搬出新話題的機率"

#: src/handlers/llm/openai_handler.py:108
msgid "Custom Options"
msgstr "自訂選項"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr "提供包含自訂選項的 JSON"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Provider Sorting"
msgstr "供給者排序"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "根據最佳價格、流量或最低延遲選擇供給者"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "價格"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "流量"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "延遲"

#: src/handlers/llm/openrouter_handler.py:15
msgid "Providers Order"
msgstr "供給者排序"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr "新增自訂供給者，將不同供給者名稱以逗號隔開。留空以不使用"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "允許後備供給者"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "允許在無法使用主要供給者時使用其他供給者"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "將檔案編入索引"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"將文件資料夾內的所有檔案編入索引。您必須於每次有檔案變更、改變文件分析緝或嵌"
"入模行時使用"

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "執行的指令"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "{0} 參數會被模型的絕對路徑取代"

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr "Google SR 的 API 金鑰，設定「default」將使用預設金鑰"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "語言"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "IETF 的辨識語言"

#: src/handlers/stt/groqsr_handler.py:14
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr "Groq SR 的 API 金鑰，設定「default」將使用預設金鑰"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "Groq 模型"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "Groq 模型名稱"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr "選擇紀錄的語言。使用 ISO 639-1 語言碼（例如「en」英文，「fr」法文等）"

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "OpenAI 的端點網址"

#: src/handlers/stt/openaisr_handler.py:18
msgid "API Key for OpenAI"
msgstr "OpenAI 的 API 金鑰"

#: src/handlers/stt/openaisr_handler.py:25
msgid "Whisper Model"
msgstr "Whisper 模型"

#: src/handlers/stt/openaisr_handler.py:26
msgid "Name of the OpenAI model"
msgstr "OpenAI 的模型名稱"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"可選：自訂文字紀錄的語言。使用 ISO 639-1 語言碼（例如「en」為英文，「fr」為法"
"文等）"

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "模型路徑"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "VOSK 模型的絕對路徑（已解壓縮）"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
msgid "Name of the Whisper model"
msgstr "Whisper 模型的名稱"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "wit.ai 的伺服器存取權杖"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "無法理解語音"

#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language of the recognition. For example en, it..."
msgstr "辨識的語言，如 en，it..."

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Model Library"
msgstr "模型資料庫"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Manage Whisper models"
msgstr "管理 Whisper 模型"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "Advanced Settings"
msgstr "更多設定"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "More advanced settings"
msgstr "更多設定"

#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature to use"
msgstr "使用的溫度"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt for the recognition"
msgstr "語音辨識提示語"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr "使用語音辨識時傳入的提示語"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Endpoint"
msgstr "端點"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "使用的自訂端點"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "語音"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
msgid "The voice to use"
msgstr "使用的聲音"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "使用說明"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr "聲音生成的步驟，留空以忽略此欄。"

#: src/handlers/tts/custom_handler.py:17
#, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr "{0} 參數為絕對路徑，{1} 參數則是文字"

#: src/handlers/tts/elevenlabs_handler.py:11
msgid "API Key for ElevenLabs"
msgstr "ElevenLabs 的 API 金鑰"

#: src/handlers/tts/elevenlabs_handler.py:19
msgid "Voice ID to use"
msgstr "使用的 Voice ID"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "穩定度"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "聲音的穩定度"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "提升聲線相似度"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "提升聲線的相似度以及清晰度"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "加強語調"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr "數值越高將越加強語調"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "選擇聲音"

#: src/handlers/websearch/tavily.py:20
msgid "Token"
msgstr "Token"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "Tavily API 金鑰"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "搜尋結果數最大值"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "限制搜尋的結果數目"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "搜尋深度"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"搜尋的深度。進階搜尋將致力於取得最相關的資料與內容，而基礎搜尋將從各個來源提"
"供最基本的搜尋結果。每次基礎搜尋將花費一個 API Credit，而進階搜尋將花費兩個 "
"Credit。"

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "搜尋的分類"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"「新聞」分類將能夠方便的取得最新資訊，尤其是有關政治、賽事、等重大新聞。「一"
"般」分類適用於更廣泛的搜尋，也將取得更多來源。"

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "來源採取段落量"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr "每個來源將採取的段落量。每個段落約五百個字元。僅適用於進階搜尋。"

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "搜尋的時間範圍"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "僅適用於「新聞」分類"

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "包含回覆"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"將大型語言模型的回覆置入於搜尋序列中。基礎搜尋將大致回覆，而進階搜尋將給予更"
"精確的回覆。"

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "包含原始內容"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr "包含已從搜尋結果處理的 HTML 資料"

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "包含圖片"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr "再往路上搜尋圖片並且將結果輸出。"

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "包含圖片說明"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr "當「包含圖片」已被啟用時可啟用此選項以為每個圖片加註"

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "包含網域"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr "在搜尋結果內包含這些網域"

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "排除網域"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr "在搜尋結果內排除這些網域"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "區間"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "搜尋的區間"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "設定"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "設定檔名稱"

#: src/ui/profile.py:58
msgid "Copied Settings"
msgstr "已複製設定"

#: src/ui/profile.py:58
msgid "Settings that will be copied to the new profile"
msgstr "新設定檔將複製的設定"

#: src/ui/profile.py:70
msgid "Create Profile"
msgstr "建立新設定檔"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "導入設定檔"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
msgid "Edit Profile"
msgstr "編輯設定檔"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "導出設定檔"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "導出密碼"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "並且導出密鑰內容"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "導出頭像"

#: src/ui/profile.py:89
msgid "Also export the profile picture"
msgstr "並且導出頭像"

#: src/ui/profile.py:109 src/ui/explorer.py:692
msgid "Create"
msgstr "新增聊天室"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "套用"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr "目前的設定將複製到新的設定檔"

#: src/ui/profile.py:122 src/ui/profile.py:128
msgid "Newelle Profiles"
msgstr "Newelle 設定檔"

#: src/ui/profile.py:123
msgid "Export"
msgstr "導出"

#: src/ui/profile.py:129
msgid "Import"
msgstr "導入"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "設定頭像"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "執行緒編輯"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "沒有正在執行的執行緒"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "執行緒序號："

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "選擇設定檔"

#: src/ui/widgets/profilerow.py:53
msgid "Delete Profile"
msgstr "刪除設定檔"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "思考過程"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "點開以顯示完整內容"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "思考中..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr "思考中...點開以顯示完整內容"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "沒有思考過程"

#: src/ui/widgets/tipscarousel.py:41
msgid "Newelle Tips"
msgstr "Newelle 小秘訣"

#: src/ui/explorer.py:192
msgid "Folder is Empty"
msgstr "資料夾為空"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr "找不到檔案"

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "於新分頁開啟"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "於編輯器開啟"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "於檔案管理員開啟"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
msgid "Rename"
msgstr "重新命名"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "刪除"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "複製絕對路徑"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
msgid "Failed to open file manager"
msgstr "無法開啟檔案管理員"

#: src/ui/explorer.py:436
msgid "New name:"
msgstr "新名稱："

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "取消"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "重新命名成功"

#: src/ui/explorer.py:476
#, python-brace-format
msgid "Failed to rename: {}"
msgstr "重新命名 {} 失敗"

#: src/ui/explorer.py:497
msgid "Delete File?"
msgstr "是否刪除檔案？"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "是否刪除「{}」？"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "刪除成功"

#: src/ui/explorer.py:525
#, python-brace-format
msgid "Failed to delete: {}"
msgstr "無法刪除 {}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "已複製路徑"

#: src/ui/explorer.py:542
msgid "Failed to copy path"
msgstr "複製路徑失敗"

#: src/ui/explorer.py:580
msgid "Create new folder"
msgstr "建立新資料夾"

#: src/ui/explorer.py:583
msgid "Create new file"
msgstr "建立新檔案"

#: src/ui/explorer.py:586
msgid "Open Terminal Here"
msgstr "開啟終端機"

#: src/ui/explorer.py:640
msgid "Create New Folder"
msgstr "建立新設定檔"

#: src/ui/explorer.py:640
msgid "Folder name:"
msgstr "資料夾名稱："

#: src/ui/explorer.py:644
msgid "Create New File"
msgstr "建立新檔"

#: src/ui/explorer.py:644
msgid "File name:"
msgstr "新檔案名稱："

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "資料夾建立成功"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "檔案建立成功"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "已有相同名稱的檔案或資料夾"

#: src/ui/explorer.py:728
msgid "folder"
msgstr "資料夾"

#: src/ui/explorer.py:728
msgid "file"
msgstr "檔案"

#: src/ui/explorer.py:730
#, python-brace-format
msgid "Failed to create {}: {}"
msgstr "建立 {} 失敗：{}"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "幫助"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "捷徑"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "重新整理聊天室"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "重新整理資料夾"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "新分頁"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "貼上圖片"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "將焦點移動到訊息輸入框上"

#: src/ui/shortcuts.py:18
msgid "Start/stop recording"
msgstr "開始/停止錄影"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "儲存"

#: src/ui/shortcuts.py:20
msgid "Stop TTS"
msgstr "停止文本轉語音"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr "放大"

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr "縮小"

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr "程式輸出監測器"

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr "清除輸出"

#: src/ui/stdout_monitor.py:61
msgid "Start/Stop monitoring"
msgstr "開始/停止監測"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr "監測：已啟用"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr "監測：已停用"

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr "行數：{}"

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr "行數：0"

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "擴充功能"

#: src/ui/extension.py:50
msgid "Installed Extensions"
msgstr "已安裝的擴充功能"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "擴充功能使用說明"

#: src/ui/extension.py:89
msgid "Download new Extensions"
msgstr "下載擴充功能"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "從檔案安裝檔案..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "Newelle"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "已在新視窗開啟聊天室"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "歡迎來到 Newelle"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "您的個人助理"

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "原始碼（GitHub）"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "選擇您最喜歡的人工智慧模型"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"Newelle 可與不同模型與服務使用！\n"
"<b>註記：強烈建議您閱讀「大型語言模型 101」頁面</b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "大型語言模型 101"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "與檔案互動"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr "Newelle 能擷取檔案的相關資訊並且傳送給模型以給予更精確的回覆！"

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "指令虛擬化"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"請小心！<b>模型雖然能產生程式碼，但因其不在我們的控制之下，所以可能產生有問題"
"的程式碼！</b>\n"
"即使指令大多可能將於<b> Flatpak 沙盒中執行</b>，但請仍小心。"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "利用擴充功能讓 Newelle 的功能多又更多！"

#: src/ui/presentation.py:136
msgid "Download extensions"
msgstr "下載擴充功能"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "權限錯誤"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr "Newelle 沒有足夠權限在系統內執行指令"

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "開始使用應用程式"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "開始聊天"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "一般"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "模型"

#: src/ui/settings.py:49 src/constants.py:528
msgid "Prompts"
msgstr "提示語"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "認知"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "語言模型"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "其他大型語言模型"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "其他提供大型語言模型的服務"

#: src/ui/settings.py:73
msgid "Advanced LLM Settings"
msgstr "更多設定"

#: src/ui/settings.py:77
msgid "Secondary Language Model"
msgstr "輔助語言模型"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr "專門執行第二類工作：例如聊天室名稱生成以及記憶生成"

#: src/ui/settings.py:94
msgid "Embedding Model"
msgstr "嵌入模型"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"專門將文字轉成向量以提供給 RAG 與長期記憶所使用的模型。改變此設定後請重整索引"
"與清除記憶。"

#: src/ui/settings.py:105 src/window.py:647
msgid "Long Term Memory"
msgstr "長期記憶"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "將舊聊天紀錄內容形成記憶"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "網路搜尋"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "在網路上搜尋更多資訊"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "文本轉語音程式"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "選擇文本轉語音的程式"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "語音辨識"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "選擇語音辨識引擎"

#: src/ui/settings.py:150
msgid "Automatic Speech To Text"
msgstr "自動語音辨識"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr "在朗讀完畢後自動重新啟動語音辨識服務"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "提示控制"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "界面"

#: src/ui/settings.py:162
msgid "Interface Size"
msgstr "界面大小"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "調整界面的大小"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "編輯器顏色主題"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "更改編輯器顏色主題"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "隱藏檔"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "顯示隱藏檔"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "使用 Enter 輸入"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"若啟用此功能將會讓 Enter 鍵傳送訊息，否則將文字換行，並使用 Shift＋Enter 傳送"
"訊息。"

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "將思考過程從歷史移除"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr "將推論模型的思考內容從歷史中移除以節省 Token 消耗"

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "顯示 LaTeX"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "在聊天室顯示 LaTeX 數學式"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "反向聊天室"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr "將最新的訊息至頂"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "自動生成聊天室名稱"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr "生成第一則訊息後自動生成聊天室名稱"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "建議訊息數量"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "建議訊息的顯示數量"

#: src/ui/settings.py:224
msgid "Username"
msgstr "使用者名稱"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr "更改聊天室內的使用者名稱，預設將不會將此資訊傳送給模型"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "類神經網路控制"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "在模擬器中執行指令"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "外部終端機"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr "選擇執行指令的外部終端機"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "模型記憶"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "模型將會記得多久的聊天紀錄"

#: src/ui/settings.py:266
msgid "Developer"
msgstr "開發者"

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr "即時的監測程式，對於除錯與下載進度十分方便"

#: src/ui/settings.py:270
msgid "Open"
msgstr "開啟"

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr "刪除 pip 路徑"

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr "刪除多餘依賴"

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr "安裝 pip 模組"

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr "手動安裝 pip 模組"

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "自訂自動執行指令"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "將會在終端機內自動執行指令"

#: src/ui/settings.py:313
msgid "Max number of commands"
msgstr "最多指令數量"

#: src/ui/settings.py:313
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr "一次訊息的最多指令數量"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "瀏覽器"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "瀏覽器設定"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "使用外部瀏覽器"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr "使用外部瀏覽棄取代內部瀏覽器開啟連結"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "保留當前瀏覽器分頁"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr "在重新啟動時開啟關閉前的分頁狀態。請重新啟動應用程式以套用此變更"

#: src/ui/settings.py:361
msgid "Delete browser data"
msgstr "刪除瀏覽器資料"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "刪除瀏覽器的所有資料"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "初始瀏覽頁面"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "瀏覽器開啟後將開啟的頁面"

#: src/ui/settings.py:375
msgid "Search string"
msgstr "搜尋字串"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr "將在瀏覽器搜尋的字串，%s 將以搜尋序列取代"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "檔案來源（RAG）"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "在回應中包含檔案內容"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "檔案分析器"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr "使用不同技術提取各個檔案的相關內容"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "不支援時閱讀檔案"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr "如果模型不支援閱讀文件將會利用檔案分析器傳送檔案相關資訊給模型"

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "RAG 使用 Tokens 的最大值"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"此將限制每次呼叫 RAG 時使用的 Tokens 的數量。如果需要使用的 Tokens 多過於此數"
"值，將僅傳送檔案的大致內容"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "文件資料夾"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr "檔案分析器將從資料夾內提取資訊"

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr "將欲索引的檔案放置此資料夾內。"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "靜音門檻"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr "靜音門檻以秒為單位，當音量為 0 時也視為靜音"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "靜音時間"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr "靜音時間以秒為單位，當靜音時間達到此秒數將自動停止錄音"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "權限不足"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr "Newelle 沒有足夠權限在系統內執行指令，請執行以下指令"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "確認"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr "已刪除 pip 路徑"

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr ""
"pip 路徑已被刪除，您可以重新安裝其依賴。請重新啟動應用程式以套用此操作。"

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "Newelle 預覽 API"

#: src/constants.py:26
msgid "GPT4Free"
msgstr "GPT4Free"

#: src/constants.py:34
msgid "Local Model"
msgstr "本地模型"

#: src/constants.py:35
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr ""
"不支援顯卡加速，僅有「Ollama 模型」選項支援。不過選擇此選項能保有更多隱私"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "Ollama 模型"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "讓同時在硬體上執行多個模型更加容易"

#: src/constants.py:47
msgid "Groq"
msgstr "Groq"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "Google Gemini API"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "OpenAI API"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr "支援自訂 API 端點。"

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "Anthropic Claude"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr "Anthropic Claude 的官方 API，支援圖形與檔案上傳。需要 API 金鑰"

#: src/constants.py:73
msgid "Mistral"
msgstr "Mistral"

#: src/constants.py:74
msgid "Mistral API"
msgstr "Mistral API"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "支援廣泛種類的模型"

#: src/constants.py:87
msgid "Deepseek"
msgstr "Deepseek"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "擁有最強大的開源模型"

#: src/constants.py:94 src/constants.py:203
msgid "Custom Command"
msgstr "自動執行的指令"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "從自訂指令中使用"

#: src/constants.py:104
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr "可離線執行。此為使用 C++ 改良過的 Whisper 模型"

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "可離線執行，僅支援英文"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "Google 語音辨識"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "Google 線上語音辨識"

#: src/constants.py:124
msgid "Groq Speech Recognition"
msgstr "Groq 語音辨識"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit AI"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr "免費線上語音辨識 API（請在網站上選擇語言）"

#: src/constants.py:137
msgid "Vosk API"
msgstr "Vosk API"

#: src/constants.py:138
msgid "Works Offline"
msgstr "可離線執行"

#: src/constants.py:144
msgid "Whisper API"
msgstr "Whisper API"

#: src/constants.py:145
msgid "Uses OpenAI Whisper API"
msgstr "使用 OpenAI Whisper API"

#: src/constants.py:151
msgid "Custom command"
msgstr "自動執行的指令"

#: src/constants.py:152
msgid "Runs a custom command"
msgstr "此可執行自訂指令"

#: src/constants.py:161
msgid "Google TTS"
msgstr "Google 文本轉語音"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "Google 的文本轉語音"

#: src/constants.py:167
msgid "Kokoro TTS"
msgstr "Kokoro 文本轉語音"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr "輕量且快速的開源文本轉語音。～3GB 依賴，400MB 模型"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "ElevenLabs 文本轉語音"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "自然、多樣的聲線"

#: src/constants.py:179 src/constants.py:180
msgid "OpenAI TTS"
msgstr "OpenAI 文本轉語音"

#: src/constants.py:185
msgid "Groq TTS"
msgstr "Groq 文本轉語音"

#: src/constants.py:186
msgid "Groq TTS API"
msgstr "Groq 文本轉語音 API"

#: src/constants.py:191 src/constants.py:192
msgid "Custom OpenAI TTS"
msgstr "自訂 OpenAI 文本轉語音"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "Espeak 文本轉語音"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "離線文本轉語音"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr "使用自訂指令當作文本轉語音工具，{0} 將會被取代為需處理的文字"

#: src/constants.py:212
msgid "WordLlama"
msgstr "WordLlama"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr "基於 llama 的輕量級模型。可離線使用，資源消耗極低。"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "Ollama 嵌入"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr "使用 Ollama 模型嵌入內容"

#: src/constants.py:231
msgid "Use Google Gemini API to get embeddings"
msgstr "使用 Google Gemini API 取得嵌入內容"

#: src/constants.py:239
msgid "User Summary"
msgstr "聊天歷史大意"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "從對話內容中生成大意"

#: src/constants.py:245
msgid "Memoripy"
msgstr "Memoripy"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"使用前後文記憶處理其他聊天歷史的內容，包括取得、壓縮、記憶損失、以及概念分析"
"等技術。每次訊息將呼叫一次。"

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "聊天歷史大意＋Memoripy"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "同時使用聊天歷史大意以及 Memoripy"

#: src/constants.py:260
msgid "Document reader"
msgstr "文件閱讀器"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr "傳統 RAG — 將檔案縮減並嵌入，最後與序列比對，輸出最相關的檔案"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr "SearXNG — 尊重隱私的開源元搜尋引擎"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "DuckDuckGo 搜尋"

#: src/constants.py:281
msgid "Tavily"
msgstr "Tavily"

#: src/constants.py:282
msgid "Tavily search"
msgstr "Tavily 搜尋"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "模型最佳化"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr "讓模型能提供更準確的回應以及提供更多前後文"

#: src/constants.py:384
msgid "Console access"
msgstr "終端機取用權"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "模型將有權使用電腦上的終端機"

#: src/constants.py:392
msgid "Current directory"
msgstr "目前所在資料夾"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "目前我們在哪個資料夾"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "允許大型語言模型使用網路搜尋"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "基本功能"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "顯示表格與程式（不必啟用）"

#: src/constants.py:419
msgid "Graphs access"
msgstr "圖表取用權"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "模型將有權生成圖表"

#: src/constants.py:428
msgid "Show image"
msgstr "顯示圖片"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "模型將會生成圖片"

#: src/constants.py:437
msgid "Custom Prompt"
msgstr "自訂提示語"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "在此新增自訂的提示語"

#: src/constants.py:480
msgid "LLM and Secondary LLM settings"
msgstr "大型語言模型與輔助模型設定"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "文本轉語音"

#: src/constants.py:485
msgid "Text to Speech settings"
msgstr "文本轉語音設定"

#: src/constants.py:488
msgid "STT"
msgstr "語音辨識"

#: src/constants.py:490
msgid "Speech to Text settings"
msgstr "語音辨識設定"

#: src/constants.py:493
msgid "Embedding"
msgstr "嵌入"

#: src/constants.py:495
msgid "Embedding settings"
msgstr "嵌入設定"

#: src/constants.py:498
msgid "Memory"
msgstr "記憶"

#: src/constants.py:500
msgid "Memory settings"
msgstr "設定"

#: src/constants.py:503
msgid "Websearch"
msgstr "網路搜尋"

#: src/constants.py:505
msgid "Websearch settings"
msgstr "網路搜尋設定"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
msgid "Document analyzer settings"
msgstr "檔案分析器設定"

#: src/constants.py:515
msgid "Extensions settings"
msgstr "擴充功能設定"

#: src/constants.py:518
msgid "Inteface"
msgstr "界面"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr "界面設定、隱藏檔、反向順序、放大等..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr "一般設定、指令虛擬化、記憶長度、自動生成聊天室名稱、目前資料夾等..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr "提示語設定、自訂提示語等..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "聊天室 "

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "終端機執行緒正在背景中執行"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "關閉視窗後將自動停止執行"

#: src/main.py:210
msgid "Close"
msgstr "關閉"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "重新整理聊天紀錄完成"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "重新整理資料夾完成"

#: src/main.py:254
msgid "Chat is created"
msgstr "已建立新的聊天室"

#: src/window.py:120
msgid "Keyboard shorcuts"
msgstr "快捷鍵"

#: src/window.py:121
msgid "About"
msgstr "關於"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "聊天"

#: src/window.py:170
msgid "History"
msgstr "最近的聊天紀錄"

#: src/window.py:191
msgid "Create a chat"
msgstr "新增聊天室"

#: src/window.py:196
msgid "Chats"
msgstr "聊天室"

#: src/window.py:267
msgid " Stop"
msgstr " 停止"

#: src/window.py:282
msgid " Clear"
msgstr " 清除"

#: src/window.py:297
msgid " Continue"
msgstr " 繼續"

#: src/window.py:310
msgid " Regenerate"
msgstr " 重新生成"

#: src/window.py:376
msgid "Send a message..."
msgstr "傳送訊息......"

#: src/window.py:467
msgid "Explorer Tab"
msgstr "探索分頁"

#: src/window.py:468
msgid "Terminal Tab"
msgstr "終端機監分頁"

#: src/window.py:469
msgid "Browser Tab"
msgstr "瀏覽器分頁"

#: src/window.py:589
msgid "Ask about a website"
msgstr "詢問有關一個網站的資訊"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr "在聊天室內以 #https://網站.名稱 的格式以詢問一個網站的資訊"

#: src/window.py:590
msgid "Check out our Extensions!"
msgstr "看看我們的擴充功能"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "我們有五花八門的擴充功能可供使用！"

#: src/window.py:591
msgid "Chat with documents!"
msgstr "與檔案互動！"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr "在「文件」資料夾內放置檔案以讓人工智慧讀取，提供更精確的回覆"

#: src/window.py:592
msgid "Surf the web!"
msgstr "支援網路搜尋！"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr "提供網路搜尋的功能以提供更新的資訊"

#: src/window.py:593
msgid "Mini Window"
msgstr "迷你視窗"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "啟用迷你視窗模式以更方便地問問題"

#: src/window.py:594
msgid "Text to Speech"
msgstr "文本轉語音"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr "Newelle 提供文本轉語音功能"

#: src/window.py:595
msgid "Keyboard Shortcuts"
msgstr "快捷鍵"

#: src/window.py:595
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "使用快捷鍵以更便捷地使用 Newelle"

#: src/window.py:596
msgid "Prompt Control"
msgstr "提示語控制"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr "Newelle 讓您能夠完全控制提示語。"

#: src/window.py:597
msgid "Thread Editing"
msgstr "執行緒編輯"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "檢視從 Newelle 執行的各種行程"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr "高自由度的提示語"

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr "您可以"

#: src/window.py:605
msgid "New Chat"
msgstr "新聊天室"

#: src/window.py:623
msgid "Provider Errror"
msgstr "供給者錯誤"

#: src/window.py:646
msgid "Local Documents"
msgstr "本地檔案"

#: src/window.py:650
msgid "Web search"
msgstr "網路搜尋"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "此供給者沒有模型清單"

#: src/window.py:901
msgid " Models"
msgstr " 個模型"

#: src/window.py:904
msgid "Search Models..."
msgstr "搜尋模型......"

#: src/window.py:1132
msgid "Create new profile"
msgstr "建立新設定檔"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "無法辨識聲音"

#: src/window.py:1303
msgid "Images"
msgstr "圖片"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "大型語言模型支援的檔案"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "RAG 支援的檔案"

#: src/window.py:1333
msgid "Supported Files"
msgstr "支援的檔案"

#: src/window.py:1337
msgid "All Files"
msgstr "所有檔案"

#: src/window.py:1343
msgid "Attach file"
msgstr "嵌入檔案"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "在執行完成前無法上傳檔案"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "檔案無法辨識"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "您無法繼續此訊息。"

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "您無法重新生成此訊息。"

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "已清除聊天紀錄"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "此訊息已從聊天紀錄中取消並且刪除"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr "在執行完成前無法傳送訊息"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr "在執行完成前無法編輯訊息"

#: src/window.py:3080
msgid "Prompt content"
msgstr "提示內容"

#: src/window.py:3339
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"注意：此人工智慧模型有權取用電腦中的資料\n"
"我們對這些模型不承擔任何責任\n"
"請勿分享機密資訊"

#: src/window.py:3368
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"注意：此人工智慧模型有權取用電腦中的資料\n"
"我們對這些模型不承擔任何責任\n"
"請勿分享機密資訊"

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "錯誤資料夾路徑"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "執行緒尚未完成，執行緒序號："

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "無法開啟資料夾"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "聊天室為空"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr "利用擴充功能讓 Newelle 的功能多又更多！"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "AI 聊天機器人"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "快速設定檔選取"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
msgid "Message Editing"
msgstr "訊息編輯"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "多過 10 種 AI 服務"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "修正各種錯誤"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr "迷你應用程式支援！擴充功能將能於側欄顯示迷你應用程式"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr "新增內建瀏覽器迷你應用程式：直接地於 Newelle 瀏覽並內嵌網頁"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr "改良內建檔案管理，支援多檔案操作"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr "內建檔案編輯器：直接地於 Newelle 編輯檔案與程式碼"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr "內建終端機迷你應用程式：於 Newelle 直接地開啟終端機"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr "動態提示語"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr "現在可直接更改聊天室名稱"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
msgid "Minor bug fixes"
msgstr "修正各種小錯誤"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr "支援 Kokoro TTS 與 Whisper.CPP 的更多語言"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr "直接地於應用程式內執行 HTML/CSS/JS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr "新增更動聊天室時的小動畫"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "小小改進"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "改善檔案閱讀理解速度"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "新增 CTRL＋Enter 快捷鍵"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "改善程式碼區塊顯示"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
msgid "Fix Kokoro TTS"
msgstr "修復 Kokoro 文本轉語音"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "從文本轉語音移除表情符號"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "設定 API 金鑰為「密碼」型輸入框"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "新增 Gemini 思考支援"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "更新翻譯"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "新增更多新功能"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr "網站閱讀與支援 SearXNG、DuckDuckGo、與 Tavily 等搜尋引擎"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "改良 LaTeX 處理以及文件管理"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "新增推理模型的「思考過程」以及 OpenRouter 管理"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "新增 Groq Llama4 圖片辨識支援"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "新翻譯（繁體中文、孟加拉文、印度文）"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "新增更多新功能以及修正錯誤"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "新增更多新功能以及修正錯誤"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "新增更多新功能以及修正錯誤"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "新增更多新功能以及修正錯誤"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr "改良 g4f 資料庫的版本管理、使用者說明、擴充功能瀏覽、以及模型處理。"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"新增更多新功能以及修正錯誤。我們不但更改了擴充功能架構、新增新模型、以及新增"
"圖片上傳功能等。"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "穩定版本"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
msgid "Added extension"
msgstr "已新增擴充功能"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
msgid "Blacklist of commands"
msgstr "指令黑名單"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "翻譯"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "重新設計"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "Qwersyk"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "Newelle：您強大的聊天機器人"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "ai;assistant;chat;chatgpt;gpt;llm;ollama;"

#~ msgid "max Tokens"
#~ msgstr "Token 限制"

#~ msgid "Max tokens of the generated text"
#~ msgstr "使用的最多 Token 數量"

#~ msgid "Any free Provider"
#~ msgstr "其他免費服務"

#~ msgid "chat;ai;gpt;chatgpt;assistant;"
#~ msgstr "chat;ai;gpt;chatgpt;assistant;"

#~ msgid "_Cancel"
#~ msgstr "取消"

#~ msgid "_Delete"
#~ msgstr "刪除"

#, python-brace-format
#~ msgid "Name of the {provider_name} Model"
#~ msgstr "模型 {provider_name} 供應者名稱"

#~ msgid "Choose an extension"
#~ msgstr "從檔案安裝..."

#~ msgid " has been removed"
#~ msgstr " 已經被移除"

#~ msgid "Extension added. New extensions will run from the next launch"
#~ msgstr "擴充功能安裝成功。新的擴充功能將在下次重新啟動後套用。"

#~ msgid "The extension is wrong"
#~ msgstr "錯誤的擴充功能"

#~ msgid "This is not an extension"
#~ msgstr "此非擴充功能"

#~ msgid "Chat has been stopped"
#~ msgstr "已停止聊天室"

#~ msgid "The change will take effect after you restart the program."
#~ msgstr "將在下次重新啟動後套用設定。"
