# Arabic translation for PACKAGE package.
# Copyright (C) 2024
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, 2024.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-07-03 09:00+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: Arabic\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "نقطة نهاية الـ API"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr ""
"عنوان URL الأساسي لواجهة برمجة التطبيقات، غيّره لاستخدام واجهات برمجة تطبيقات "
"التداخل"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid "Automatically Serve"
msgstr "الخدمة تلقائياً"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"تشغيل ollama serve تلقائيًا في الخلفية عند الحاجة إذا لم يكن قيد التشغيل. "
"يمكنك إيقافه باستخدام killall ollama"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
#, fuzzy
msgid "Custom Model"
msgstr "نموذج مخصص"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
msgid "Use a custom model"
msgstr "استخدم نموذجًا مخصصًا"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
msgid "Ollama Model"
msgstr "نموذج أولاما"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
msgid "Name of the Ollama Model"
msgstr "اسم نموذج أولاما"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "مفتاح الـ API"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
msgid "API Key for "
msgstr "مفتاح الـ API لـ "

#: src/handlers/embeddings/openai_handler.py:38
msgid "API base url, change this to use different APIs"
msgstr ""
"عنوان URL الأساسي لواجهة برمجة التطبيقات، غيّره لاستخدام واجهات برمجة تطبيقات "
"مختلفة"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
#, fuzzy
msgid "Use Custom Model"
msgstr "استخدام نموذج مخصص"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "النموذج"

#: src/handlers/embeddings/openai_handler.py:44
msgid "Name of the Embedding Model to use"
msgstr "اسم نموذج التضمين المراد استخدامه"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
msgid " Model"
msgstr " نموذج"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
msgid "The API key to use"
msgstr "مفتاح الـ API المراد استخدامه"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
msgid "The model to use"
msgstr "النموذج المراد استخدامه"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "الحد الأقصى للرموز"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "الحد الأقصى لعدد الرموز التي سيتم توليدها"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
#, fuzzy
msgid "Message Streaming"
msgstr "تدفق الرسائل"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "تدفق إخراج الرسائل تدريجياً"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "الأمر المراد تنفيذه للحصول على مخرجات الروبوت"

#: src/handlers/llm/custom_handler.py:21
#, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr ""
"الأمر المراد تنفيذه للحصول على استجابة الروبوت، سيتم استبدال {0} بملف JSON "
"يحتوي على الدردشة، و {1} بموجه النظام"

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "الأمر المراد تنفيذه للحصول على اقتراحات الروبوت"

#: src/handlers/llm/custom_handler.py:22
#, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"الأمر المراد تنفيذه للحصول على اقتراحات الدردشة، سيتم استبدال {0} بملف JSON "
"يحتوي على الدردشة، و {1} بالمطالبات الإضافية، و {2} بعدد الاقتراحات المراد "
"توليدها. يجب أن يعيد مصفوفة JSON تحتوي على الاقتراحات كسلاسل نصية"

#: src/handlers/llm/gpt4all_handler.py:67
msgid "RAM Required: "
msgstr "الرام المطلوبة: "

#: src/handlers/llm/gpt4all_handler.py:68
msgid "Parameters: "
msgstr "المعلمات: "

#: src/handlers/llm/gpt4all_handler.py:69
msgid "Size: "
msgstr "الحجم: "

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
msgid "Model to use"
msgstr "النموذج المراد استخدامه"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
msgid "Name of the model to use"
msgstr "اسم النموذج المراد استخدامه"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "Model Manager"
msgstr "مدير النماذج"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "قائمة النماذج المتاحة"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr "تحديث G4F"

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "سياسة الخصوصية"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "فتح موقع سياسة الخصوصية"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "تمكين التفكير"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr "السماح بالتفكير في النموذج، بعض النماذج فقط مدعومة"

#: src/handlers/llm/ollama_handler.py:176
msgid "Add custom model"
msgstr "أضف نموذجًا مخصصًا"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"أضف أي نموذج إلى هذه القائمة بوضع الاسم:الحجم\n"
"أو أي gguf من hf بـ hf.co/username/model"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr "تحديث أولاما"

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "مفتاح الـ API (مطلوب)"

#: src/handlers/llm/gemini_handler.py:94
msgid "API key got from ai.google.dev"
msgstr "مفتاح الـ API تم الحصول عليه من ai.google.dev"

#: src/handlers/llm/gemini_handler.py:98
msgid "AI Model to use"
msgstr "نموذج الذكاء الاصطناعي المراد استخدامه"

#: src/handlers/llm/gemini_handler.py:103
msgid "Enable System Prompt"
msgstr "تمكين موجه النظام"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr ""
"بعض النماذج لا تدعم موجه النظام (أو تعليمات المطورين)، عطلها إذا ظهرت لك "
"أخطاء بخصوصه"

#: src/handlers/llm/gemini_handler.py:107
msgid "Inject system prompt"
msgstr "حقن موجه النظام"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr ""
"حتى لو كان النموذج لا يدعم موجه النظام، ضع الموجهات في أعلى رسالة المستخدم"

#: src/handlers/llm/gemini_handler.py:109
#, fuzzy
msgid "Thinking Settings"
msgstr "إعدادات التفكير"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "إعدادات حول نماذج التفكير"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr "إظهار التفكير، عطلها إذا كان نموذجك لا يدعمها"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "تمكين ميزانية التفكير"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "إذا كان سيتم تمكين ميزانية التفكير"

#: src/handlers/llm/gemini_handler.py:112
msgid "Thinking Budget"
msgstr "ميزانية التفكير"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "كم من الوقت سيتم قضاؤه في التفكير"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "إخراج الصور"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr "تمكين إخراج الصور، مدعوم فقط من gemini-2.0-flash-exp"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "تمكين إعدادات الأمان"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr "تمكين إعدادات أمان جوجل لتجنب توليد محتوى ضار"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
msgid "Advanced Parameters"
msgstr "معلمات متقدمة"

#: src/handlers/llm/gemini_handler.py:135
msgid "Enable advanced parameters"
msgstr "تمكين المعلمات المتقدمة"

#: src/handlers/llm/openai_handler.py:81
#, fuzzy
msgid "Include parameters like Top-P, Temperature, etc."
msgstr "تضمين معلمات مثل الحد الأقصى للرموز، Top-P، درجة الحرارة، إلخ."

#: src/handlers/llm/openai_handler.py:84
msgid "Name of the LLM Model to use"
msgstr "اسم نموذج LLM المراد استخدامه"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "Top-P"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr "بديل لأخذ العينات بالحرارة، يسمى أخذ العينات النووية"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "درجة الحرارة"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr ""
"ما هي درجة حرارة العينة التي ستستخدم. القيم الأعلى ستجعل المخرجات أكثر "
"عشوائية"

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "عقوبة التردد"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr ""
"رقم بين -2.0 و 2.0. القيم الإيجابية تقلل من احتمالية النموذج لتكرار نفس "
"السطر حرفياً"

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "عقوبة الحضور"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr ""
"رقم بين -2.0 و 2.0. القيم الإيجابية تقلل من احتمالية النموذج للحديث عن "
"مواضيع جديدة"

#: src/handlers/llm/openai_handler.py:108
#, fuzzy
msgid "Custom Options"
msgstr "موجه مخصص"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr ""

#: src/handlers/llm/openrouter_handler.py:14
msgid "Provider Sorting"
msgstr "ترتيب المزودين"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "اختر المزودين بناءً على التسعير/الإنتاجية أو وقت الاستجابة"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "السعر"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "الإنتاجية"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "وقت الاستجابة"

#: src/handlers/llm/openrouter_handler.py:15
msgid "Providers Order"
msgstr "ترتيب المزودين"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr ""
"أضف ترتيب المزودين المراد استخدامهم، بأسماء مفصولة بفاصلة.\n"
"اتركه فارغاً لعدم التحديد"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "السماح بالعودة للوراء"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "السماح بالعودة إلى مزودين آخرين"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "فهرسة مستنداتك"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"فهرس جميع المستندات في مجلد مستنداتك. يجب عليك تشغيل هذه العملية في كل مرة "
"تقوم فيها بتحرير/إنشاء مستند، أو تغيير محلل المستندات، أو تغيير نموذج التضمين"

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "الأمر المراد تنفيذه"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "سيتم استبدال {0} بالمسار الكامل للنموذج"

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr "مفتاح API لـ Google SR، اكتب 'default' لاستخدام الافتراضي"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "اللغة"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "لغة النص المراد التعرف عليه في IETF"

#: src/handlers/stt/groqsr_handler.py:14
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr "مفتاح API لـ Groq SR، اكتب 'default' لاستخدام الافتراضي"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "نموذج Groq"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "اسم نموذج Groq"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"حدد اللغة للنسخ. استخدم رموز لغة ISO 639-1 (مثل \"en\" للإنجليزية، \"fr\" "
"للفرنسية، إلخ)."

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "نقطة النهاية لطلبات OpenAI"

#: src/handlers/stt/openaisr_handler.py:18
msgid "API Key for OpenAI"
msgstr "مفتاح API لـ OpenAI"

#: src/handlers/stt/openaisr_handler.py:25
msgid "Whisper Model"
msgstr "نموذج Whisper"

#: src/handlers/stt/openaisr_handler.py:26
msgid "Name of the OpenAI model"
msgstr "اسم نموذج OpenAI"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"اختياري: حدد اللغة للنسخ. استخدم رموز لغة ISO 639-1 (مثل \"en\" للإنجليزية، "
"\"fr\" للفرنسية، إلخ)."

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "مسار النموذج"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "المسار المطلق لنموذج VOSK (مفكوك الضغط)"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
msgid "Name of the Whisper model"
msgstr "اسم نموذج Whisper"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "رمز الوصول إلى الخادم لـ wit.ai"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "لم يتم فهم الصوت"

#: src/handlers/stt/whispercpp_handler.py:47
#, fuzzy
msgid "Language of the recognition. For example en, it..."
msgstr "لغة التعرف."

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Model Library"
msgstr "مكتبة النماذج"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Manage Whisper models"
msgstr "إدارة نماذج Whisper"

#: src/handlers/stt/whispercpp_handler.py:49
#, fuzzy
msgid "Advanced Settings"
msgstr "إعدادات متقدمة"

#: src/handlers/stt/whispercpp_handler.py:49
#, fuzzy
msgid "More advanced settings"
msgstr "إعدادات متقدمة"

#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature to use"
msgstr "درجة الحرارة المراد استخدامها"

#: src/handlers/stt/whispercpp_handler.py:51
#, fuzzy
msgid "Prompt for the recognition"
msgstr "موجه للتعرف"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr "الموجه المراد استخدامه للتعرف"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Endpoint"
msgstr "نقطة النهاية"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "نقطة نهاية مخصصة للخدمة المراد استخدامها"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "الصوت"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
msgid "The voice to use"
msgstr "الصوت المراد استخدامه"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "تعليمات"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr "تعليمات لتوليد الصوت. اتركها فارغة لتجنب هذا الحقل"

#: src/handlers/tts/custom_handler.py:17
#, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr "سيتم استبدال {0} بالمسار الكامل للملف، و {1} بالنص"

#: src/handlers/tts/elevenlabs_handler.py:11
msgid "API Key for ElevenLabs"
msgstr "مفتاح API لـ ElevenLabs"

#: src/handlers/tts/elevenlabs_handler.py:19
msgid "Voice ID to use"
msgstr "معرف الصوت المراد استخدامه"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "الاستقرار"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "استقرار الصوت"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "تعزيز التشابه"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "يعزز وضوح الصوت الكلي وتشابه المتحدث"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "مبالغة في الأسلوب"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr "يوصى بقيم عالية إذا كان أسلوب الكلام يجب أن يكون مبالغًا فيه"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "اختر الصوت المفضل"

#: src/handlers/websearch/tavily.py:20
msgid "Token"
msgstr "الرمز المميز"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "مفتاح API لـ Tavily"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "الحد الأقصى للنتائج"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "عدد النتائج المراد اعتبارها"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "عمق البحث"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"عمق البحث. البحث المتقدم مصمم لاسترجاع المصادر الأكثر صلة ومقتطفات المحتوى "
"لاستعلامك، بينما يوفر البحث الأساسي مقتطفات محتوى عامة من كل مصدر. يكلف "
"البحث الأساسي 1 رصيد API، بينما يكلف البحث المتقدم 2 رصيد API."

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "فئة البحث"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"فئة البحث. الأخبار مفيدة لاسترجاع التحديثات في الوقت الفعلي، خاصة حول "
"السياسة، الرياضة، والأحداث الجارية الرئيسية التي تغطيها مصادر الإعلام "
"الرئيسية. عامة للبحوث الأوسع والأكثر عمومية التي قد تتضمن مجموعة واسعة من "
"المصادر."

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "القطع لكل مصدر"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr ""
"عدد أجزاء المحتوى المراد استردادها من كل مصدر. طول كل جزء هو 500 حرف كحد "
"أقصى. متوفر فقط عندما يكون عمق البحث متقدمًا."

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "عدد الأيام الماضية من التاريخ الحالي لتضمينها"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "متاح فقط إذا كان الموضوع أخبارًا."

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "تضمين الإجابة"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"تضمين إجابة مولدة من LLM للاستعلام المقدم. البحث الأساسي يعيد إجابة سريعة. "
"البحث المتقدم يعيد إجابة أكثر تفصيلاً."

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "تضمين المحتوى الخام"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr "تضمين محتوى HTML المنظف والمحلل لكل نتيجة بحث."

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "تضمين الصور"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr "أجراء بحث عن الصور وتضمين النتائج في الرد."

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "تضمين أوصاف الصور"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr "عند تمكين تضمين الصور، أضف أيضًا نصًا وصفيًا لكل صورة."

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "تضمين النطاقات"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr "قائمة بالنطاقات المراد تضمينها بشكل خاص في نتائج البحث."

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "استبعاد النطاقات"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr "قائمة بالنطاقات المراد استبعادها بشكل خاص من نتائج البحث."

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "المنطقة"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "المنطقة لنتائج البحث"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "الإعدادات"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "اسم الملف الشخصي"

#: src/ui/profile.py:58
#, fuzzy
msgid "Copied Settings"
msgstr "الإعدادات المنسوخة"

#: src/ui/profile.py:58
msgid "Settings that will be copied to the new profile"
msgstr "الإعدادات التي سيتم نسخها إلى الملف الشخصي الجديد"

#: src/ui/profile.py:70
msgid "Create Profile"
msgstr "إنشاء ملف شخصي"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "استيراد ملف شخصي"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
msgid "Edit Profile"
msgstr "تعديل الملف الشخصي"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "تصدير الملف الشخصي"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "تصدير كلمات المرور"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "تصدير الحقول المشابهة لكلمة المرور أيضًا"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "تصدير صورة الملف الشخصي"

#: src/ui/profile.py:89
msgid "Also export the profile picture"
msgstr "تصدير صورة الملف الشخصي أيضًا"

#: src/ui/profile.py:109 src/ui/explorer.py:692
#, fuzzy
msgid "Create"
msgstr "إنشاء"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "تطبيق"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr "سيتم نسخ إعدادات الملف الشخصي الحالي إلى الملف الجديد"

#: src/ui/profile.py:122 src/ui/profile.py:128
msgid "Newelle Profiles"
msgstr "ملفات Newelle الشخصية"

#: src/ui/profile.py:123
msgid "Export"
msgstr "تصدير"

#: src/ui/profile.py:129
msgid "Import"
msgstr "استيراد"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "تعيين صورة الملف الشخصي"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "تعديل الخيوط"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "لا توجد خيوط قيد التشغيل"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "رقم الخيط: "

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "تحديد الملف الشخصي"

#: src/ui/widgets/profilerow.py:53
msgid "Delete Profile"
msgstr "حذف الملف الشخصي"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "أفكار"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "توسيع لرؤية التفاصيل"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "جارٍ التفكير..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr "نموذج اللغة الكبيرة يفكر... وسّع لرؤية عملية التفكير"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "لم يتم تسجيل أي عملية تفكير"

#: src/ui/widgets/tipscarousel.py:41
msgid "Newelle Tips"
msgstr "نصائح Newelle"

#: src/ui/explorer.py:192
msgid "Folder is Empty"
msgstr "المجلد فارغ"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr "لم يتم العثور على الملف"

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "فتح في علامة تبويب جديدة"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "فتح في المحرر المدمج"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "فتح في مدير الملفات"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
msgid "Rename"
msgstr "إعادة تسمية"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "حذف"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "نسخ المسار الكامل"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
#, fuzzy
msgid "Failed to open file manager"
msgstr "فشل فتح مدير الملفات"

#: src/ui/explorer.py:436
#, fuzzy
msgid "New name:"
msgstr "اسم جديد:"

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "إلغاء"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "تمت إعادة التسمية بنجاح"

#: src/ui/explorer.py:476
#, fuzzy, python-brace-format
msgid "Failed to rename: {}"
msgstr "فشل إعادة التسمية: {}"

#: src/ui/explorer.py:497
msgid "Delete File?"
msgstr "حذف الملف؟"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "هل أنت متأكد أنك تريد حذف \"{}\"؟"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "تم الحذف بنجاح"

#: src/ui/explorer.py:525
#, fuzzy, python-brace-format
msgid "Failed to delete: {}"
msgstr "فشل الحذف: {}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "تم نسخ المسار إلى الحافظة"

#: src/ui/explorer.py:542
#, fuzzy
msgid "Failed to copy path"
msgstr "فشل نسخ المسار"

#: src/ui/explorer.py:580
#, fuzzy
msgid "Create new folder"
msgstr "إنشاء مجلد جديد"

#: src/ui/explorer.py:583
msgid "Create new file"
msgstr "إنشاء ملف جديد"

#: src/ui/explorer.py:586
msgid "Open Terminal Here"
msgstr "فتح الطرفية هنا"

#: src/ui/explorer.py:640
msgid "Create New Folder"
msgstr "إنشاء مجلد جديد"

#: src/ui/explorer.py:640
#, fuzzy
msgid "Folder name:"
msgstr "اسم المجلد:"

#: src/ui/explorer.py:644
msgid "Create New File"
msgstr "إنشاء ملف جديد"

#: src/ui/explorer.py:644
#, fuzzy
msgid "File name:"
msgstr "اسم الملف:"

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "تم إنشاء المجلد بنجاح"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "تم إنشاء الملف بنجاح"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "ملف أو مجلد بهذا الاسم موجود بالفعل"

#: src/ui/explorer.py:728
#, fuzzy
msgid "folder"
msgstr "مجلد"

#: src/ui/explorer.py:728
msgid "file"
msgstr "ملف"

#: src/ui/explorer.py:730
#, fuzzy, python-brace-format
msgid "Failed to create {}: {}"
msgstr "فشل إنشاء {}: {}"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "مساعدة"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "الاختصارات"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "إعادة تحميل الدردشة"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "إعادة تحميل المجلد"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "علامة تبويب جديدة"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "لصق الصورة"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "تركيز مربع الرسالة"

#: src/ui/shortcuts.py:18
msgid "Start/stop recording"
msgstr "بدء/إيقاف التسجيل"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "حفظ"

#: src/ui/shortcuts.py:20
#, fuzzy
msgid "Stop TTS"
msgstr "إيقاف تحويل النص إلى كلام"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr "تكبير"

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr "تصغير"

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr "مراقب إخراج البرنامج"

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr "مسح الإخراج"

#: src/ui/stdout_monitor.py:61
msgid "Start/Stop monitoring"
msgstr "بدء/إيقاف المراقبة"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr "المراقبة: نشطة"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr "المراقبة: متوقفة"

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr "الأسطر: {}"

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr "الأسطر: 0"

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "إضافات"

#: src/ui/extension.py:50
#, fuzzy
msgid "Installed Extensions"
msgstr "الإضافات المثبتة"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "دليل المستخدم للإضافات"

#: src/ui/extension.py:89
#, fuzzy
msgid "Download new Extensions"
msgstr "تنزيل إضافات جديدة"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "تثبيت إضافة من ملف..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "نيويل"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "الدردشة مفتوحة في نافذة مصغرة"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "مرحبًا بك في نيويل"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "مساعدك الافتراضي الأمثل."

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "صفحة جيت هاب"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "اختر نموذج لغة الذكاء الاصطناعي المفضل لديك"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"يمكن استخدام نيويل مع نماذج ومزودين متعددين!\n"
"<b>ملاحظة: يوصى بشدة بقراءة صفحة دليل نماذج اللغة الكبيرة</b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "دليل نماذج اللغة الكبيرة"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "الدردشة مع مستنداتك"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr ""
"يمكن لنيويل استرداد المعلومات ذات الصلة من المستندات التي ترسلها في الدردشة "
"أو من ملفاتك الخاصة! سيتم إرسال المعلومات ذات الصلة باستعلامك إلى نموذج "
"اللغة الكبيرة."

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "محاكاة الأوامر"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"يمكن استخدام نيويل لتشغيل الأوامر على نظامك، ولكن انتبه لما تقوم بتشغيله! "
"<b>نموذج اللغة الكبيرة ليس تحت سيطرتنا، لذلك قد يولد رمزًا ضارًا!</b>\n"
"افتراضيًا، سيتم <b>محاكاة أوامرك في بيئة Flatpak</b>، ولكن انتبه!"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "يمكنك توسيع وظائف Newelle باستخدام الإضافات!"

#: src/ui/presentation.py:136
#, fuzzy
msgid "Download extensions"
msgstr "تنزيل إضافات"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "خطأ في الأذونات"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr "نيويل لا يملك أذونات كافية لتشغيل الأوامر على نظامك."

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "ابدأ استخدام التطبيق"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "ابدأ الدردشة"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "عام"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "نموذج اللغة الكبيرة"

#: src/ui/settings.py:49 src/constants.py:528
msgid "Prompts"
msgstr "الموجهات"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "المعرفة"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "نموذج اللغة"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "نماذج لغة كبيرة أخرى"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "مزودو نماذج اللغة الكبيرة المتاحون الآخرون"

#: src/ui/settings.py:73
#, fuzzy
msgid "Advanced LLM Settings"
msgstr "إعدادات نماذج اللغة الكبيرة المتقدمة"

#: src/ui/settings.py:77
msgid "Secondary Language Model"
msgstr "نموذج اللغة الثانوي"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr ""
"النموذج المستخدم للمهام الثانوية، مثل العرض، اسم الدردشة، وتوليد الذاكرة"

#: src/ui/settings.py:94
msgid "Embedding Model"
msgstr "نموذج التضمين"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"يستخدم التضمين لتحويل النص إلى متجهات. يستخدمه الذاكرة طويلة الأمد و RAG. قد "
"يتطلب تغييره إعادة فهرسة المستندات أو إعادة تعيين الذاكرة."

#: src/ui/settings.py:105 src/window.py:647
#, fuzzy
msgid "Long Term Memory"
msgstr "الذاكرة طويلة الأمد"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "احتفظ بذاكرة المحادثات القديمة"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "البحث على الويب"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "ابحث عن المعلومات على الويب"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "برنامج تحويل النص إلى كلام"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "اختر برنامج تحويل النص إلى كلام المراد استخدامه"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "محرك تحويل الكلام إلى نص"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "اختر محرك التعرف على الكلام الذي تريده"

#: src/ui/settings.py:150
msgid "Automatic Speech To Text"
msgstr "تحويل الكلام إلى نص تلقائي"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr "إعادة تشغيل تحويل الكلام إلى نص تلقائيًا في نهاية النص/TTS"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "التحكم في الموجه"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "الواجهة"

#: src/ui/settings.py:162
#, fuzzy
msgid "Interface Size"
msgstr "حجم الواجهة"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "ضبط حجم الواجهة"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "نظام ألوان المحرر"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "تغيير نظام ألوان المحرر وكتل التعليمات البرمجية"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "الملفات المخفية"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "إظهار الملفات المخفية"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "إرسال مع ENTER"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"إذا تم التمكين، سيتم إرسال الرسائل باستخدام ENTER، للانتقال إلى سطر جديد "
"استخدم CTRL+ENTER. إذا تم التعطيل، سيتم إرسال الرسائل باستخدام SHIFT+ENTER، "
"والسطر الجديد باستخدام ENTER"

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "إزالة التفكير من السجل"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr "لا ترسل كتل التفكير القديمة لنماذج التفكير لتقليل استخدام الرموز"

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "عرض LaTeX"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "عرض صيغ LaTeX في الدردشة"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "عكس ترتيب الدردشة"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr ""
"إظهار أحدث الدردشات في الأعلى في قائمة الدردشات (غيّر الدردشة لتطبيق التغيير)"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "توليد أسماء الدردشات تلقائيًا"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr "توليد أسماء الدردشات تلقائيًا بعد أول رسالتين"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "عدد العروض"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "عدد اقتراحات الرسائل لإرسالها إلى الدردشة "

#: src/ui/settings.py:224
msgid "Username"
msgstr "اسم المستخدم"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr ""
"غيّر التسمية التي تظهر قبل رسالتك\n"
"هذه المعلومات لا يتم إرسالها إلى نموذج اللغة الكبيرة (LLM) افتراضيًا\n"
"يمكنك إضافتها إلى موجه باستخدام المتغير {USER}"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "التحكم في الشبكة العصبية"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "تشغيل الأوامر في جهاز افتراضي"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "الطرفية الخارجية"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr "اختر الطرفية الخارجية لتشغيل أوامر الكونسول فيها"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "ذاكرة البرنامج"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "كم من الوقت يتذكر البرنامج الدردشة"

#: src/ui/settings.py:266
msgid "Developer"
msgstr "المطور"

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr ""
"مراقبة إخراج البرنامج في الوقت الحقيقي، مفيد لتصحيح الأخطاء ورؤية تقدم "
"التنزيلات"

#: src/ui/settings.py:270
msgid "Open"
msgstr "فتح"

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr "حذف مسار pip"

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr "إزالة التبعيات الإضافية المثبتة"

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr ""

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr ""

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "تشغيل الأوامر تلقائيا"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "سيتم تشغيل الأوامر التي يكتبها الروبوت تلقائيًا"

#: src/ui/settings.py:313
#, fuzzy
msgid "Max number of commands"
msgstr "الحد الأقصى لعدد الأوامر"

#: src/ui/settings.py:313
#, fuzzy
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr "الحد الأقصى لعدد الأوامر التي سيكتبها الروبوت بعد طلب واحد من المستخدم"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "المتصفح"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "إعدادات المتصفح"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "استخدام متصفح خارجي"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr "استخدم متصفحًا خارجيًا لفتح الروابط بدلاً من المتصفح المدمج"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "استمرار جلسة المتصفح"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr ""
"استمرار جلسة المتصفح بين عمليات إعادة التشغيل. يتطلب إيقاف هذا الخيار إعادة "
"تشغيل البرنامج"

#: src/ui/settings.py:361
msgid "Delete browser data"
msgstr "حذف بيانات المتصفح"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "حذف جلسة المتصفح وبياناته"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "صفحة المتصفح الأولية"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "الصفحة التي سيبدأ منها المتصفح"

#: src/ui/settings.py:375
#, fuzzy
msgid "Search string"
msgstr "سلسلة البحث"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr "سلسلة البحث المستخدمة في المتصفح، يتم استبدال %s بالاستعلام"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "مصادر المستندات (RAG)"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "تضمين محتوى مستنداتك في الردود"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "محلل المستندات"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr ""
"يستخدم محلل المستندات تقنيات متعددة لاستخراج المعلومات ذات الصلة من مستنداتك"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "قراءة المستندات إذا كانت غير مدعومة"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr ""
"إذا كان نموذج اللغة الكبيرة لا يدعم قراءة المستندات، سيتم تقديم المعلومات "
"المتعلقة بالمستندات المرسلة في الدردشة إلى نموذج اللغة الكبيرة باستخدام محلل "
"المستندات الخاص بك."

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "الحد الأقصى للرموز لـ RAG"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"الحد الأقصى لعدد الرموز التي سيتم استخدامها لـ RAG. إذا لم تتجاوز المستندات "
"هذا العدد من الرموز،\n"
"أفرغها كلها في السياق"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "مجلد المستندات"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr ""
"ضع المستندات التي تريد استعلامها في مجلد مستنداتك. سيجد محلل المستندات "
"المعلومات ذات الصلة فيها إذا تم تمكين هذا الخيار"

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr "ضع جميع المستندات التي تريد فهرستها في هذا المجلد"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "عتبة الصمت"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr "عتبة الصمت بالثواني، نسبة من الحجم تعتبر صمتًا"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "وقت الصمت"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr "وقت الصمت بالثواني قبل أن يتوقف التسجيل تلقائياً"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "لا توجد أذونات كافية"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr ""
"لا تملك Newelle أذونات كافية لتشغيل الأوامر على نظامك، يرجى تشغيل الأمر "
"التالي"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "مفهوم"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr "تم حذف مسار Pip"

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr ""
"تم حذف مسار pip، يمكنك الآن إعادة تثبيت التبعيات. تتطلب هذه العملية إعادة "
"تشغيل التطبيق."

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "واجهة برمجة تطبيقات Newelle التجريبية"

#: src/constants.py:26
msgid "GPT4Free"
msgstr "GPT4Free"

#: src/constants.py:34
msgid "Local Model"
msgstr "النموذج المحلي"

#: src/constants.py:35
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr ""
"لا يوجد دعم لبطاقة الرسومات، استخدم OLLAMA بدلاً من ذلك. قم بتشغيل نموذج "
"اللغة الكبيرة محليًا، خصوصية أكبر ولكن أبطأ"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "مثيل أولاما"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "قم بتشغيل نماذج LLM متعددة بسهولة على جهازك الخاص"

#: src/constants.py:47
msgid "Groq"
msgstr "غروق"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "واجهة برمجة تطبيقات جوجل جيميني"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "واجهة برمجة تطبيقات OpenAI"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr ""
"واجهة برمجة تطبيقات OpenAI. تدعم نقاط النهاية المخصصة. استخدم هذا للمزودين "
"المخصصين"

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "أنثروبيك كلود"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr ""
"واجهات برمجة تطبيقات رسمية لنماذج أنثروبيك كلود، مع دعم الصور والملفات، "
"تتطلب مفتاح API"

#: src/constants.py:73
msgid "Mistral"
msgstr "ميسترال"

#: src/constants.py:74
msgid "Mistral API"
msgstr "واجهة برمجة تطبيقات ميسترال"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "واجهة برمجة تطبيقات Openrouter.ai، تدعم العديد من النماذج"

#: src/constants.py:87
msgid "Deepseek"
msgstr "ديبسيك"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "واجهة برمجة تطبيقات ديبسيك، أقوى نماذج مفتوحة المصدر"

#: src/constants.py:94 src/constants.py:203
#, fuzzy
msgid "Custom Command"
msgstr "أمر مخصص"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "استخدم مخرجات أمر مخصص"

#: src/constants.py:104
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr "يعمل دون اتصال. تطبيق Whisper محسن مكتوب بلغة C++"

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "يعمل دون اتصال. يدعم الإنجليزية فقط"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "التعرف على الكلام من جوجل"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "التعرف على الكلام من جوجل عبر الإنترنت"

#: src/constants.py:124
msgid "Groq Speech Recognition"
msgstr "التعرف على الكلام من Groq"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit AI"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr ""
"واجهة برمجة تطبيقات مجانية للتعرف على الكلام من wit.ai (اللغة المختارة على "
"الموقع)"

#: src/constants.py:137
msgid "Vosk API"
msgstr "واجهة برمجة تطبيقات Vosk"

#: src/constants.py:138
msgid "Works Offline"
msgstr "يعمل دون اتصال"

#: src/constants.py:144
msgid "Whisper API"
msgstr "واجهة برمجة تطبيقات Whisper"

#: src/constants.py:145
msgid "Uses OpenAI Whisper API"
msgstr "يستخدم واجهة برمجة تطبيقات OpenAI Whisper"

#: src/constants.py:151
#, fuzzy
msgid "Custom command"
msgstr "أمر مخصص"

#: src/constants.py:152
#, fuzzy
msgid "Runs a custom command"
msgstr "يشغل أمرًا مخصصًا"

#: src/constants.py:161
msgid "Google TTS"
msgstr "تحويل النص إلى كلام من جوجل"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "تحويل النص إلى كلام من جوجل"

#: src/constants.py:167
msgid "Kokoro TTS"
msgstr "تحويل النص إلى كلام من Kokoro"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr ""
"محرك تحويل النص إلى كلام مفتوح المصدر خفيف وسريع. ~3 جيجابايت من التبعيات، "
"نموذج بحجم 400 ميجابايت"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "ElevenLabs TTS"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "تحويل النص إلى كلام بصوت طبيعي"

#: src/constants.py:179 src/constants.py:180
msgid "OpenAI TTS"
msgstr "OpenAI TTS"

#: src/constants.py:185
#, fuzzy
msgid "Groq TTS"
msgstr "Groq TTS"

#: src/constants.py:186
msgid "Groq TTS API"
msgstr "واجهة برمجة تطبيقات Groq TTS"

#: src/constants.py:191 src/constants.py:192
msgid "Custom OpenAI TTS"
msgstr "تحويل النص إلى كلام مخصص من OpenAI"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "Espeak TTS"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "تحويل النص إلى كلام دون اتصال"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr "استخدم أمرًا مخصصًا لتحويل النص إلى كلام، وسيتم استبدال {0} بالنص"

#: src/constants.py:212
msgid "WordLlama"
msgstr "وورد لاما"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr ""
"نموذج تضمين محلي خفيف يعتمد على لاما. يعمل دون اتصال بالإنترنت، واستهلاك "
"منخفض جدًا للموارد"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "تضمين أولاما"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr ""
"استخدم نماذج أولاما للتضمين. يعمل دون اتصال بالإنترنت، واستهلاك منخفض جدًا "
"للموارد"

#: src/constants.py:231
msgid "Use Google Gemini API to get embeddings"
msgstr "استخدم واجهة برمجة تطبيقات جوجل جيميني للحصول على التضمينات"

#: src/constants.py:239
msgid "User Summary"
msgstr "ملخص المستخدم"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "توليد ملخص لمحادثة المستخدم"

#: src/constants.py:245
msgid "Memoripy"
msgstr "مميموريبي"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"استخراج الرسائل من المحادثات السابقة باستخدام استرداد الذاكرة السياقية، "
"تلاشي الذاكرة، استخراج المفاهيم وتقنيات متقدمة أخرى. يقوم باستدعاء LLM واحد "
"لكل رسالة."

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "ملخص المستخدم + ميموريبي"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "استخدم كلا التقنيتين للذاكرة طويلة الأمد"

#: src/constants.py:260
msgid "Document reader"
msgstr "قارئ المستندات"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr ""
"النهج التقليدي لـ RAG - تقسيم المستندات وتضمينها، ثم مقارنتها بالاستعلام "
"وإرجاع المستندات الأكثر صلة"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr "SearXNG - محرك بحث خاص وقابل للاستضافة الذاتية"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "بحث DuckDuckGo"

#: src/constants.py:281
msgid "Tavily"
msgstr "تافيلي"

#: src/constants.py:282
msgid "Tavily search"
msgstr "بحث تافيلي"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "مساعد مفيد"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr "موجه عام الغرض لتعزيز إجابات LLM وتوفير المزيد من السياق"

#: src/constants.py:384
msgid "Console access"
msgstr "الوصول إلى وحدة التحكم"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "هل يمكن للبرنامج تشغيل أوامر الطرفية على الكمبيوتر"

#: src/constants.py:392
msgid "Current directory"
msgstr "الدليل الحالي"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "ما هو الدليل الحالي"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "السماح لـ LLM بالبحث على الإنترنت"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "وظائف أساسية"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "عرض الجداول والرموز (*يمكن أن يعمل بدونها)"

#: src/constants.py:419
msgid "Graphs access"
msgstr "الوصول إلى الرسوم البيانية"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "هل يمكن للبرنامج عرض الرسوم البيانية"

#: src/constants.py:428
msgid "Show image"
msgstr "عرض الصورة"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "عرض الصورة في الدردشة"

#: src/constants.py:437
#, fuzzy
msgid "Custom Prompt"
msgstr "موجه مخصص"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "أضف موجهك المخصص"

#: src/constants.py:480
#, fuzzy
msgid "LLM and Secondary LLM settings"
msgstr "إعدادات LLM و LLM الثانوي"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "تحويل النص إلى كلام"

#: src/constants.py:485
msgid "Text to Speech settings"
msgstr "إعدادات تحويل النص إلى كلام"

#: src/constants.py:488
msgid "STT"
msgstr "تحويل الكلام إلى نص"

#: src/constants.py:490
msgid "Speech to Text settings"
msgstr "إعدادات تحويل الكلام إلى نص"

#: src/constants.py:493
msgid "Embedding"
msgstr "التضمين"

#: src/constants.py:495
#, fuzzy
msgid "Embedding settings"
msgstr "إعدادات التضمين"

#: src/constants.py:498
msgid "Memory"
msgstr "الذاكرة"

#: src/constants.py:500
#, fuzzy
msgid "Memory settings"
msgstr "إعدادات الذاكرة"

#: src/constants.py:503
msgid "Websearch"
msgstr "البحث على الويب"

#: src/constants.py:505
#, fuzzy
msgid "Websearch settings"
msgstr "إعدادات البحث على الويب"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
msgid "Document analyzer settings"
msgstr "إعدادات محلل المستندات"

#: src/constants.py:515
#, fuzzy
msgid "Extensions settings"
msgstr "إعدادات الإضافات"

#: src/constants.py:518
#, fuzzy
msgid "Inteface"
msgstr "واجهة"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr "إعدادات الواجهة، الملفات المخفية، الترتيب العكسي، التكبير/التصغير..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr ""
"الإعدادات العامة، المحاكاة الافتراضية، العروض، طول الذاكرة، توليد اسم "
"الدردشة تلقائيًا، المجلد الحالي..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr "إعدادات الموجهات، موجه إضافي مخصص، موجهات مخصصة..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "دردشة "

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "عمليات الطرفية لا تزال قيد التشغيل في الخلفية"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "عند إغلاق النافذة، سيتم إنهاؤها تلقائيًا"

#: src/main.py:210
msgid "Close"
msgstr "إغلاق"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "تم إعادة تشغيل الدردشة"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "تمت إعادة تشغيل المجلد"

#: src/main.py:254
msgid "Chat is created"
msgstr "تم إنشاء دردشة"

#: src/window.py:120
msgid "Keyboard shorcuts"
msgstr "اختصارات لوحة المفاتيح"

#: src/window.py:121
msgid "About"
msgstr "حول"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "دردشة"

#: src/window.py:170
msgid "History"
msgstr "السجل"

#: src/window.py:191
msgid "Create a chat"
msgstr "إنشاء دردشة"

#: src/window.py:196
#, fuzzy
msgid "Chats"
msgstr "الدردشات"

#: src/window.py:267
msgid " Stop"
msgstr " إيقاف"

#: src/window.py:282
msgid " Clear"
msgstr " مسح"

#: src/window.py:297
msgid " Continue"
msgstr " متابعة"

#: src/window.py:310
msgid " Regenerate"
msgstr "إعادة توليد"

#: src/window.py:376
msgid "Send a message..."
msgstr "أرسل رسالة..."

#: src/window.py:467
msgid "Explorer Tab"
msgstr "علامة تبويب المستكشف"

#: src/window.py:468
msgid "Terminal Tab"
msgstr "علامة تبويب الطرفية"

#: src/window.py:469
msgid "Browser Tab"
msgstr "علامة تبويب المتصفح"

#: src/window.py:589
msgid "Ask about a website"
msgstr "اسأل عن موقع ويب"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr "اكتب #https://website.com في الدردشة لطلب معلومات عن موقع ويب"

#: src/window.py:590
msgid "Check out our Extensions!"
msgstr "اطلع على إضافاتنا!"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "لدينا العديد من الإضافات لأشياء مختلفة. تحقق منها!"

#: src/window.py:591
msgid "Chat with documents!"
msgstr "الدردشة مع المستندات!"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr ""
"أضف مستنداتك إلى مجلد المستندات الخاص بك ودردش باستخدام المعلومات الموجودة "
"فيها!"

#: src/window.py:592
msgid "Surf the web!"
msgstr "تصفح الويب!"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr ""
"مكّن البحث على الويب للسماح لنموذج اللغة الكبيرة بتصفح الويب وتقديم إجابات "
"محدثة"

#: src/window.py:593
msgid "Mini Window"
msgstr "نافذة مصغرة"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "اطرح الأسئلة بسرعة باستخدام وضع النافذة المصغرة"

#: src/window.py:594
msgid "Text to Speech"
msgstr "تحويل النص إلى كلام"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr "Newelle يدعم تحويل النص إلى كلام! قم بتمكينه في الإعدادات"

#: src/window.py:595
#, fuzzy
msgid "Keyboard Shortcuts"
msgstr "اختصارات لوحة المفاتيح"

#: src/window.py:595
#, fuzzy
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "التحكم في Newelle باستخدام اختصارات لوحة المفاتيح"

#: src/window.py:596
#, fuzzy
msgid "Prompt Control"
msgstr "التحكم في الموجه"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr "نيويل يمنحك 100% تحكم في الموجهات. اضبط موجهاتك لاستخدامك الخاص."

#: src/window.py:597
#, fuzzy
msgid "Thread Editing"
msgstr "تعديل الخيوط"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "تحقق من البرامج والعمليات التي تشغلها من Newelle"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr "موجهات قابلة للبرمجة"

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr "يمكنك إضافة موجهات ديناميكية إلى Newelle، مع شروط واحتِمَالات"

#: src/window.py:605
#, fuzzy
msgid "New Chat"
msgstr "دردشة جديدة"

#: src/window.py:623
msgid "Provider Errror"
msgstr "خطأ في المزود"

#: src/window.py:646
msgid "Local Documents"
msgstr "المستندات المحلية"

#: src/window.py:650
msgid "Web search"
msgstr "بحث الويب"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "هذا المزود لا يملك قائمة بالنماذج"

#: src/window.py:901
msgid " Models"
msgstr " نماذج"

#: src/window.py:904
msgid "Search Models..."
msgstr "البحث عن نماذج..."

#: src/window.py:1132
msgid "Create new profile"
msgstr "إنشاء ملف شخصي جديد"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "لم يتم التعرف على صوتك"

#: src/window.py:1303
msgid "Images"
msgstr "الصور"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "ملفات مدعومة من LLM"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "ملفات مدعومة من RAG"

#: src/window.py:1333
msgid "Supported Files"
msgstr "الملفات المدعومة"

#: src/window.py:1337
msgid "All Files"
msgstr "جميع الملفات"

#: src/window.py:1343
msgid "Attach file"
msgstr "إرفاق ملف"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "لا يمكن إرسال الملف حتى ينتهي البرنامج"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "الملف غير معروف"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "لا يمكنك متابعة الرسالة."

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "لا يمكنك إعادة توليد الرسالة."

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "تم مسح الدردشة"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "تم إلغاء الرسالة وحذفها من السجل"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr "لا يمكن إرسال الرسالة حتى ينتهي البرنامج"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr "لا يمكنك تعديل رسالة أثناء تشغيل البرنامج."

#: src/window.py:3080
#, fuzzy
msgid "Prompt content"
msgstr "محتوى الموجه"

#: src/window.py:3339
#, fuzzy
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"الشبكة العصبية لديها إمكانية الوصول إلى جهاز الكمبيوتر الخاص بك وأي بيانات "
"في هذه الدردشة ويمكنها تشغيل الأوامر، كن حذرًا، نحن لسنا مسؤولين عن الشبكة "
"العصبية. لا تشارك أي معلومات حساسة."

#: src/window.py:3368
#, fuzzy
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"الشبكة العصبية لديها إمكانية الوصول إلى أي بيانات في هذه الدردشة، كن حذرًا، "
"نحن لسنا مسؤولين عن الشبكة العصبية. لا تشارك أي معلومات حساسة."

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "مسار المجلد خاطئ"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "لم يتم إكمال الخيط، رقم الخيط: "

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "فشل فتح المجلد"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "الدردشة فارغة"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr ""
"درّب نيويل على فعل المزيد باستخدام الإضافات المخصصة ووحدات الذكاء الاصطناعي "
"الجديدة، مما يمنح روبوت الدردشة الخاص بك إمكانيات لا حصر لها."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "روبوت الدردشة بالذكاء الاصطناعي"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "تحديد سريع للملف الشخصي"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
#, fuzzy
msgid "Message Editing"
msgstr "تعديل الرسائل"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "أكثر من 10 مزودي ذكاء اصطناعي قياسيين"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "إصلاحات الأخطاء"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
#, fuzzy
msgid "Minor bug fixes"
msgstr "إصلاحات الأخطاء"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "تحسينات صغيرة"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "تحسين أداء قراءة وتحميل المستندات المحلية"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "إضافة خيار الإرسال باستخدام CTRL+Enter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "تحسين كتل التعليمات البرمجية"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
msgid "Fix Kokoro TTS"
msgstr "إصلاح Kokoro TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "إزالة الرموز التعبيرية من تحويل النص إلى كلام"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "تعيين مفاتيح API كحقول كلمة مرور"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "إضافة دعم التفكير لـ Gemini"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "تحديث الترجمات"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "تمت إضافة ميزات جديدة"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr "قراءة المواقع والبحث على الويب باستخدام SearXNG، DuckDuckGo، و Tavily"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "تحسين عرض LaTeX وإدارة المستندات"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "عنصر واجهة مستخدم جديد للتفكير ومعالج OpenRouter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "دعم الرؤية لـ Llama4 على Groq"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "ترجمات جديدة (الصينية التقليدية، البنغالية، الهندية)"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "تم إصلاح العديد من الأخطاء، وإضافة بعض الميزات!"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "دعم للميزات الجديدة وإصلاح الأخطاء"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "تمت إضافة العديد من الميزات الجديدة وإصلاح الأخطاء"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "تمت إضافة ميزات جديدة وإصلاحات للأخطاء"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr ""
"تم تحديث مكتبة g4f مع إضافة تحديد الإصدار، وإضافة أدلة المستخدم، وتحسين تصفح "
"الإضافات، وتعزيز التعامل مع النماذج."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"تم تنفيذ إصلاحات الأخطاء والميزات الجديدة. لقد قمنا بتعديل بنية الإضافات، "
"وإضافة نماذج جديدة، وتقديم دعم الرؤية، بالإضافة إلى المزيد من القدرات."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "إصدار مستقر"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
#, fuzzy
msgid "Added extension"
msgstr "تمت إضافة إضافة"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
#, fuzzy
msgid "Blacklist of commands"
msgstr "قائمة سوداء للأوامر"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "الترجمة"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "إعادة تصميم"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "كفيرسيك"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "نيويل: روبوت الدردشة المتقدم الخاص بك"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "ذكاء اصطناعي;مساعد;دردشة;شات جي بي تي;جي بي تي;نموذج لغة كبيرة;أولاما;"

#~ msgid "max Tokens"
#~ msgstr "الحد الأقصى للرموز"

#~ msgid "Max tokens of the generated text"
#~ msgstr "الحد الأقصى لرموز النص المولّد"

#, fuzzy
#~ msgid "_Cancel"
#~ msgstr "إلغاء"

#~ msgid "Choose an extension"
#~ msgstr "اختر إضافة"

#~ msgid " has been removed"
#~ msgstr "تمت إزالته"

#~ msgid "Extension added. New extensions will run from the next launch"
#~ msgstr "تمت إضافة الإضافة. ستعمل الإضافات الجديدة من الإطلاق التالي"

#~ msgid "The extension is wrong"
#~ msgstr "الإضافة خاطئة"

#~ msgid "This is not an extension"
#~ msgstr "هذه ليست إضافة"

#~ msgid "Chat has been stopped"
#~ msgstr "تم إيقاف الدردشة"

#~ msgid "The change will take effect after you restart the program."
#~ msgstr "سيسري التغيير بعد إعادة تشغيل البرنامج."
