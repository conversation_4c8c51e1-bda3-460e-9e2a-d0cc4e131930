<?xml version="1.0" encoding="UTF-8"?>
<component type="desktop">
	<id>io.github.qwersyk.Newelle.desktop</id>
	<metadata_license>CC0-1.0</metadata_license>
	<project_license>GPL-3.0-or-later</project_license>
        <launchable type="desktop-id">io.github.qwersyk.Newelle.desktop</launchable>
        <name>Newelle</name>
	<description>
	  <p>Train Newelle to do more with custom extensions and new AI modules, giving your chatbot endless possibilities.</p>
	</description>
	<summary>AI chatbot</summary>
	<screenshots>
		<screenshot type="default">
			<image>https://raw.githubusercontent.com/qwersyk/Newelle/master/data/screenshots/screenshot1.png</image>
			<caption>Quick profile selection</caption>
		</screenshot>
		<screenshot>
			<image>https://raw.githubusercontent.com/qwersyk/Newelle/master/data/screenshots/screenshot2.png</image>
			<caption>Message Editing</caption>
		</screenshot>
 		<screenshot>
			<image>https://raw.githubusercontent.com/qwersyk/Newelle/master/data/screenshots/screenshot3.png</image>
			<caption>More than 10 standard AI providers</caption>
		</screenshot>
	</screenshots>
  	<branding>
    		<color type="primary" scheme_preference="light">#99c1f1</color>
    		<color type="primary" scheme_preference="dark">#003d81</color>
  	</branding>
	<url type="homepage">https://newelle.qsk.me</url>
  	<url type="vcs-browser">https://github.com/qwersyk/Newelle</url>
	<url type="bugtracker">https://github.com/qwersyk/Newelle/issues</url>
	<content_rating type="oars-1.0" />
	<releases>
	    <release version="1.0.1" date="2025-08-03">
            <description>
                <ul>
                    <li>Bug fixes</li>
                </ul>
            </description>
        </release>
	    <release version="1.0" date="2025-07-29">
            <description>
                <ul>
                    <li>Mini Apps support! Extensions can now show custom mini apps on the sidebar</li>
                    <li>Added integrated browser Mini App: browse the web directly in Newelle and attach web pages</li>
                    <li>Improved integrated file manager, supporting multiple file operations</li>
                    <li>Integrated file editor: edit files and codeblocks directly in Newelle</li>
                    <li>Integrated Terminal mini app: open the terminal directly in Newelle</li>
                    <li>Programmable prompts: add dynamic content to prompts with conditionals and random strings</li>
                    <li>Add ability to manually edit chat name</li>
                    <li>Minor bug fixes</li>
                    <li>Added support for multiple languages for Kokoro TTS and Whisper.CPP</li>
                    <li>Run HTML/CSS/JS websites directly in app</li>
                    <li>New animation on chat change</li>
                </ul>
            </description>
        </release>
	    <release version="0.9.8" date="2025-06-03">
            <description>
                <ul>
                  <li>Bug fixes</li>
                  <li>Small improvements</li>
                </ul>
            </description>
        </release>
	    <release version="0.9.7" date="2025-05-24">
            <description>
                <ul>
                  <li>Improve local documents reading and loading performances</li>
                  <li>Add option to send with CTRL+Enter</li>
                  <li>Improve codeblocks</li>
                  <li>Fix Kokoro TTS</li>
                  <li>Remove emoji from TTS</li>
                  <li>Set API keys as password fields</li>
                  <li>Add thinking support for Gemini</li>
                  <li>Updated translations</li>
                </ul>
            </description>
        </release>
	    <release version="0.9.6" date="2025-05-10">
            <description>
                <ul>
                  <li>Added new features</li>
                  <li>Bug fixes</li>
                </ul>
            </description>
        </release>
	    <release version="0.9.5" date="2025-04-26">
            <description>
                <ul>
                  <li>Website reading and web search with SearXNG, DuckDuckGo, and Tavily</li>
                  <li>Improved LaTeX rendering and document management</li>
                  <li>New Thinking Widget and OpenRouter handler</li>
                  <li>Vision support for Llama4 on Groq</li>
                  <li>New translations (Traditional Chinese, Bengali, Hindi)</li>
                </ul>
            </description>
        </release>
	    <release version="0.9.0" date="2025-04-10">
            <description>
                <p>Fixed many bugs, added some features!</p>
            </description>
        </release>
	    <release version="0.8.1" date="2025-03-16">
            <description>
                <p>Support for new features and bug fixes</p>
            </description>
        </release>
	    <release version="0.8.0" date="2025-03-11">
            <description>
                <p>Added many new features and bug fixes</p>
            </description>
        </release>
	    <release version="0.7.0" date="2025-02-06">
            <description>
                <p>Added many new features and bug fixes</p>
            </description>
        </release>
	    <release version="0.6.0" date="2025-01-04">
            <description>
                <p>Added many new features and bug fixes</p>
            </description>
        </release>
	    <release version="0.5.0" date="2024-12-27">
            <description>
                <p>Added new features and bug fixes</p>
            </description>
        </release>
	    <release version="0.4.2" date="2024-12-21">
            <description>
                <p>Added new features and bug fixes</p>
            </description>
        </release>
	    <release version="0.4.1" date="2024-11-16">
            <description>
                <p>Updated the g4f library with versioning, added user guides, improved extension browsing, and enhanced model handling.</p>
            </description>
        </release>
	    <release version="0.4.0" date="2024-11-02">
            <description>
                <p>Bug fixes and new features have been implemented. We've modified the extension architecture, added new models, and introduced vision support, along with more capabilities.</p>
            </description>
        </release>
	    <release version="0.3.1" date="2024-09-28">
            <description>
                <p>Bug fixes</p>
            </description>
        </release>
	    <release version="0.3" date="2024-08-28">
            <description>
                <p>Bug fixes</p>
            </description>
        </release>
 		<release version="0.2.2" date="2024-08-06">
			<description>
				<p>Bug fixes</p>
			</description>
		</release>
 		 <release version="0.2.1" date="2024-08-03">
			<description>
				<p>Bug fixes</p>
			</description>
		</release>
 		<release version="0.2.0" date="2024-08-03">
			<description>
				<p>Bug fixes</p>
			</description>
		</release>
		<release version="0.1.7" date="2023-07-11">
			<description>
				<p>Stable version</p>
			</description>
		</release>
		<release version="0.1.6" date="2023-07-08">
			<description>
				<p>Added extension</p>
			</description>
		</release>
		<release version="0.1.5" date="2023-07-04">
			<description>
				<p>Blacklist of commands</p>
			</description>
		</release>
		<release version="0.1.4" date="2023-06-30">
			<description>
				<p>Localization</p>
			</description>
		</release>
		<release version="0.1.3" date="2023-06-25">
			<description>
				<p>Redesign</p>
			</description>
		</release>
	</releases>
	<developer_name>Qwersyk</developer_name>
	<update_contact><EMAIL></update_contact>
</component>
