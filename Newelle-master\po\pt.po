msgid ""
msgstr ""
"Project-Id-Version: 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-07-03 09:00+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Portuguese <<EMAIL>>\n"
"Language: pt\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "Ponto de extremidade da API"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr "URL base da API, altere para usar APIs de interferência"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid "Automatically Serve"
msgstr "Servir Automaticamente"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"Executa automaticamente o ollama serve em segundo plano quando necessário, "
"se não estiver em execução. Você pode encerrá-lo com killall ollama"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
msgid "Custom Model"
msgstr "Modelo Personalizado"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
msgid "Use a custom model"
msgstr "Usar um modelo personalizado"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
msgid "Ollama Model"
msgstr "Modelo Ollama"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
msgid "Name of the Ollama Model"
msgstr "Nome do Modelo Ollama"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "Chave da API"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
msgid "API Key for "
msgstr "Chave da API para "

#: src/handlers/embeddings/openai_handler.py:38
msgid "API base url, change this to use different APIs"
msgstr "URL base da API, altere para usar APIs diferentes"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
msgid "Use Custom Model"
msgstr "Usar Modelo Personalizado"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "Modelo"

#: src/handlers/embeddings/openai_handler.py:44
msgid "Name of the Embedding Model to use"
msgstr "Nome do Modelo de Incorporação a ser usado"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
msgid " Model"
msgstr " Modelo"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
msgid "The API key to use"
msgstr "A chave da API a ser usada"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
msgid "The model to use"
msgstr "O modelo a ser usado"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "Tokens Máximos"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "O número máximo de tokens a serem gerados"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
msgid "Message Streaming"
msgstr "Fluxo de Mensagens"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "Transmitir gradualmente a saída da mensagem"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "Comando a ser executado para obter a saída do bot"

#: src/handlers/llm/custom_handler.py:21
#, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr ""
"Comando a ser executado para obter a resposta do bot, {0} será substituído "
"por um arquivo JSON contendo o chat, {1} pelo prompt do sistema"

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "Comando a ser executado para obter as sugestões do bot"

#: src/handlers/llm/custom_handler.py:22
#, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"Comando a ser executado para obter sugestões de chat, {0} será substituído "
"por um arquivo JSON contendo o chat, {1} pelos prompts extras, {2} pelo "
"número de sugestões a serem geradas. Deve retornar um array JSON contendo as "
"sugestões como strings"

#: src/handlers/llm/gpt4all_handler.py:67
msgid "RAM Required: "
msgstr "RAM Necessária: "

#: src/handlers/llm/gpt4all_handler.py:68
msgid "Parameters: "
msgstr "Parâmetros: "

#: src/handlers/llm/gpt4all_handler.py:69
msgid "Size: "
msgstr "Tamanho: "

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
msgid "Model to use"
msgstr "Modelo a ser usado"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
msgid "Name of the model to use"
msgstr "Nome do modelo a ser usado"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "Model Manager"
msgstr "Gerenciador de Modelos"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "Lista de modelos disponíveis"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr "Atualizar G4F"

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "Política de Privacidade"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "Abrir site da política de privacidade"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "Habilitar Pensamento"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr "Permitir pensar no modelo, apenas alguns modelos são suportados"

#: src/handlers/llm/ollama_handler.py:176
msgid "Add custom model"
msgstr "Adicionar modelo personalizado"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"Adicione qualquer modelo a esta lista inserindo nome:tamanho\n"
"Ou qualquer gguf de hf com hf.co/username/model"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr "Atualizar Ollama"

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "Chave da API (obrigatório)"

#: src/handlers/llm/gemini_handler.py:94
msgid "API key got from ai.google.dev"
msgstr "Chave da API obtida em ai.google.dev"

#: src/handlers/llm/gemini_handler.py:98
msgid "AI Model to use"
msgstr "Modelo de IA a usar"

#: src/handlers/llm/gemini_handler.py:103
msgid "Enable System Prompt"
msgstr "Habilitar Prompt do Sistema"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr ""
"Alguns modelos não suportam prompt do sistema (ou instruções de "
"desenvolvedores), desabilite-o se você receber erros sobre isso"

#: src/handlers/llm/gemini_handler.py:107
msgid "Inject system prompt"
msgstr "Injetar prompt do sistema"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr ""
"Mesmo que o modelo não suporte prompts do sistema, coloque os prompts no "
"topo da mensagem do usuário"

#: src/handlers/llm/gemini_handler.py:109
msgid "Thinking Settings"
msgstr "Configurações de Pensamento"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "Configurações sobre modelos de pensamento"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr "Mostrar pensamento, desabilite se o seu modelo não o suportar"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "Habilitar Orçamento de Pensamento"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "Se deve habilitar o orçamento de pensamento"

#: src/handlers/llm/gemini_handler.py:112
msgid "Thinking Budget"
msgstr "Orçamento de Pensamento"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "Quanto tempo gastar pensando"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "Saída de Imagem"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr "Habilitar saída de imagem, apenas suportado por gemini-2.0-flash-exp"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "Habilitar configurações de segurança"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr ""
"Habilitar configurações de segurança do Google para evitar a geração de "
"conteúdo prejudicial"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
msgid "Advanced Parameters"
msgstr "Parâmetros Avançados"

#: src/handlers/llm/gemini_handler.py:135
msgid "Enable advanced parameters"
msgstr "Habilitar parâmetros avançados"

#: src/handlers/llm/openai_handler.py:81
#, fuzzy
msgid "Include parameters like Top-P, Temperature, etc."
msgstr "Incluir parâmetros como Max Tokens, Top-P, Temperatura, etc."

#: src/handlers/llm/openai_handler.py:84
msgid "Name of the LLM Model to use"
msgstr "Nome do Modelo LLM a ser usado"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "Top-P"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr ""
"Uma alternativa à amostragem com temperatura, chamada amostragem de núcleo"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "Temperatura"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr ""
"Qual temperatura de amostragem usar. Valores mais altos tornarão a saída "
"mais aleatória"

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "Penalidade de Frequência"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr ""
"Número entre -2.0 e 2.0. Valores positivos diminuem a probabilidade de o "
"modelo repetir a mesma linha palavra por palavra"

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "Penalidade de Presença"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr ""
"Número entre -2.0 e 2.0. Valores positivos diminuem a probabilidade de o "
"modelo falar sobre novos tópicos"

#: src/handlers/llm/openai_handler.py:108
#, fuzzy
msgid "Custom Options"
msgstr "Prompt Personalizado"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr ""

#: src/handlers/llm/openrouter_handler.py:14
msgid "Provider Sorting"
msgstr "Classificação de Provedores"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "Escolha provedores com base em preço/desempenho ou latência"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "Preço"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "Taxa de Transferência"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "Latência"

#: src/handlers/llm/openrouter_handler.py:15
msgid "Providers Order"
msgstr "Ordem dos Provedores"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr ""
"Adicione a ordem dos provedores a serem usados, nomes separados por "
"vírgula.\n"
"Vazio para não especificar"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "Permitir Fallbacks"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "Permitir fallbacks para outros provedores"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "Indexar seus documentos"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"Indexar todos os documentos em sua pasta de documentos. Você deve executar "
"esta operação toda vez que editar/criar um documento, mudar o analisador de "
"documentos ou mudar o modelo de incorporação"

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "Comando a ser executado"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "{0} será substituído pelo caminho completo do modelo"

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr "Chave da API para Google SR, escreva 'default' para usar a padrão"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "Idioma"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "O idioma do texto a ser reconhecido em IETF"

#: src/handlers/stt/groqsr_handler.py:14
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr "Chave da API para Groq SR, escreva 'default' para usar a padrão"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "Modelo Groq"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "Nome do Modelo Groq"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Especifique o idioma para transcrição. Use códigos de idioma ISO 639-1 (por "
"exemplo, \"en\" para inglês, \"fr\" para francês, etc.). "

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "Ponto de extremidade para solicitações da OpenAI"

#: src/handlers/stt/openaisr_handler.py:18
msgid "API Key for OpenAI"
msgstr "Chave da API para OpenAI"

#: src/handlers/stt/openaisr_handler.py:25
msgid "Whisper Model"
msgstr "Modelo Whisper"

#: src/handlers/stt/openaisr_handler.py:26
msgid "Name of the OpenAI model"
msgstr "Nome do modelo OpenAI"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Opcional: Especifique o idioma para transcrição. Use códigos de idioma ISO "
"639-1 (por exemplo, \"en\" para inglês, \"fr\" para francês, etc.). "

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "Caminho do Modelo"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "Caminho absoluto para o modelo VOSK (descompactado)"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
msgid "Name of the Whisper model"
msgstr "Nome do modelo Whisper"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "Token de Acesso ao Servidor para wit.ai"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "Não foi possível entender o áudio"

#: src/handlers/stt/whispercpp_handler.py:47
#, fuzzy
msgid "Language of the recognition. For example en, it..."
msgstr "Idioma do reconhecimento."

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Model Library"
msgstr "Biblioteca de Modelos"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Manage Whisper models"
msgstr "Gerenciar modelos Whisper"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "Advanced Settings"
msgstr "Configurações Avançadas"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "More advanced settings"
msgstr "Configurações mais avançadas"

#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature to use"
msgstr "Temperatura a usar"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt for the recognition"
msgstr "Prompt para o reconhecimento"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr "Prompt a ser usado para o reconhecimento"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Endpoint"
msgstr "Ponto de Extremidade"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "Ponto de extremidade personalizado do serviço a ser usado"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "Voz"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
msgid "The voice to use"
msgstr "A voz a usar"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "Instruções"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr ""
"Instruções para a geração de voz. Deixe em branco para evitar este campo"

#: src/handlers/tts/custom_handler.py:17
#, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr "{0} será substituído pelo caminho completo do arquivo, {1} pelo texto"

#: src/handlers/tts/elevenlabs_handler.py:11
msgid "API Key for ElevenLabs"
msgstr "Chave da API para ElevenLabs"

#: src/handlers/tts/elevenlabs_handler.py:19
msgid "Voice ID to use"
msgstr "ID de Voz a usar"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "Estabilidade"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "estabilidade da voz"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "Aumento de similaridade"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "Aumenta a clareza geral da voz e a similaridade do locutor"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "Exagero de estilo"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr "Valores altos são recomendados se o estilo da fala deve ser exagerado"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "Escolha a voz preferida"

#: src/handlers/websearch/tavily.py:20
msgid "Token"
msgstr "Token"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "Chave da API Tavily"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "Resultados Máximos"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "Número de resultados a serem considerados"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "A profundidade da pesquisa"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"A profundidade da pesquisa. A pesquisa avançada é adaptada para recuperar as "
"fontes mais relevantes e trechos de conteúdo para sua consulta, enquanto a "
"pesquisa básica fornece trechos de conteúdo genéricos de cada fonte. Uma "
"pesquisa básica custa 1 Crédito de API, enquanto uma pesquisa avançada custa "
"2 Créditos de API."

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "A categoria da pesquisa"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"A categoria da pesquisa. Notícias é útil para recuperar atualizações em "
"tempo real, particularmente sobre política, esportes e grandes eventos "
"atuais cobertos por fontes de mídia convencionais. Geral é para pesquisas "
"mais amplas e de propósito geral que podem incluir uma ampla gama de fontes."

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "Pedaços por fonte"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr ""
"O número de pedaços de conteúdo a serem recuperados de cada fonte. O "
"comprimento de cada pedaço é de no máximo 500 caracteres. Disponível apenas "
"quando a profundidade da pesquisa é avançada."

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "Número de dias anteriores à data atual para incluir"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "Disponível apenas se o tópico for notícias."

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "Incluir resposta"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"Inclua uma resposta gerada por LLM para a consulta fornecida. A pesquisa "
"básica retorna uma resposta rápida. A avançada retorna uma resposta mais "
"detalhada."

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "Incluir conteúdo bruto"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr ""
"Incluir o conteúdo HTML limpo e analisado de cada resultado de pesquisa."

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "Incluir imagens"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr "Realize uma pesquisa de imagem e inclua os resultados na resposta."

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "Incluir descrições de imagens"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr ""
"Quando a opção Incluir imagens estiver ativada, adicione também um texto "
"descritivo para cada imagem."

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "Incluir domínios"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr ""
"Uma lista de domínios a serem incluídos especificamente nos resultados da "
"pesquisa."

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "Excluir domínios"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr ""
"Uma lista de domínios a serem especificamente excluídos dos resultados da "
"pesquisa."

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "Região"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "Região para os resultados da pesquisa"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "Configurações"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "Nome do Perfil"

#: src/ui/profile.py:58
msgid "Copied Settings"
msgstr "Configurações Copiadas"

#: src/ui/profile.py:58
msgid "Settings that will be copied to the new profile"
msgstr "Configurações que serão copiadas para o novo perfil"

#: src/ui/profile.py:70
msgid "Create Profile"
msgstr "Criar Perfil"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "Importar Perfil"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
msgid "Edit Profile"
msgstr "Editar Perfil"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "Exportar Perfil"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "Exportar Senhas"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "Também exportar campos semelhantes a senhas"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "Exportar Propic"

#: src/ui/profile.py:89
msgid "Also export the profile picture"
msgstr "Também exportar a foto de perfil"

#: src/ui/profile.py:109 src/ui/explorer.py:692
msgid "Create"
msgstr "Criar"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "Aplicar"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr "As configurações do perfil atual serão copiadas para o novo"

#: src/ui/profile.py:122 src/ui/profile.py:128
msgid "Newelle Profiles"
msgstr "Perfis Newelle"

#: src/ui/profile.py:123
msgid "Export"
msgstr "Exportar"

#: src/ui/profile.py:129
msgid "Import"
msgstr "Importar"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "Definir foto de perfil"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "Edição de thread"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "Nenhuma thread está em execução"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "Número da thread: "

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "Selecionar perfil"

#: src/ui/widgets/profilerow.py:53
msgid "Delete Profile"
msgstr "Excluir Perfil"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "Pensamentos"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "Expandir para ver detalhes"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "Pensando..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr "O LLM está pensando... Expanda para ver o processo de pensamento"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "Nenhum processo de pensamento registrado"

#: src/ui/widgets/tipscarousel.py:41
msgid "Newelle Tips"
msgstr "Dicas da Newelle"

#: src/ui/explorer.py:192
msgid "Folder is Empty"
msgstr "A Pasta está Vazia"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr "Arquivo não encontrado"

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "Abrir em nova aba"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "Abrir no editor integrado"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "Abrir no gerenciador de arquivos"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
msgid "Rename"
msgstr "Renomear"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "Excluir"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "Copiar caminho completo"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
msgid "Failed to open file manager"
msgstr "Falha ao abrir o gerenciador de arquivos"

#: src/ui/explorer.py:436
msgid "New name:"
msgstr "Novo nome:"

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "Cancelar"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "Renomeado com sucesso"

#: src/ui/explorer.py:476
#, python-brace-format
msgid "Failed to rename: {}"
msgstr "Falha ao renomear: {}"

#: src/ui/explorer.py:497
msgid "Delete File?"
msgstr "Excluir Arquivo?"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "Tem certeza de que deseja excluir \"{}\"?"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "Excluído com sucesso"

#: src/ui/explorer.py:525
#, python-brace-format
msgid "Failed to delete: {}"
msgstr "Falha ao excluir: {}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "Caminho copiado para a área de transferência"

#: src/ui/explorer.py:542
msgid "Failed to copy path"
msgstr "Falha ao copiar o caminho"

#: src/ui/explorer.py:580
msgid "Create new folder"
msgstr "Criar nova pasta"

#: src/ui/explorer.py:583
msgid "Create new file"
msgstr "Criar novo arquivo"

#: src/ui/explorer.py:586
msgid "Open Terminal Here"
msgstr "Abrir Terminal Aqui"

#: src/ui/explorer.py:640
msgid "Create New Folder"
msgstr "Criar Nova Pasta"

#: src/ui/explorer.py:640
msgid "Folder name:"
msgstr "Nome da pasta:"

#: src/ui/explorer.py:644
msgid "Create New File"
msgstr "Criar Novo Arquivo"

#: src/ui/explorer.py:644
msgid "File name:"
msgstr "Nome do arquivo:"

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "Pasta criada com sucesso"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "Arquivo criado com sucesso"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "Um arquivo ou pasta com esse nome já existe"

#: src/ui/explorer.py:728
msgid "folder"
msgstr "pasta"

#: src/ui/explorer.py:728
msgid "file"
msgstr "arquivo"

#: src/ui/explorer.py:730
#, python-brace-format
msgid "Failed to create {}: {}"
msgstr "Falha ao criar {}: {}"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "Ajuda"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "Atalhos"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "Recarregar chat"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "Recarregar pasta"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "Nova aba"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "Colar Imagem"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "Focar caixa de mensagem"

#: src/ui/shortcuts.py:18
msgid "Start/stop recording"
msgstr "Iniciar/parar gravação"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "Salvar"

#: src/ui/shortcuts.py:20
msgid "Stop TTS"
msgstr "Parar TTS"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr "Aumentar zoom"

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr "Diminuir zoom"

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr "Monitor de Saída do Programa"

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr "Limpar saída"

#: src/ui/stdout_monitor.py:61
msgid "Start/Stop monitoring"
msgstr "Iniciar/Parar monitoramento"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr "Monitoramento: Ativo"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr "Monitoramento: Parado"

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr "Linhas: {}"

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr "Linhas: 0"

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "Extensões"

#: src/ui/extension.py:50
msgid "Installed Extensions"
msgstr "Extensões Instaladas"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "Guia do usuário para Extensões"

#: src/ui/extension.py:89
msgid "Download new Extensions"
msgstr "Baixar novas Extensões"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "Instalar extensão de arquivo..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "Newelle"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "O chat está aberto em uma mini janela"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "Bem-vindo ao Newelle"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "Seu assistente virtual definitivo."

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "Página do Github"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "Escolha seu Modelo de Linguagem de IA favorito"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"Newelle pode ser usada com múltiplos modelos e provedores!\n"
"<b>Nota: É fortemente sugerido ler a página Guia para LLM</b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "Guia para LLM"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "Converse com seus documentos"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr ""
"Newelle pode recuperar informações relevantes de documentos que você envia "
"no chat ou de seus próprios arquivos! Informações relevantes para sua "
"consulta serão enviadas para o LLM."

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "Virtualização de comandos"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"Newelle pode ser usada para executar comandos em seu sistema, mas preste "
"atenção no que você executa! <b>O LLM não está sob nosso controle, então "
"pode gerar código malicioso!</b>\n"
"Por padrão, seus comandos serão <b>virtualizados no ambiente Flatpak</b>, "
"mas preste atenção!"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "Você pode estender as funcionalidades do Newelle usando extensões!"

#: src/ui/presentation.py:136
msgid "Download extensions"
msgstr "Baixar extensões"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "Erro de Permissão"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr ""
"Newelle não possui permissões suficientes para executar comandos em seu "
"sistema."

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "Começar a usar o aplicativo"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "Começar a conversar"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "Geral"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "LLM"

#: src/ui/settings.py:49 src/constants.py:528
msgid "Prompts"
msgstr "Prompts"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "Conhecimento"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "Modelo de Linguagem"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "Outros LLMs"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "Outros provedores LLM disponíveis"

#: src/ui/settings.py:73
msgid "Advanced LLM Settings"
msgstr "Configurações Avançadas do LLM"

#: src/ui/settings.py:77
msgid "Secondary Language Model"
msgstr "Modelo de Linguagem Secundário"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr ""
"Modelo usado para tarefas secundárias, como oferta, nome do chat e geração "
"de memória"

#: src/ui/settings.py:94
msgid "Embedding Model"
msgstr "Modelo de Incorporação"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"A incorporação é usada para transformar texto em vetores. Usado pela Memória "
"de Longo Prazo e RAG. Alterá-lo pode exigir que você reindexe documentos ou "
"redefina a memória."

#: src/ui/settings.py:105 src/window.py:647
msgid "Long Term Memory"
msgstr "Memória de Longo Prazo"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "Manter a memória de conversas antigas"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "Pesquisa na Web"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "Pesquisar informações na Web"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "Programa de Texto para Fala"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "Escolha qual texto para fala usar"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "Mecanismo de Fala para Texto"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "Escolha o mecanismo de reconhecimento de fala que você deseja"

#: src/ui/settings.py:150
msgid "Automatic Speech To Text"
msgstr "Fala para Texto Automática"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr "Reiniciar automaticamente a fala para texto no final de um texto/TTS"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "Controle de Prompt"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "Interface"

#: src/ui/settings.py:162
msgid "Interface Size"
msgstr "Tamanho da Interface"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "Ajustar o tamanho da interface"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "Esquema de cores do editor"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "Mudar o esquema de cores do editor e dos blocos de código"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "Arquivos ocultos"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "Mostrar arquivos ocultos"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "Enviar com ENTER"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"Se ativado, as mensagens serão enviadas com ENTER, para ir para uma nova "
"linha use CTRL+ENTER. Se desativado, as mensagens serão enviadas com "
"SHIFT+ENTER, e a nova linha com ENTER"

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "Remover o pensamento do histórico"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr ""
"Não enviar blocos de pensamento antigos para modelos de raciocínio a fim de "
"reduzir o uso de tokens"

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "Exibir LaTeX"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "Exibir fórmulas LaTeX no chat"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "Inverter Ordem do Chat"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr ""
"Mostrar os chats mais recentes no topo da lista de chats (mude o chat para "
"aplicar)"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "Gerar Nomes de Chat Automaticamente"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr "Gerar nomes de chat automaticamente após as duas primeiras mensagens"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "Número de ofertas"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "Número de sugestões de mensagem para enviar ao chat "

#: src/ui/settings.py:224
msgid "Username"
msgstr "Nome de usuário"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr ""
"Altere o rótulo que aparece antes da sua mensagem\n"
"Esta informação não é enviada para o LLM por padrão\n"
"Você pode adicioná-la a um prompt usando a variável {USER}"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "Controle de Rede Neural"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "Executar comandos em uma máquina virtual"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "Terminal Externo"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr "Escolha o terminal externo onde executar os comandos do console"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "Memória do programa"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "Por quanto tempo o programa se lembra do chat "

#: src/ui/settings.py:266
msgid "Developer"
msgstr "Desenvolvedor"

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr ""
"Monitorar a saída do programa em tempo real, útil para depuração e para ver "
"o progresso dos downloads"

#: src/ui/settings.py:270
msgid "Open"
msgstr "Abrir"

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr "Excluir caminho do pip"

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr "Remover as dependências extras instaladas"

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr ""

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr ""

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "Comandos de execução automática"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "Os comandos que o bot escreverá serão executados automaticamente"

#: src/ui/settings.py:313
msgid "Max number of commands"
msgstr "Número máximo de comandos"

#: src/ui/settings.py:313
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr ""
"Número máximo de comandos que o bot escreverá após uma única solicitação do "
"usuário"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "Navegador"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "Configurações do navegador"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "Usar navegador externo"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr "Usar um navegador externo para abrir links em vez do integrado"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "Persistir sessão do navegador"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr ""
"Persistir a sessão do navegador entre as reinicializações. Desativar isso "
"requer reiniciar o programa"

#: src/ui/settings.py:361
msgid "Delete browser data"
msgstr "Excluir dados do navegador"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "Excluir sessão e dados do navegador"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "Página inicial do navegador"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "A página onde o navegador iniciará"

#: src/ui/settings.py:375
msgid "Search string"
msgstr "String de pesquisa"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr ""
"A string de pesquisa usada no navegador, %s é substituída pela consulta"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "Fontes de Documentos (RAG)"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "Incluir conteúdo de seus documentos nas respostas"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "Analisador de Documentos"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr ""
"O analisador de documentos usa múltiplas técnicas para extrair informações "
"relevantes sobre seus documentos"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "Ler documentos se não suportados"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr ""
"Se o LLM não suportar a leitura de documentos, informações relevantes sobre "
"os documentos enviados no chat serão fornecidas ao LLM usando seu Analisador "
"de Documentos."

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "Tokens máximos para RAG"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"A quantidade máxima de tokens a serem usados para RAG. Se os documentos não "
"excederem essa contagem de tokens,\n"
"descarregar todos eles no contexto"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "Pasta de Documentos"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr ""
"Coloque os documentos que você deseja consultar em sua pasta de documentos. "
"O analisador de documentos encontrará informações relevantes neles se esta "
"opção estiver ativada"

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr "Coloque todos os documentos que você deseja indexar nesta pasta"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "Limiar de silêncio"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr ""
"Limiar de silêncio em segundos, porcentagem do volume a ser considerada "
"silêncio"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "Tempo de silêncio"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr ""
"Tempo de silêncio em segundos antes que a gravação pare automaticamente"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "Permissões insuficientes"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr ""
"Newelle não possui permissões suficientes para executar comandos em seu "
"sistema, por favor, execute o seguinte comando"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "Entendido"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr "Caminho do Pip excluído"

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr ""
"O caminho do pip foi excluído, agora você pode reinstalar as dependências. "
"Esta operação requer a reinicialização do aplicativo."

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "API de Demonstração do Newelle"

#: src/constants.py:26
msgid "GPT4Free"
msgstr "GPT4Free"

#: src/constants.py:34
msgid "Local Model"
msgstr "Modelo Local"

#: src/constants.py:35
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr ""
"SEM SUPORTE A GPU, USE OLLAMA EM VEZ DISSO. Execute um modelo LLM "
"localmente, mais privacidade mas mais lento"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "Instância Ollama"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "Execute facilmente múltiplos modelos LLM em seu próprio hardware"

#: src/constants.py:47
msgid "Groq"
msgstr "Groq"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "API Google Gemini"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "API OpenAI"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr ""
"API OpenAI. Pontos de extremidade personalizados suportados. Use para "
"provedores personalizados"

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "Anthropic Claude"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr ""
"APIs oficiais para os modelos Anthropic Claude, com suporte a imagens e "
"arquivos, requer uma chave de API"

#: src/constants.py:73
msgid "Mistral"
msgstr "Mistral"

#: src/constants.py:74
msgid "Mistral API"
msgstr "API Mistral"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "API Openrouter.ai, suporta muitos modelos"

#: src/constants.py:87
msgid "Deepseek"
msgstr "Deepseek"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "API Deepseek, modelos de código aberto mais fortes"

#: src/constants.py:94 src/constants.py:203
msgid "Custom Command"
msgstr "Comando Personalizado"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "Usar a saída de um comando personalizado"

#: src/constants.py:104
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr "Funciona offline. Implementação otimizada do Whisper escrita em C++"

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "Funciona offline. Apenas inglês suportado"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "Reconhecimento de Fala do Google"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "Reconhecimento de Fala do Google online"

#: src/constants.py:124
msgid "Groq Speech Recognition"
msgstr "Reconhecimento de Fala Groq"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit AI"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr ""
"API gratuita de reconhecimento de fala wit.ai (idioma escolhido no site)"

#: src/constants.py:137
msgid "Vosk API"
msgstr "API Vosk"

#: src/constants.py:138
msgid "Works Offline"
msgstr "Funciona Offline"

#: src/constants.py:144
msgid "Whisper API"
msgstr "API Whisper"

#: src/constants.py:145
msgid "Uses OpenAI Whisper API"
msgstr "Usa a API OpenAI Whisper"

#: src/constants.py:151
msgid "Custom command"
msgstr "Comando personalizado"

#: src/constants.py:152
msgid "Runs a custom command"
msgstr "Executa um comando personalizado"

#: src/constants.py:161
msgid "Google TTS"
msgstr "TTS do Google"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "Texto para fala do Google"

#: src/constants.py:167
msgid "Kokoro TTS"
msgstr "TTS Kokoro"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr ""
"Motor TTS de código aberto leve e rápido. ~3GB de dependências, modelo de "
"400MB"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "TTS ElevenLabs"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "TTS com som natural"

#: src/constants.py:179 src/constants.py:180
msgid "OpenAI TTS"
msgstr "TTS OpenAI"

#: src/constants.py:185
msgid "Groq TTS"
msgstr "TTS Groq"

#: src/constants.py:186
msgid "Groq TTS API"
msgstr "API TTS Groq"

#: src/constants.py:191 src/constants.py:192
msgid "Custom OpenAI TTS"
msgstr "TTS OpenAI Personalizado"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "TTS Espeak"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "TTS Offline"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr "Use um comando personalizado como TTS, {0} será substituído pelo texto"

#: src/constants.py:212
msgid "WordLlama"
msgstr "WordLlama"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr ""
"Modelo de incorporação local leve baseado em llama. Funciona offline, uso de "
"recursos muito baixo"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "Incorporação Ollama"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr ""
"Use modelos Ollama para Incorporação. Funciona offline, uso de recursos "
"muito baixo"

#: src/constants.py:231
msgid "Use Google Gemini API to get embeddings"
msgstr "Use a API Google Gemini para obter embeddings"

#: src/constants.py:239
msgid "User Summary"
msgstr "Resumo do Usuário"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "Gerar um resumo da conversa do usuário"

#: src/constants.py:245
msgid "Memoripy"
msgstr "Memoripy"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"Extrair mensagens de conversas anteriores usando recuperação de memória "
"contextual, decaimento de memória, extração de conceitos e outras técnicas "
"avançadas. Faz 1 chamada LLM por mensagem."

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "Resumo do Usuário + Memoripy"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "Use ambas as tecnologias para memória de longo prazo"

#: src/constants.py:260
msgid "Document reader"
msgstr "Leitor de documentos"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr ""
"Abordagem RAG clássica - dividir documentos em chunks e incorporá-los, "
"depois compará-los com a consulta e retornar os documentos mais relevantes"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr "SearXNG - Motor de busca privado e auto-hospedável"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "Pesquisa DuckDuckGo"

#: src/constants.py:281
msgid "Tavily"
msgstr "Tavily"

#: src/constants.py:282
msgid "Tavily search"
msgstr "Pesquisa Tavily"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "Assistente útil"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr ""
"Prompt de propósito geral para aprimorar as respostas do LLM e fornecer mais "
"contexto"

#: src/constants.py:384
msgid "Console access"
msgstr "Acesso ao console"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "O programa pode executar comandos do terminal no computador"

#: src/constants.py:392
msgid "Current directory"
msgstr "Diretório atual"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "Qual é o diretório atual"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "Permitir que o LLM pesquise na internet"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "Funcionalidade básica"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "Mostrando tabelas e código (*pode funcionar sem)"

#: src/constants.py:419
msgid "Graphs access"
msgstr "Acesso a gráficos"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "O programa pode exibir gráficos"

#: src/constants.py:428
msgid "Show image"
msgstr "Mostrar imagem"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "Mostrar imagem no chat"

#: src/constants.py:437
msgid "Custom Prompt"
msgstr "Prompt Personalizado"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "Adicione seu próprio prompt personalizado"

#: src/constants.py:480
msgid "LLM and Secondary LLM settings"
msgstr "Configurações de LLM e LLM Secundário"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "TTS"

#: src/constants.py:485
msgid "Text to Speech settings"
msgstr "Configurações de Texto para Fala"

#: src/constants.py:488
msgid "STT"
msgstr "STT"

#: src/constants.py:490
msgid "Speech to Text settings"
msgstr "Configurações de Fala para Texto"

#: src/constants.py:493
msgid "Embedding"
msgstr "Incorporação"

#: src/constants.py:495
msgid "Embedding settings"
msgstr "Configurações de incorporação"

#: src/constants.py:498
msgid "Memory"
msgstr "Memória"

#: src/constants.py:500
msgid "Memory settings"
msgstr "Configurações de memória"

#: src/constants.py:503
msgid "Websearch"
msgstr "Pesquisa na web"

#: src/constants.py:505
msgid "Websearch settings"
msgstr "Configurações de pesquisa na web"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
msgid "Document analyzer settings"
msgstr "Configurações do analisador de documentos"

#: src/constants.py:515
msgid "Extensions settings"
msgstr "Configurações de extensões"

#: src/constants.py:518
msgid "Inteface"
msgstr "Interface"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr "Configurações de interface, arquivos ocultos, ordem inversa, zoom..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr ""
"Configurações gerais, virtualização, ofertas, duração da memória, gerar nome "
"do chat automaticamente, pasta atual..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr ""
"Configurações de prompts, prompt extra personalizado, prompts "
"personalizados..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "Chat "

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "Threads do terminal ainda estão em execução em segundo plano"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "Quando você fechar a janela, elas serão automaticamente encerradas"

#: src/main.py:210
msgid "Close"
msgstr "Fechar"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "Chat reiniciado"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "Pasta reiniciada"

#: src/main.py:254
msgid "Chat is created"
msgstr "Chat criado"

#: src/window.py:120
msgid "Keyboard shorcuts"
msgstr "Atalhos de teclado"

#: src/window.py:121
msgid "About"
msgstr "Sobre"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "Chat"

#: src/window.py:170
msgid "History"
msgstr "Histórico"

#: src/window.py:191
msgid "Create a chat"
msgstr "Criar um chat"

#: src/window.py:196
msgid "Chats"
msgstr "Chats"

#: src/window.py:267
msgid " Stop"
msgstr " Parar"

#: src/window.py:282
msgid " Clear"
msgstr " Limpar"

#: src/window.py:297
msgid " Continue"
msgstr " Continuar"

#: src/window.py:310
msgid " Regenerate"
msgstr " Regenerar"

#: src/window.py:376
msgid "Send a message..."
msgstr "Enviar uma mensagem..."

#: src/window.py:467
msgid "Explorer Tab"
msgstr "Aba do Explorador"

#: src/window.py:468
msgid "Terminal Tab"
msgstr "Aba do Terminal"

#: src/window.py:469
msgid "Browser Tab"
msgstr "Aba do Navegador"

#: src/window.py:589
msgid "Ask about a website"
msgstr "Perguntar sobre um website"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr ""
"Escreva #https://website.com no chat para pedir informações sobre um website"

#: src/window.py:590
msgid "Check out our Extensions!"
msgstr "Confira nossas Extensões!"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "Temos muitas extensões para diferentes coisas. Confira!"

#: src/window.py:591
msgid "Chat with documents!"
msgstr "Converse com documentos!"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr ""
"Adicione seus documentos à sua pasta de documentos e converse usando as "
"informações contidas neles!"

#: src/window.py:592
msgid "Surf the web!"
msgstr "Navegue na web!"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr ""
"Habilite a pesquisa na web para permitir que o LLM navegue na web e forneça "
"respostas atualizadas"

#: src/window.py:593
msgid "Mini Window"
msgstr "Mini Janela"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "Faça perguntas rapidamente usando o modo mini janela"

#: src/window.py:594
msgid "Text to Speech"
msgstr "Texto para Fala"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr "Newelle suporta texto-para-fala! Habilite-o nas configurações"

#: src/window.py:595
msgid "Keyboard Shortcuts"
msgstr "Atalhos de Teclado"

#: src/window.py:595
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "Controle Newelle usando Atalhos de Teclado"

#: src/window.py:596
msgid "Prompt Control"
msgstr "Controle de Prompt"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr ""
"Newelle oferece 100% de controle de prompt. Ajuste seus prompts para seu uso."

#: src/window.py:597
msgid "Thread Editing"
msgstr "Edição de Thread"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "Verifique os programas e processos que você executa no Newelle"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr "Prompts Programáveis"

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr ""
"Você pode adicionar prompts dinâmicos ao Newelle, com condições e "
"probabilidades"

#: src/window.py:605
msgid "New Chat"
msgstr "Novo Chat"

#: src/window.py:623
msgid "Provider Errror"
msgstr "Erro do Provedor"

#: src/window.py:646
msgid "Local Documents"
msgstr "Documentos Locais"

#: src/window.py:650
msgid "Web search"
msgstr "Pesquisa na web"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "Este provedor não possui uma lista de modelos"

#: src/window.py:901
msgid " Models"
msgstr " Modelos"

#: src/window.py:904
msgid "Search Models..."
msgstr "Buscar Modelos..."

#: src/window.py:1132
msgid "Create new profile"
msgstr "Criar novo perfil"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "Não foi possível reconhecer sua voz"

#: src/window.py:1303
msgid "Images"
msgstr "Imagens"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "Arquivos Suportados pelo LLM"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "Arquivos Suportados pelo RAG"

#: src/window.py:1333
msgid "Supported Files"
msgstr "Arquivos Suportados"

#: src/window.py:1337
msgid "All Files"
msgstr "Todos os Arquivos"

#: src/window.py:1343
msgid "Attach file"
msgstr "Anexar arquivo"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "O arquivo não pode ser enviado até que o programa seja finalizado"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "O arquivo não é reconhecido"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "Você não pode mais continuar a mensagem."

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "Você não pode mais regenerar a mensagem."

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "Chat limpo"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "A mensagem foi cancelada e excluída do histórico"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr "A mensagem não pode ser enviada até que o programa seja finalizado"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr ""
"Você não pode editar uma mensagem enquanto o programa está em execução."

#: src/window.py:3080
msgid "Prompt content"
msgstr "Conteúdo do prompt"

#: src/window.py:3339
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"A rede neural tem acesso ao seu computador e a quaisquer dados neste chat e "
"pode executar comandos. Tenha cuidado, não nos responsabilizamos pela rede "
"neural. Não compartilhe informações sensíveis."

#: src/window.py:3368
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"A rede neural tem acesso a quaisquer dados neste chat. Tenha cuidado, não "
"nos responsabilizamos pela rede neural. Não compartilhe informações "
"sensíveis."

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "Caminho da pasta incorreto"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "A thread não foi concluída, número da thread: "

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "Falha ao abrir a pasta"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "Chat vazio"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr ""
"Treine o Newelle para fazer mais com extensões personalizadas e novos "
"módulos de IA, dando ao seu chatbot infinitas possibilidades."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "Chatbot de IA"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "Seleção rápida de perfil"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
msgid "Message Editing"
msgstr "Edição de Mensagens"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "Mais de 10 provedores de IA padrão"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "Correções de bugs"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
#, fuzzy
msgid "Minor bug fixes"
msgstr "Correções de bugs"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "Pequenas melhorias"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "Melhorar a leitura de documentos locais e o desempenho de carregamento"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "Adicionar opção de envio com CTRL+Enter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "Melhorar blocos de código"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
msgid "Fix Kokoro TTS"
msgstr "Corrigir Kokoro TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "Remover emoji do TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "Definir chaves de API como campos de senha"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "Adicionar suporte a pensamento para Gemini"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "Traduções atualizadas"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "Novas funcionalidades adicionadas"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr "Leitura de websites e pesquisa na web com SearXNG, DuckDuckGo e Tavily"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "Renderização de LaTeX e gerenciamento de documentos aprimorados"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "Novo Widget de Pensamento e manipulador OpenRouter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "Suporte a visão para Llama4 no Groq"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "Novas traduções (Chinês Tradicional, Bengali, Hindi)"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "Muitos bugs corrigidos, algumas funcionalidades adicionadas!"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "Suporte a novas funcionalidades e correções de bugs"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "Adicionadas muitas novas funcionalidades e correções de bugs"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "Adicionadas novas funcionalidades e correções de bugs"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr ""
"Biblioteca g4f atualizada com versionamento, guias do usuário adicionados, "
"navegação de extensões aprimorada e tratamento de modelos melhorado."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"Correções de bugs e novas funcionalidades foram implementadas. Modificamos a "
"arquitetura de extensão, adicionamos novos modelos e introduzimos suporte à "
"visão, juntamente com mais capacidades."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "Versão estável"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
msgid "Added extension"
msgstr "Extensão adicionada"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
msgid "Blacklist of commands"
msgstr "Lista negra de comandos"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "Localização"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "Redesenho"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "Qwersyk"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "Newelle: Seu chatbot avançado"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "ia;assistente;chat;chatgpt;gpt;llm;ollama;"

#~ msgid "max Tokens"
#~ msgstr "Máx Tokens"

#~ msgid "Max tokens of the generated text"
#~ msgstr "Máximo de tokens do texto gerado"
