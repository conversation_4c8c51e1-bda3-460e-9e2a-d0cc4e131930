from .tts import TTS<PERSON>and<PERSON>
from .custom_handler import CustomTTSHandler
from .espeak_handler import EspeakHandler
from .gtts_handler import gTTSHandler
from .elevenlabs_handler import ElevenLabs
from .kokoro_handler import KokoroTTSHandler
from .openai_tts_handler import OpenAITTS<PERSON>andler
from .custom_openai_tts import CustomOpen<PERSON>ITTSHandler
from .groq_tts_handler import GroqTT<PERSON><PERSON>andler

__all__ = [
    "TTSHandler",
    "CustomTTSHandler",
    "EspeakHandler",
    "gTTSHandler",
    "ElevenLabs",
    "KokoroTTSHandler",
    "OpenAITTSHandler",
    "CustomOpenAITTSHandler",
    "GroqTTSHandler"
]

