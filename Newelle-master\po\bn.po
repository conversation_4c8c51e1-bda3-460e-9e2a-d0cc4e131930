# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# aritra saha<EMAIL@ADDRESS>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-07-03 09:00+0200\n"
"Last-Translator: aritra saha<EMAIL@ADDRESS>\n"
"Language-Team: Bengali\n"
"Language: bn\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "API এন্ডপয়েন্ট"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr "API বেস URL, ইন্টারফারেন্স API ব্যবহার করতে এটি পরিবর্তন করুন"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid "Automatically Serve"
msgstr "স্বয়ংক্রিয়ভাবে পরিবেশন করুন"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"প্রয়োজনে ব্যাকগ্রাউন্ডে স্বয়ংক্রিয়ভাবে ollama serve চালান যদি এটি চালু না থাকে। "
"আপনি killall ollama দিয়ে এটি বন্ধ করতে পারেন"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
msgid "Custom Model"
msgstr "কাস্টম মডেল"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
msgid "Use a custom model"
msgstr "একটি কাস্টম মডেল ব্যবহার করুন"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
msgid "Ollama Model"
msgstr "Ollama মডেল"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
msgid "Name of the Ollama Model"
msgstr "Ollama মডেলের নাম"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "API কী"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
msgid "API Key for "
msgstr "-এর জন্য API কী "

#: src/handlers/embeddings/openai_handler.py:38
msgid "API base url, change this to use different APIs"
msgstr "API বেস URL, ভিন্ন API ব্যবহার করতে এটি পরিবর্তন করুন"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
msgid "Use Custom Model"
msgstr "কাস্টম মডেল ব্যবহার করুন"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "মডেল"

#: src/handlers/embeddings/openai_handler.py:44
msgid "Name of the Embedding Model to use"
msgstr "ব্যবহার করার জন্য Embedding মডেলের নাম"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
msgid " Model"
msgstr " মডেল"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
msgid "The API key to use"
msgstr "ব্যবহার করার জন্য API কী"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
msgid "The model to use"
msgstr "ব্যবহার করার জন্য মডেল"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "সর্বোচ্চ টোকেন"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "তৈরি করার জন্য টোকেনের সর্বোচ্চ সংখ্যা"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
msgid "Message Streaming"
msgstr "বার্তা স্ট্রিমিং"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "ধীরে ধীরে বার্তা আউটপুট স্ট্রিম করুন"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "বট আউটপুট পেতে চালানোর জন্য কমান্ড"

#: src/handlers/llm/custom_handler.py:21
#, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr ""
"বট প্রতিক্রিয়া পেতে চালানোর জন্য কমান্ড, {0} চ্যাট ধারণকারী একটি JSON ফাইল দ্বারা "
"প্রতিস্থাপিত হবে, {1} সিস্টেম প্রম্পট দ্বারা প্রতিস্থাপিত হবে"

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "বটের পরামর্শ পেতে চালানোর জন্য কমান্ড"

#: src/handlers/llm/custom_handler.py:22
#, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"চ্যাট পরামর্শ পেতে চালানোর জন্য কমান্ড, {0} চ্যাট ধারণকারী একটি JSON ফাইল দ্বারা "
"প্রতিস্থাপিত হবে, {1} অতিরিক্ত প্রম্পট দ্বারা, {2} তৈরি করার জন্য পরামর্শের সংখ্যা "
"দ্বারা। অবশ্যই স্ট্রিং হিসাবে পরামর্শ ধারণকারী একটি JSON অ্যারে ফেরত দিতে হবে"

#: src/handlers/llm/gpt4all_handler.py:67
msgid "RAM Required: "
msgstr "RAM প্রয়োজন: "

#: src/handlers/llm/gpt4all_handler.py:68
msgid "Parameters: "
msgstr "প্যারামিটার: "

#: src/handlers/llm/gpt4all_handler.py:69
msgid "Size: "
msgstr "আকার: "

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
msgid "Model to use"
msgstr "ব্যবহার করার জন্য মডেল"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
msgid "Name of the model to use"
msgstr "ব্যবহার করার জন্য মডেলের নাম"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "Model Manager"
msgstr "মডেল ম্যানেজার"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "উপলব্ধ মডেলগুলির তালিকা"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr ""

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "গোপনীয়তা নীতি"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "গোপনীয়তা নীতি ওয়েবসাইট খুলুন"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "ভাবনা সক্রিয় করুন"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr ""

#: src/handlers/llm/ollama_handler.py:176
msgid "Add custom model"
msgstr "কাস্টম মডেল যোগ করুন"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"নাম:আকার বসিয়ে এই তালিকায় যেকোনো মডেল যোগ করুন\n"
"অথবা hf.co/ব্যবহারকারীর নাম/মডেল সহ hf থেকে যেকোনো gguf"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr ""

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "API কী (প্রয়োজনীয়)"

#: src/handlers/llm/gemini_handler.py:94
msgid "API key got from ai.google.dev"
msgstr "ai.google.dev থেকে প্রাপ্ত API কী"

#: src/handlers/llm/gemini_handler.py:98
msgid "AI Model to use"
msgstr "ব্যবহার করার জন্য AI মডেল"

#: src/handlers/llm/gemini_handler.py:103
msgid "Enable System Prompt"
msgstr "সিস্টেম প্রম্পট সক্ষম করুন"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr ""
"কিছু মডেল সিস্টেম প্রম্পট (বা ডেভেলপারদের নির্দেশাবলী) সমর্থন করে না, যদি এটি "
"সম্পর্কে ত্রুটি পান তবে এটি নিষ্ক্রিয় করুন"

#: src/handlers/llm/gemini_handler.py:107
msgid "Inject system prompt"
msgstr "সিস্টেম প্রম্পট ইনজেক্ট করুন"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr ""
"এমনকি যদি মডেল সিস্টেম প্রম্পট সমর্থন না করে, ব্যবহারকারীর বার্তার উপরে প্রম্পটগুলি "
"রাখুন"

#: src/handlers/llm/gemini_handler.py:109
#, fuzzy
msgid "Thinking Settings"
msgstr "সেটিংস"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "ভাবনা মডেল সম্পর্কিত সেটিংস"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr "ভাবনা দেখান, আপনার মডেল যদি এটি সমর্থন না করে তবে এটি নিষ্ক্রিয় করুন"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "ভাবনা বাজেট সক্রিয় করুন"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "ভাবনা বাজেট সক্রিয় করতে হবে কিনা"

#: src/handlers/llm/gemini_handler.py:112
#, fuzzy
msgid "Thinking Budget"
msgstr "ভাবনা বাজেট"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "ভাবনা করতে কত সময় ব্যয় করবেন"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "চিত্র আউটপুট"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr "চিত্র আউটপুট সক্ষম করুন, শুধুমাত্র gemini-2.0-flash-exp দ্বারা সমর্থিত"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "নিরাপত্তা সেটিংস সক্ষম করুন"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr "ক্ষতিকারক সামগ্রী তৈরি এড়াতে Google নিরাপত্তা সেটিংস সক্ষম করুন"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
msgid "Advanced Parameters"
msgstr "উন্নত প্যারামিটার"

#: src/handlers/llm/gemini_handler.py:135
msgid "Enable advanced parameters"
msgstr "উন্নত প্যারামিটার সক্ষম করুন"

#: src/handlers/llm/openai_handler.py:81
#, fuzzy
msgid "Include parameters like Top-P, Temperature, etc."
msgstr "সর্বোচ্চ টোকেন, টপ-পি, তাপমাত্রা ইত্যাদি প্যারামিটার অন্তর্ভুক্ত করুন।"

#: src/handlers/llm/openai_handler.py:84
msgid "Name of the LLM Model to use"
msgstr "ব্যবহার করার জন্য LLM মডেলের নাম"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "টপ-পি"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr "তাপমাত্রার সাথে স্যাম্পলিংয়ের একটি বিকল্প, যাকে নিউক্লিয়াস স্যাম্পলিং বলা হয়"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "তাপমাত্রা"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr ""
"কোন স্যাম্পলিং তাপমাত্রা ব্যবহার করতে হবে। উচ্চতর মান আউটপুটকে আরও এলোমেলো করে তুলবে"

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "ফ্রিকোয়েন্সি পেনাল্টি"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr ""
"সংখ্যা -2.0 এবং 2.0 এর মধ্যে। পজিটিভ মান মডেলের একই লাইন হুবহু পুনরাবৃত্তি করার "
"সম্ভাবনা হ্রাস করে"

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "প্রেজেন্স পেনাল্টি"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr ""
"সংখ্যা -2.0 এবং 2.0 এর মধ্যে। পজিটিভ মান মডেলের নতুন বিষয় নিয়ে কথা বলার সম্ভাবনা "
"হ্রাস করে"

#: src/handlers/llm/openai_handler.py:108
#, fuzzy
msgid "Custom Options"
msgstr "কাস্টম প্রম্পট"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr ""

#: src/handlers/llm/openrouter_handler.py:14
#, fuzzy
msgid "Provider Sorting"
msgstr "প্রদানকারী ত্রুটি"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "মূল্য/থ্রুপুট বা লেটেন্সির উপর ভিত্তি করে প্রদানকারী নির্বাচন করুন"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "মূল্য"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "থ্রুপুট"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "লেটেন্সি"

#: src/handlers/llm/openrouter_handler.py:15
#, fuzzy
msgid "Providers Order"
msgstr "প্রদানকারী ত্রুটি"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr ""
"ব্যবহার করার জন্য প্রদানকারীদের ক্রম যোগ করুন, নামগুলি কমা দ্বারা পৃথক করা।\n"
"নির্দিষ্ট না করতে খালি রাখুন।"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "ফলব্যাক অনুমোদন করুন"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "অন্যান্য প্রদানকারীদের ফলব্যাক অনুমোদন করুন"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "আপনার ডকুমেন্টগুলি ইন্ডেক্স করুন"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"আপনার ডকুমেন্ট ফোল্ডারের সমস্ত ডকুমেন্ট ইন্ডেক্স করুন। আপনাকে প্রতিবার ডকুমেন্ট সম্পাদনা/"
"তৈরি করার সময়, ডকুমেন্ট অ্যানালাইজার পরিবর্তন করার সময় বা এম্বেডিং মডেল পরিবর্তন "
"করার সময় এই অপারেশনটি চালাতে হবে"

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "চালানোর জন্য কমান্ড"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "{0} মডেলের সম্পূর্ণ পাথ দ্বারা প্রতিস্থাপিত হবে"

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr "Google SR-এর জন্য API কী, ডিফল্টটি ব্যবহার করতে 'default' লিখুন"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "ভাষা"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "IETF-এ চেনার জন্য টেক্সটের ভাষা"

#: src/handlers/stt/groqsr_handler.py:14
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr "Groq SR-এর জন্য API কী, ডিফল্টটি ব্যবহার করতে 'default' লিখুন"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "Groq মডেল"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "Groq মডেলের নাম"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"ট্রান্সক্রিপশনের জন্য ভাষা নির্দিষ্ট করুন। ISO 639-1 ভাষার কোড ব্যবহার করুন (যেমন "
"ইংরেজির জন্য \"en\", ফরাসি জন্য \"fr\" ইত্যাদি)। "

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "OpenAI অনুরোধের জন্য এন্ডপয়েন্ট"

#: src/handlers/stt/openaisr_handler.py:18
msgid "API Key for OpenAI"
msgstr "OpenAI-এর জন্য API কী"

#: src/handlers/stt/openaisr_handler.py:25
msgid "Whisper Model"
msgstr "Whisper মডেল"

#: src/handlers/stt/openaisr_handler.py:26
msgid "Name of the OpenAI model"
msgstr "OpenAI মডেলের নাম"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"ঐচ্ছিক: ট্রান্সক্রিপশনের জন্য ভাষা নির্দিষ্ট করুন। ISO 639-1 ভাষার কোড ব্যবহার করুন "
"(যেমন ইংরেজির জন্য \"en\", ফরাসি জন্য \"fr\" ইত্যাদি)। "

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "মডেল পাথ"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "VOSK মডেলের অ্যাবসোলিউট পাথ (আনজিপ করা)"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
msgid "Name of the Whisper model"
msgstr "Whisper মডেলের নাম"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "wit.ai-এর জন্য সার্ভার অ্যাক্সেস টোকেন"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "অডিও বোঝা যায়নি"

#: src/handlers/stt/whispercpp_handler.py:47
#, fuzzy
msgid "Language of the recognition. For example en, it..."
msgstr "IETF-এ চেনার জন্য টেক্সটের ভাষা"

#: src/handlers/stt/whispercpp_handler.py:48
#, fuzzy
msgid "Model Library"
msgstr "মডেল ম্যানেজার"

#: src/handlers/stt/whispercpp_handler.py:48
#, fuzzy
msgid "Manage Whisper models"
msgstr "Whisper মডেল"

#: src/handlers/stt/whispercpp_handler.py:49
#, fuzzy
msgid "Advanced Settings"
msgstr "উন্নত LLM সেটিংস"

#: src/handlers/stt/whispercpp_handler.py:49
#, fuzzy
msgid "More advanced settings"
msgstr "উন্নত LLM সেটিংস"

#: src/handlers/stt/whispercpp_handler.py:50
#, fuzzy
msgid "Temperature to use"
msgstr "তাপমাত্রা"

#: src/handlers/stt/whispercpp_handler.py:51
#, fuzzy
msgid "Prompt for the recognition"
msgstr "Groq Speech Recognition"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr ""

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Endpoint"
msgstr "এন্ডপয়েন্ট"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "ব্যবহার করার জন্য সার্ভিসের কাস্টম এন্ডপয়েন্ট"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "ভয়েস"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
msgid "The voice to use"
msgstr "ব্যবহার করার জন্য ভয়েস"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "নির্দেশাবলী"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr "ভয়েস জেনারেশনের জন্য নির্দেশাবলী। এই ক্ষেত্রটি এড়াতে এটি ফাঁকা রাখুন"

#: src/handlers/tts/custom_handler.py:17
#, fuzzy, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr "{0} মডেলের সম্পূর্ণ পাথ দ্বারা প্রতিস্থাপিত হবে"

#: src/handlers/tts/elevenlabs_handler.py:11
msgid "API Key for ElevenLabs"
msgstr "ElevenLabs-এর জন্য API কী"

#: src/handlers/tts/elevenlabs_handler.py:19
msgid "Voice ID to use"
msgstr "ব্যবহার করার জন্য ভয়েস আইডি"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "স্থিতিশীলতা"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "ভয়েসের স্থিতিশীলতা"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "সাদৃশ্য বুস্ট"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "সামগ্রিক ভয়েস স্বচ্ছতা এবং স্পিকারের সাদৃশ্য বৃদ্ধি করে"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "স্টাইল অতিরঞ্জন"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr "যদি বক্তৃতার স্টাইল অতিরঞ্জিত করতে হয় তবে উচ্চ মান সুপারিশ করা হয়"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "পছন্দের ভয়েস নির্বাচন করুন"

#: src/handlers/websearch/tavily.py:20
msgid "Token"
msgstr "Token"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "Tavily API কী"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "সর্বোচ্চ ফলাফল"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "বিবেচনা করার জন্য ফলাফলের সংখ্যা"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "অনুসন্ধানের গভীরতা"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"অনুসন্ধানের গভীরতা। উন্নত অনুসন্ধান আপনার প্রশ্নের জন্য সবচেয়ে প্রাসঙ্গিক উৎস এবং "
"বিষয়বস্তুর অংশগুলি পুনরুদ্ধার করার জন্য তৈরি করা হয়েছে, যখন মৌলিক অনুসন্ধান প্রতিটি "
"উৎস থেকে সাধারণ বিষয়বস্তুর অংশ সরবরাহ করে। একটি মৌলিক অনুসন্ধানের জন্য ১টি এপিআই "
"ক্রেডিট খরচ হয়, যখন একটি উন্নত অনুসন্ধানের জন্য ২টি এপিআই ক্রেডিট খরচ হয়।"

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "অনুসন্ধানের শ্রেণী"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"অনুসন্ধানের শ্রেণী। সংবাদ রিয়েল-টাইম আপডেট পেতে উপযোগী, বিশেষ করে রাজনীতি, "
"খেলাধুলা এবং মূলধারার মিডিয়া সূত্র দ্বারা আচ্ছাদিত বড় বর্তমান ঘটনা সম্পর্কে। সাধারণ "
"অনুসন্ধান হল ব্যাপক, আরও সাধারণ-উদ্দেশ্যমূলক অনুসন্ধানের জন্য যা বিস্তৃত উৎস অন্তর্ভুক্ত "
"করতে পারে।"

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "প্রতি উৎস থেকে খণ্ড"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr ""
"প্রতিটি উৎস থেকে পুনরুদ্ধার করার জন্য বিষয়বস্তুর খণ্ডের সংখ্যা। প্রতিটি খণ্ডের দৈর্ঘ্য "
"সর্বোচ্চ ৫০০ অক্ষর। শুধুমাত্র যখন অনুসন্ধানের গভীরতা উন্নত (advanced) থাকে তখনই উপলব্ধ।"

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "বর্তমান তারিখ থেকে কত দিন পিছনের তথ্য অন্তর্ভুক্ত করা হবে তার সংখ্যা"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "শুধুমাত্র যদি বিষয় (topic) সংবাদ হয় তাহলে উপলব্ধ।"

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "উত্তর অন্তর্ভুক্ত করুন"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"প্রদত্ত প্রশ্নের জন্য একটি এলএলএম-জেনারেটেড উত্তর অন্তর্ভুক্ত করুন। মৌলিক অনুসন্ধান একটি "
"দ্রুত উত্তর প্রদান করে। উন্নত অনুসন্ধান আরও বিস্তারিত উত্তর প্রদান করে।"

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "কাঁচা বিষয়বস্তু অন্তর্ভুক্ত করুন"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr "প্রতিটি অনুসন্ধান ফলাফলের পরিষ্কার এবং পার্স করা HTML বিষয়বস্তু অন্তর্ভুক্ত করুন।"

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "ছবি অন্তর্ভুক্ত করুন"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr "একটি চিত্র অনুসন্ধান করুন এবং ফলাফলগুলি প্রতিক্রিয়ায় অন্তর্ভুক্ত করুন।"

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "ছবির বিবরণ অন্তর্ভুক্ত করুন"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr ""
"যখন 'ছবি অন্তর্ভুক্ত করুন' সক্ষম করা হয়, তখন প্রতিটি ছবির জন্য একটি বর্ণনামূলক পাঠ্যও "
"যোগ করুন।"

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "ডোমেইন অন্তর্ভুক্ত করুন"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr "অনুসন্ধান ফলাফলে বিশেষভাবে অন্তর্ভুক্ত করার জন্য ডোমেইনগুলির একটি তালিকা।"

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "ডোমেইন বাদ দিন"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr "অনুসন্ধান ফলাফল থেকে বিশেষভাবে বাদ দেওয়ার জন্য ডোমেইনগুলির একটি তালিকা।"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "অঞ্চল"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "অনুসন্ধান ফলাফলের জন্য অঞ্চল"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "সেটিংস"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "প্রোফাইল নাম"

#: src/ui/profile.py:58
#, fuzzy
msgid "Copied Settings"
msgstr "কপি করা সেটিংস"

#: src/ui/profile.py:58
#, fuzzy
msgid "Settings that will be copied to the new profile"
msgstr "নতুন প্রোফাইলে কপি করা হবে এমন সেটিংস"

#: src/ui/profile.py:70
#, fuzzy
msgid "Create Profile"
msgstr "প্রোফাইল তৈরি করুন"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "প্রোফাইল আমদানি করুন"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
#, fuzzy
msgid "Edit Profile"
msgstr "প্রোফাইল সম্পাদনা করুন"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "প্রোফাইল রপ্তানি করুন"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "পাসওয়ার্ড রপ্তানি করুন"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "পাসওয়ার্ড সদৃশ ক্ষেত্রও রপ্তানি করুন"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "প্রোফাইল ছবি রপ্তানি করুন"

#: src/ui/profile.py:89
#, fuzzy
msgid "Also export the profile picture"
msgstr "প্রোফাইল ছবিটিও রপ্তানি করুন"

#: src/ui/profile.py:109 src/ui/explorer.py:692
#, fuzzy
msgid "Create"
msgstr "তৈরি করুন"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "প্রয়োগ করুন"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr "বর্তমান প্রোফাইলের সেটিংস নতুন প্রোফাইলে কপি করা হবে"

#: src/ui/profile.py:122 src/ui/profile.py:128
#, fuzzy
msgid "Newelle Profiles"
msgstr "নিউয়েল প্রোফাইলসমূহ"

#: src/ui/profile.py:123
msgid "Export"
msgstr "রপ্তানি করুন"

#: src/ui/profile.py:129
msgid "Import"
msgstr "আমদানি করুন"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "প্রোফাইল ছবি সেট করুন"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "থ্রেড সম্পাদনা"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "কোনও থ্রেড চলছে না"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "থ্রেড নম্বর: "

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "প্রোফাইল নির্বাচন করুন"

#: src/ui/widgets/profilerow.py:53
#, fuzzy
msgid "Delete Profile"
msgstr "প্রোফাইল নির্বাচন করুন"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "চিন্তা"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "বিস্তারিত দেখতে প্রসারিত করুন"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "ভাবছে..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr "এলএলএম ভাবছে... চিন্তার প্রক্রিয়া দেখতে প্রসারিত করুন"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "কোনো চিন্তার প্রক্রিয়া রেকর্ড করা হয়নি"

#: src/ui/widgets/tipscarousel.py:41
#, fuzzy
msgid "Newelle Tips"
msgstr "নিউয়েল"

#: src/ui/explorer.py:192
#, fuzzy
msgid "Folder is Empty"
msgstr "ফোল্ডার রিবুট করা হয়েছে"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr ""

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "নতুন ট্যাবে খুলুন"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "ইন্টিগ্রেটেড সম্পাদক খুলুন"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "ফাইল ম্যানেজারে খুলুন"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
#, fuzzy
msgid "Rename"
msgstr "নাম পরিবর্তন করুন"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "মুছে ফেলুন"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "সম্পূর্ণ পথ কপি করুন"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
#, fuzzy
msgid "Failed to open file manager"
msgstr "ফোল্ডার খুলতে ব্যর্থ"

#: src/ui/explorer.py:436
#, fuzzy
msgid "New name:"
msgstr "নতুন ট্যাব"

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "বাতিল করুন"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "সফলভাবে নাম পরিবর্তন হয়েছে"

#: src/ui/explorer.py:476
#, fuzzy, python-brace-format
msgid "Failed to rename: {}"
msgstr "নাম পরিবর্তন করতে ব্যর্থ: {}"

#: src/ui/explorer.py:497
#, fuzzy
msgid "Delete File?"
msgstr "ফাইল মুছে ফেলবেন?"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "আপনি কি নিশ্চিত যে আপনি \"{}\" মুছে ফেলতে চান?"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "সফলভাবে মুছে ফেলা হয়েছে"

#: src/ui/explorer.py:525
#, fuzzy, python-brace-format
msgid "Failed to delete: {}"
msgstr "মুছে ফেলতে ব্যর্থ: {}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "পথ ক্লিপবোর্ডে কপি করা হয়েছে"

#: src/ui/explorer.py:542
#, fuzzy
msgid "Failed to copy path"
msgstr "পথ কপি করতে ব্যর্থ"

#: src/ui/explorer.py:580
#, fuzzy
msgid "Create new folder"
msgstr "নতুন ফোল্ডার তৈরি করুন"

#: src/ui/explorer.py:583
#, fuzzy
msgid "Create new file"
msgstr "নতুন ফাইল তৈরি করুন"

#: src/ui/explorer.py:586
#, fuzzy
msgid "Open Terminal Here"
msgstr "এখানে টার্মিনাল খুলুন"

#: src/ui/explorer.py:640
#, fuzzy
msgid "Create New Folder"
msgstr "নতুন ফোল্ডার তৈরি করুন"

#: src/ui/explorer.py:640
#, fuzzy
msgid "Folder name:"
msgstr "ফোল্ডারের নাম:"

#: src/ui/explorer.py:644
#, fuzzy
msgid "Create New File"
msgstr "নতুন ফাইল তৈরি করুন"

#: src/ui/explorer.py:644
#, fuzzy
msgid "File name:"
msgstr "ফাইলের নাম:"

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "ফোল্ডার সফলভাবে তৈরি হয়েছে"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "ফাইল সফলভাবে তৈরি হয়েছে"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "সেই নামে একটি ফাইল বা ফোল্ডার ইতিমধ্যে আছে"

#: src/ui/explorer.py:728
#, fuzzy
msgid "folder"
msgstr "ফোল্ডার"

#: src/ui/explorer.py:728
msgid "file"
msgstr "ফাইল"

#: src/ui/explorer.py:730
#, fuzzy, python-brace-format
msgid "Failed to create {}: {}"
msgstr "ফোল্ডার খুলতে ব্যর্থ"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "সাহায্য"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "শর্টকাট"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "চ্যাট রিলোড করুন"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "ফোল্ডার রিলোড করুন"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "নতুন ট্যাব"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "ছবি পেস্ট করুন"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "বার্তা বক্সে ফোকাস করুন"

#: src/ui/shortcuts.py:18
#, fuzzy
msgid "Start/stop recording"
msgstr "রেকর্ডিং শুরু করুন"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "সেভ করুন"

#: src/ui/shortcuts.py:20
#, fuzzy
msgid "Stop TTS"
msgstr "Groq TTS"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr ""

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr ""

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr ""

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr ""

#: src/ui/stdout_monitor.py:61
#, fuzzy
msgid "Start/Stop monitoring"
msgstr "রেকর্ডিং শুরু করুন"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr ""

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr ""

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr ""

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr ""

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "এক্সটেনশন"

#: src/ui/extension.py:50
#, fuzzy
msgid "Installed Extensions"
msgstr "ইনস্টল করা এক্সটেনশন"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "এক্সটেনশনগুলির জন্য ব্যবহারকারী নির্দেশিকা"

#: src/ui/extension.py:89
msgid "Download new Extensions"
msgstr "নতুন এক্সটেনশন ডাউনলোড করুন"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "ফাইল থেকে এক্সটেনশন ইনস্টল করুন..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "নিউয়েল"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "চ্যাট মিনি উইন্ডোতে খোলা হয়েছে"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "নিউয়েলে স্বাগতম"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "আপনার চূড়ান্ত ভার্চুয়াল অ্যাসিস্ট্যান্ট।"

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "গিটহাব পেজ"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "আপনার প্রিয় AI ভাষা মডেল নির্বাচন করুন"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"নিউয়েল একাধিক মডেল এবং প্রদানকারীর সাথে ব্যবহার করা যেতে পারে!\n"
"<b>দ্রষ্টব্য: LLM-এর গাইড পৃষ্ঠাটি পড়ার জন্য জোরালোভাবে পরামর্শ দেওয়া হচ্ছে</b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "LLM-এর গাইড"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "আপনার ডকুমেন্টগুলির সাথে চ্যাট করুন"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr ""
"নিউয়েল চ্যাটে পাঠানো ডকুমেন্ট বা আপনার নিজস্ব ফাইল থেকে প্রাসঙ্গিক তথ্য পুনরুদ্ধার "
"করতে পারে! আপনার কোয়েরির সাথে প্রাসঙ্গিক তথ্য LLM-এ পাঠানো হবে।"

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "কমান্ড ভার্চুয়ালাইজেশন"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"নিউয়েল আপনার সিস্টেমে কমান্ড চালানোর জন্য ব্যবহার করা যেতে পারে, তবে আপনি কী "
"চালাচ্ছেন সেদিকে মনোযোগ দিন! <b>LLM আমাদের নিয়ন্ত্রণে নেই, তাই এটি ক্ষতিকারক কোড "
"তৈরি করতে পারে!</b>\n"
"ডিফল্টরূপে, আপনার কমান্ডগুলি <b>ফ্ল্যাটপ্যাক পরিবেশে ভার্চুয়ালাইজড</b> হবে, তবে "
"মনোযোগ দিন!"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "আপনি এক্সটেনশন ব্যবহার করে নিউয়েলের কার্যকারিতা প্রসারিত করতে পারেন!"

#: src/ui/presentation.py:136
msgid "Download extensions"
msgstr "এক্সটেনশন ডাউনলোড করুন"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "অনুমতি ত্রুটি"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr "নিউয়েলের আপনার সিস্টেমে কমান্ড চালানোর জন্য পর্যাপ্ত অনুমতি নেই।"

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "অ্যাপ ব্যবহার শুরু করুন"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "চ্যাটিং শুরু করুন"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "সাধারণ"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "LLM"

#: src/ui/settings.py:49 src/constants.py:528
msgid "Prompts"
msgstr "প্রম্পট"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "জ্ঞান"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "ভাষা মডেল"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "অন্যান্য LLM"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "অন্যান্য উপলব্ধ LLM প্রদানকারী"

#: src/ui/settings.py:73
msgid "Advanced LLM Settings"
msgstr "উন্নত LLM সেটিংস"

#: src/ui/settings.py:77
msgid "Secondary Language Model"
msgstr "সেকেন্ডারি ভাষা মডেল"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr "অফার, চ্যাট নাম এবং মেমরি জেনারেশনের মতো সেকেন্ডারি কাজের জন্য ব্যবহৃত মডেল"

#: src/ui/settings.py:94
msgid "Embedding Model"
msgstr "এম্বেডিং মডেল"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"এম্বেডিং টেক্সটকে ভেক্টরে রূপান্তর করতে ব্যবহৃত হয়। দীর্ঘমেয়াদী মেমরি এবং RAG দ্বারা "
"ব্যবহৃত। এটি পরিবর্তন করলে ডকুমেন্টগুলি পুনরায় ইন্ডেক্স করতে বা মেমরি রিসেট করার "
"প্রয়োজন হতে পারে।"

#: src/ui/settings.py:105 src/window.py:647
msgid "Long Term Memory"
msgstr "দীর্ঘমেয়াদী মেমরি"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "পুরানো কথোপকথনের স্মৃতি রাখুন"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "ওয়েব অনুসন্ধান"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "ওয়েবে তথ্য অনুসন্ধান করুন"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "টেক্সট টু স্পিচ প্রোগ্রাম"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "কোন টেক্সট টু স্পিচ ব্যবহার করতে হবে তা নির্বাচন করুন"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "স্পিচ টু টেক্সট ইঞ্জিন"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "আপনি কোন স্পিচ রিকগনিশন ইঞ্জিন চান তা নির্বাচন করুন"

#: src/ui/settings.py:150
msgid "Automatic Speech To Text"
msgstr "স্বয়ংক্রিয় স্পিচ টু টেক্সট"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr "একটি টেক্সট/TTS শেষে স্বয়ংক্রিয়ভাবে স্পিচ টু টেক্সট পুনরায় চালু করুন"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "প্রম্পট নিয়ন্ত্রণ"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "ইন্টারফেস"

#: src/ui/settings.py:162
msgid "Interface Size"
msgstr "ইন্টারফেসের আকার"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "ইন্টারফেসের আকার সামঞ্জস্য করুন"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "সম্পাদকের রঙের থিম"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "সম্পাদকের ও কোডব্লকের রঙের থিম পরিবর্তন করুন"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "লুকানো ফাইল"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "লুকানো ফাইল দেখান"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "এন্টার দিয়ে পাঠান"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"সক্রিয় করা হলে, এন্টার দিয়ে বার্তা পাঠানো হবে, নতুন লাইনে যেতে CTRL+এন্টার ব্যবহার "
"করুন। নিষ্ক্রিয় করা হলে, SHIFT+এন্টার দিয়ে বার্তা পাঠানো হবে, এবং এন্টার দিয়ে নতুন "
"লাইন হবে।"

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "ইতিহাস থেকে চিন্তা মুছে ফেলুন"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr "টোকেন ব্যবহার কমাতে রিজনিং মডেলগুলির জন্য পুরানো থিঙ্কিং ব্লক পাঠাবেন না"

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "LaTeX প্রদর্শন করুন"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "চ্যাটে LaTeX সূত্র প্রদর্শন করুন"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "চ্যাটের ক্রম উল্টান"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr ""
"চ্যাট তালিকায় সাম্প্রতিকতম চ্যাটগুলি উপরে দেখান (প্রয়োগ করতে চ্যাট পরিবর্তন করুন)"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "স্বয়ংক্রিয়ভাবে চ্যাট নাম তৈরি করুন"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr "প্রথম দুটি বার্তার পরে স্বয়ংক্রিয়ভাবে চ্যাট নাম তৈরি করুন"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "অফারের সংখ্যা"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "চ্যাটে পাঠানোর জন্য বার্তা পরামর্শের সংখ্যা "

#: src/ui/settings.py:224
msgid "Username"
msgstr "ব্যবহারকারীর নাম"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr ""
"আপনার বার্তার আগে প্রদর্শিত লেবেল পরিবর্তন করুন\n"
"এই তথ্য ডিফল্টরূপে LLM-এ পাঠানো হয় না\n"
"আপনি {USER} ভেরিয়েবল ব্যবহার করে এটি একটি প্রম্পটে যোগ করতে পারেন"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "নিউরাল নেটওয়ার্ক নিয়ন্ত্রণ"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "একটি ভার্চুয়াল মেশিনে কমান্ড চালান"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "বাহ্যিক টার্মিনাল"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr "কনসোল কমান্ড চালানোর জন্য বাহ্যিক টার্মিনাল নির্বাচন করুন"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "প্রোগ্রাম মেমরি"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "প্রোগ্রামটি কতক্ষণ চ্যাট মনে রাখে "

#: src/ui/settings.py:266
msgid "Developer"
msgstr ""

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr ""

#: src/ui/settings.py:270
msgid "Open"
msgstr ""

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr ""

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr ""

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr ""

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr ""

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "স্বয়ংক্রিয়-রান কমান্ড"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "বট যে কমান্ডগুলি লিখবে সেগুলি স্বয়ংক্রিয়ভাবে চলবে"

#: src/ui/settings.py:313
msgid "Max number of commands"
msgstr "কমান্ডের সর্বোচ্চ সংখ্যা"

#: src/ui/settings.py:313
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr "একটি একক ব্যবহারকারীর অনুরোধের পরে বট যে সর্বাধিক সংখ্যক কমান্ড লিখবে"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "ব্রাউজার"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "ব্রাউজারের জন্য সেটিংস"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "বাহ্যিক ব্রাউজার ব্যবহার করুন"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr "লিঙ্কগুলি খোলার জন্য ইন্টিগ্রেটেড ব্রাউজারের বদলে বাহ্যিক ব্রাউজার ব্যবহার করুন"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "ব্রাউজার সেশন বজায় রাখুন"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr ""
"রিস্টার্টের মধ্যে ব্রাউজার সেশন বজায় রাখুন। এটি বন্ধ করলে প্রোগ্রাম রিস্টার্ট করতে হবে"

#: src/ui/settings.py:361
#, fuzzy
msgid "Delete browser data"
msgstr "ব্রাউজার সেশন এবং ডেটা মুছে ফেলুন"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "ব্রাউজার সেশন এবং ডেটা মুছে ফেলুন"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "প্রাথমিক ব্রাউজার পাতা"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "যে পাতা থেকে ব্রাউজার শুরু হবে"

#: src/ui/settings.py:375
msgid "Search string"
msgstr "সার্চ স্ট্রিং"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr "ব্রাউজারে ব্যবহৃত সার্চ স্ট্রিং, %s প্রশ্নের সাথে প্রতিস্থাপিত হবে"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "ডকুমেন্ট সোর্স (RAG)"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "প্রতিক্রিয়াগুলিতে আপনার ডকুমেন্টগুলির থেকে সামগ্রী অন্তর্ভুক্ত করুন"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "ডকুমেন্ট অ্যানালাইজার"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr ""
"ডকুমেন্ট অ্যানালাইজার আপনার ডকুমেন্টগুলি সম্পর্কে প্রাসঙ্গিক তথ্য নিষ্কাশন করতে একাধিক "
"কৌশল ব্যবহার করে"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "অসমর্থিত হলে ডকুমেন্ট পড়ুন"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr ""
"যদি LLM ডকুমেন্ট পড়া সমর্থন না করে, তবে চ্যাটে পাঠানো ডকুমেন্টগুলি সম্পর্কে প্রাসঙ্গিক "
"তথ্য আপনার ডকুমেন্ট অ্যানালাইজার ব্যবহার করে LLM-কে দেওয়া হবে।"

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "RAG-এর জন্য সর্বোচ্চ টোকেন"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"RAG-এর জন্য ব্যবহার করা সর্বোচ্চ টোকেন সংখ্যা। যদি ডকুমেন্টগুলি এই টোকেন সংখ্যা "
"অতিক্রম না করে, তাহলে তাদের সবগুলিকে প্রসঙ্গে ডাম্প করুন।"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "ডকুমেন্ট ফোল্ডার"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr ""
"আপনি যে ডকুমেন্টগুলি কোয়েরি করতে চান সেগুলি আপনার ডকুমেন্ট ফোল্ডারে রাখুন। যদি এই "
"বিকল্পটি সক্ষম থাকে তবে ডকুমেন্ট অ্যানালাইজার সেগুলিতে প্রাসঙ্গিক তথ্য খুঁজে পাবে"

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr "আপনি যে সমস্ত ডকুমেন্ট ইন্ডেক্স করতে চান সেগুলি এই ফোল্ডারে রাখুন"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "নীরবতার থ্রেশহোল্ড"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr ""
"সেকেন্ডে নীরবতার থ্রেশহোল্ড, নীরবতা হিসাবে বিবেচিত হওয়ার জন্য ভলিউমের শতাংশ"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "নীরবতার সময়"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr "রেকর্ডিং স্বয়ংক্রিয়ভাবে বন্ধ হওয়ার আগে সেকেন্ডে নীরবতার সময়"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "পর্যাপ্ত অনুমতি নেই"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr ""
"নিউয়েলের আপনার সিস্টেমে কমান্ড চালানোর জন্য পর্যাপ্ত অনুমতি নেই, দয়া করে নিম্নলিখিত "
"কমান্ডটি চালান"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "বুঝেছি"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr ""

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr ""

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "নিউয়েল ডেমো API"

#: src/constants.py:26
msgid "GPT4Free"
msgstr ""

#: src/constants.py:34
msgid "Local Model"
msgstr "স্থানীয় মডেল"

#: src/constants.py:35
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr ""
"GPU সাপোর্ট নেই, এর পরিবর্তে OLLAMA ব্যবহার করুন। LLM মডেল স্থানীয়ভাবে চালান, "
"বেশি গোপনীয়তা কিন্তু ধীরগতির"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "Ollama ইনস্ট্যান্স"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "আপনার নিজের হার্ডওয়্যারে সহজেই একাধিক LLM মডেল চালান"

#: src/constants.py:47
msgid "Groq"
msgstr "Groq"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "Google Gemini API"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "OpenAI API"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr ""
"OpenAI API। কাস্টম এন্ডপয়েন্ট সমর্থিত। কাস্টম প্রদানকারীদের জন্য এটি ব্যবহার করুন"

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "Anthropic Claude"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr ""
"Anthropic Claude-এর মডেলগুলির জন্য অফিসিয়াল API, ছবি এবং ফাইল সাপোর্ট সহ, একটি "
"API কী প্রয়োজন"

#: src/constants.py:73
msgid "Mistral"
msgstr "Mistral"

#: src/constants.py:74
msgid "Mistral API"
msgstr "Mistral API"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "Openrouter.ai API, অনেক মডেল সমর্থন করে"

#: src/constants.py:87
msgid "Deepseek"
msgstr "Deepseek"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "Deepseek API, সবচেয়ে শক্তিশালী ওপেন সোর্স মডেল"

#: src/constants.py:94 src/constants.py:203
msgid "Custom Command"
msgstr "কাস্টম কমান্ড"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "একটি কাস্টম কমান্ডের আউটপুট ব্যবহার করুন"

#: src/constants.py:104
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr "অফলাইনে কাজ করে। C++ এ লেখা অপ্টিমাইজড Whisper ইমপ্লিমেন্টেশন"

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "অফলাইনে কাজ করে। শুধুমাত্র ইংরেজি সমর্থিত"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "Google Speech Recognition"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "Google Speech Recognition অনলাইন"

#: src/constants.py:124
msgid "Groq Speech Recognition"
msgstr "Groq Speech Recognition"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit AI"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr "wit.ai স্পিচ রিকগনিশন ফ্রি API (ভাষা ওয়েবসাইটে নির্বাচিত)"

#: src/constants.py:137
msgid "Vosk API"
msgstr "Vosk API"

#: src/constants.py:138
msgid "Works Offline"
msgstr "অফলাইনে কাজ করে"

#: src/constants.py:144
msgid "Whisper API"
msgstr "Whisper API"

#: src/constants.py:145
msgid "Uses OpenAI Whisper API"
msgstr "OpenAI Whisper API ব্যবহার করে"

#: src/constants.py:151
msgid "Custom command"
msgstr "কাস্টম কমান্ড"

#: src/constants.py:152
msgid "Runs a custom command"
msgstr "একটি কাস্টম কমান্ড চালায়"

#: src/constants.py:161
msgid "Google TTS"
msgstr "Google TTS"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "Google-এর টেক্সট-টু-স্পিচ"

#: src/constants.py:167
msgid "Kokoro TTS"
msgstr "Kokoro TTS"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr "হালকা এবং দ্রুত ওপেন সোর্স TTS ইঞ্জিন। ~3GB ডিপেন্ডেন্সি, 400MB মডেল"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "ElevenLabs TTS"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "স্বাভাবিক শোনানো TTS"

#: src/constants.py:179 src/constants.py:180
msgid "OpenAI TTS"
msgstr "OpenAI TTS"

#: src/constants.py:185
msgid "Groq TTS"
msgstr "Groq TTS"

#: src/constants.py:186
msgid "Groq TTS API"
msgstr "Groq TTS API"

#: src/constants.py:191 src/constants.py:192
msgid "Custom OpenAI TTS"
msgstr "কাস্টম OpenAI TTS"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "Espeak TTS"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "অফলাইন TTS"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr "TTS হিসাবে একটি কাস্টম কমান্ড ব্যবহার করুন, {0} টেক্সট দ্বারা প্রতিস্থাপিত হবে"

#: src/constants.py:212
msgid "WordLlama"
msgstr "WordLlama"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr ""
"llama-ভিত্তিক হালকা স্থানীয় এম্বেডিং মডেল। অফলাইনে কাজ করে, খুব কম রিসোর্স ব্যবহার"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "Ollama Embedding"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr ""
"Embedding-এর জন্য Ollama মডেল ব্যবহার করুন। অফলাইনে কাজ করে, খুব কম রিসোর্স ব্যবহার"

#: src/constants.py:231
msgid "Use Google Gemini API to get embeddings"
msgstr "এম্বেডিং পেতে Google Gemini API ব্যবহার করুন"

#: src/constants.py:239
msgid "User Summary"
msgstr "ব্যবহারকারীর সারাংশ"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "ব্যবহারকারীর কথোপকথনের একটি সারাংশ তৈরি করুন"

#: src/constants.py:245
msgid "Memoripy"
msgstr "Memoripy"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"প্রাসঙ্গিক মেমরি পুনরুদ্ধার, মেমরি ডেকি, ধারণা নিষ্কাশন এবং অন্যান্য উন্নত কৌশল "
"ব্যবহার করে পূর্ববর্তী কথোপকথন থেকে বার্তাগুলি বের করুন। প্রতি বার্তার জন্য 1 llm কল "
"করে।"

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "ব্যবহারকারীর সারাংশ + Memoripy"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "দীর্ঘমেয়াদী স্মৃতির জন্য উভয় প্রযুক্তি ব্যবহার করুন"

#: src/constants.py:260
msgid "Document reader"
msgstr "ডকুমেন্ট রিডার"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr ""
"ক্লাসিক RAG পদ্ধতি - ডকুমেন্টগুলিকে খণ্ড খণ্ড করে এম্বেড করুন, তারপর কোয়েরির সাথে "
"তুলনা করুন এবং সবচেয়ে প্রাসঙ্গিক ডকুমেন্টগুলি ফেরত দিন"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr "SearXNG - ব্যক্তিগত এবং স্ব-হোস্টযোগ্য অনুসন্ধান ইঞ্জিন"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "DuckDuckGo অনুসন্ধান"

#: src/constants.py:281
msgid "Tavily"
msgstr "Tavily"

#: src/constants.py:282
msgid "Tavily search"
msgstr "Tavily অনুসন্ধান"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "সহায়ক অ্যাসিস্ট্যান্ট"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr "LLM উত্তর উন্নত করতে এবং আরও প্রসঙ্গ দিতে সাধারণ উদ্দেশ্যের প্রম্পট"

#: src/constants.py:384
msgid "Console access"
msgstr "কনসোল অ্যাক্সেস"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "প্রোগ্রামটি কম্পিউটারে টার্মিনাল কমান্ড চালাতে পারে কি না"

#: src/constants.py:392
msgid "Current directory"
msgstr "বর্তমান ডিরেক্টরি"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "বর্তমান ডিরেক্টরি কী"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "এলএলএম-কে ইন্টারনেটে অনুসন্ধান করার অনুমতি দিন"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "মৌলিক কার্যকারিতা"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "টেবিল এবং কোড দেখানো (*এটি ছাড়াও কাজ করতে পারে)"

#: src/constants.py:419
msgid "Graphs access"
msgstr "গ্রাফ অ্যাক্সেস"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "প্রোগ্রামটি গ্রাফ প্রদর্শন করতে পারে কি না"

#: src/constants.py:428
msgid "Show image"
msgstr "ছবি দেখান"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "চ্যাটে ছবি দেখান"

#: src/constants.py:437
msgid "Custom Prompt"
msgstr "কাস্টম প্রম্পট"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "আপনার নিজস্ব কাস্টম প্রম্পট যোগ করুন"

#: src/constants.py:480
#, fuzzy
msgid "LLM and Secondary LLM settings"
msgstr "উন্নত LLM সেটিংস"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "টিটিএস"

#: src/constants.py:485
#, fuzzy
msgid "Text to Speech settings"
msgstr "টেক্সট টু স্পিচ সেটিংস"

#: src/constants.py:488
msgid "STT"
msgstr "এসটিটি"

#: src/constants.py:490
#, fuzzy
msgid "Speech to Text settings"
msgstr "স্পিচ টু টেক্সট ইঞ্জিন"

#: src/constants.py:493
#, fuzzy
msgid "Embedding"
msgstr "এম্বেডিং মডেল"

#: src/constants.py:495
#, fuzzy
msgid "Embedding settings"
msgstr "এম্বেডিং মডেল"

#: src/constants.py:498
msgid "Memory"
msgstr "মেমরি"

#: src/constants.py:500
#, fuzzy
msgid "Memory settings"
msgstr "সেটিংস"

#: src/constants.py:503
#, fuzzy
msgid "Websearch"
msgstr "নিরাপত্তা সেটিংস সক্ষম করুন"

#: src/constants.py:505
#, fuzzy
msgid "Websearch settings"
msgstr "নিরাপত্তা সেটিংস সক্ষম করুন"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
#, fuzzy
msgid "Document analyzer settings"
msgstr "ডকুমেন্ট অ্যানালাইজার"

#: src/constants.py:515
#, fuzzy
msgid "Extensions settings"
msgstr "এক্সটেনশন"

#: src/constants.py:518
#, fuzzy
msgid "Inteface"
msgstr "ইন্টারফেস"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr "ইন্টারফেস সেটিংস, লুকানো ফাইল, বিপরীত ক্রম, জুম..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr ""
"সাধারণ সেটিংস, ভার্চুয়ালাইজেশন, অফার, মেমরির দৈর্ঘ্য, স্বয়ংক্রিয়ভাবে চ্যাটের নাম "
"তৈরি করুন, বর্তমান ফোল্ডার..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr "প্রম্পট সেটিংস, কাস্টম অতিরিক্ত প্রম্পট, কাস্টম প্রম্পট..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "চ্যাট "

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "টার্মিনাল থ্রেডগুলি এখনও ব্যাকগ্রাউন্ডে চলছে"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "আপনি যখন উইন্ডোটি বন্ধ করবেন, তখন সেগুলি স্বয়ংক্রিয়ভাবে বন্ধ হয়ে যাবে"

#: src/main.py:210
msgid "Close"
msgstr "বন্ধ করুন"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "চ্যাট রিবুট করা হয়েছে"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "ফোল্ডার রিবুট করা হয়েছে"

#: src/main.py:254
msgid "Chat is created"
msgstr "চ্যাট তৈরি করা হয়েছে"

#: src/window.py:120
msgid "Keyboard shorcuts"
msgstr "কিবোর্ড শর্টকাট"

#: src/window.py:121
msgid "About"
msgstr "সম্পর্কে"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "চ্যাট"

#: src/window.py:170
msgid "History"
msgstr "ইতিহাস"

#: src/window.py:191
msgid "Create a chat"
msgstr "একটি চ্যাট তৈরি করুন"

#: src/window.py:196
#, fuzzy
msgid "Chats"
msgstr "চ্যাট"

#: src/window.py:267
msgid " Stop"
msgstr " থামুন"

#: src/window.py:282
msgid " Clear"
msgstr " পরিষ্কার করুন"

#: src/window.py:297
msgid " Continue"
msgstr " চালিয়ে যান"

#: src/window.py:310
msgid " Regenerate"
msgstr " পুনরায় তৈরি করুন"

#: src/window.py:376
msgid "Send a message..."
msgstr "একটি বার্তা পাঠান..."

#: src/window.py:467
msgid "Explorer Tab"
msgstr "এক্সপ্লোরার ট্যাব"

#: src/window.py:468
msgid "Terminal Tab"
msgstr "টার্মিনাল ট্যাব"

#: src/window.py:469
msgid "Browser Tab"
msgstr "ব্রাউজার ট্যাব"

#: src/window.py:589
msgid "Ask about a website"
msgstr "একটি ওয়েবসাইট সম্পর্কে জিজ্ঞাসা করুন"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr "একটি ওয়েবসাইট সম্পর্কে তথ্য জানতে চ্যাটে #https://website.com লিখুন"

#: src/window.py:590
#, fuzzy
msgid "Check out our Extensions!"
msgstr "আমাদের এক্সটেনশনগুলি দেখুন!"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "আমাদের কাছে বিভিন্ন কাজের জন্য অনেক এক্সটেনশন আছে। দেখে নিন!"

#: src/window.py:591
#, fuzzy
msgid "Chat with documents!"
msgstr "আপনার ডকুমেন্টগুলির সাথে চ্যাট করুন"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr ""
"আপনার নথিগুলি আপনার নথি ফোল্ডারে যোগ করুন এবং সেগুলিতে থাকা তথ্য ব্যবহার করে চ্যাট "
"করুন!"

#: src/window.py:592
msgid "Surf the web!"
msgstr "ওয়েব সার্ফ করুন!"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr ""
"এলএলএম-কে ওয়েব সার্ফ করতে এবং হালনাগাদ উত্তর প্রদান করতে সক্ষম করতে ওয়েব অনুসন্ধান "
"চালু করুন।"

#: src/window.py:593
msgid "Mini Window"
msgstr "মিনি উইন্ডো"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "মিনি উইন্ডো মোড ব্যবহার করে দ্রুত প্রশ্ন জিজ্ঞাসা করুন"

#: src/window.py:594
#, fuzzy
msgid "Text to Speech"
msgstr "টেক্সট টু স্পিচ প্রোগ্রাম"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr "নিউয়েল টেক্সট-টু-স্পিচ সমর্থন করে! সেটিংসে এটি সক্রিয় করুন"

#: src/window.py:595
#, fuzzy
msgid "Keyboard Shortcuts"
msgstr "কিবোর্ড শর্টকাট"

#: src/window.py:595
#, fuzzy
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "কিবোর্ড শর্টকাট"

#: src/window.py:596
#, fuzzy
msgid "Prompt Control"
msgstr "প্রম্পট নিয়ন্ত্রণ"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr ""
"নিউয়েল আপনাকে 100% প্রম্পট নিয়ন্ত্রণ দেয়। আপনার ব্যবহারের জন্য আপনার প্রম্পটগুলি টিউন "
"করুন।"

#: src/window.py:597
#, fuzzy
msgid "Thread Editing"
msgstr "থ্রেড সম্পাদনা"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "নিউয়েল থেকে আপনি যে প্রোগ্রাম এবং প্রক্রিয়াগুলি চালান তা পরীক্ষা করুন"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr ""

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr ""

#: src/window.py:605
#, fuzzy
msgid "New Chat"
msgstr "নতুন ট্যাব"

#: src/window.py:623
msgid "Provider Errror"
msgstr "প্রদানকারী ত্রুটি"

#: src/window.py:646
#, fuzzy
msgid "Local Documents"
msgstr "স্থানীয় মডেল"

#: src/window.py:650
#, fuzzy
msgid "Web search"
msgstr "নিরাপত্তা সেটিংস সক্ষম করুন"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "এই প্রদানকারীর কোনও মডেল তালিকা নেই"

#: src/window.py:901
msgid " Models"
msgstr " মডেল"

#: src/window.py:904
msgid "Search Models..."
msgstr "মডেল অনুসন্ধান করুন..."

#: src/window.py:1132
msgid "Create new profile"
msgstr "নতুন প্রোফাইল তৈরি করুন"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "আপনার ভয়েস চিনতে পারিনি"

#: src/window.py:1303
msgid "Images"
msgstr "ছবি"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "এলএলএম সমর্থিত ফাইল"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "RAG সমর্থিত ফাইল"

#: src/window.py:1333
msgid "Supported Files"
msgstr "সমর্থিত ফাইল"

#: src/window.py:1337
msgid "All Files"
msgstr "সকল ফাইল"

#: src/window.py:1343
msgid "Attach file"
msgstr "ফাইল সংযুক্ত করুন"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "প্রোগ্রাম শেষ না হওয়া পর্যন্ত ফাইলটি পাঠানো যাবে না"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "ফাইলটি স্বীকৃত নয়"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "আপনি আর বার্তাটি চালিয়ে যেতে পারবেন না।"

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "আপনি আর বার্তাটি পুনরায় তৈরি করতে পারবেন না।"

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "চ্যাট পরিষ্কার করা হয়েছে"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "বার্তাটি বাতিল করা হয়েছে এবং ইতিহাস থেকে মুছে ফেলা হয়েছে"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr "প্রোগ্রাম শেষ না হওয়া পর্যন্ত বার্তাটি পাঠানো যাবে না"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr "প্রোগ্রাম চলার সময় আপনি একটি বার্তা সম্পাদনা করতে পারবেন না।"

#: src/window.py:3080
msgid "Prompt content"
msgstr "প্রম্পট বিষয়বস্তু"

#: src/window.py:3339
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"নিউরাল নেটওয়ার্কের আপনার কম্পিউটার এবং এই চ্যাটের যেকোনো ডেটাতে অ্যাক্সেস রয়েছে "
"এবং এটি কমান্ড চালাতে পারে, সতর্ক থাকুন, আমরা নিউরাল নেটওয়ার্কের জন্য দায়ী নই। "
"কোনো সংবেদনশীল তথ্য শেয়ার করবেন না।"

#: src/window.py:3368
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"নিউরাল নেটওয়ার্কের এই চ্যাটের যেকোনো ডেটাতে অ্যাক্সেস রয়েছে, সতর্ক থাকুন, আমরা "
"নিউরাল নেটওয়ার্কের জন্য দায়ী নই। কোনো সংবেদনশীল তথ্য শেয়ার করবেন না।"

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "ভুল ফোল্ডার পাথ"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "থ্রেড সম্পন্ন হয়নি, থ্রেড নম্বর: "

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "ফোল্ডার খুলতে ব্যর্থ"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "চ্যাট খালি"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr ""
"কাস্টম এক্সটেনশন এবং নতুন এআই মডিউল ব্যবহার করে নিউয়েলকে আরও বেশি কিছু করার জন্য "
"প্রশিক্ষণ দিন, যা আপনার চ্যাটবটকে অফুরন্ত সম্ভাবনা এনে দেবে।"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "এআই চ্যাটবট"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "দ্রুত প্রোফাইল নির্বাচন"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
#, fuzzy
msgid "Message Editing"
msgstr "বার্তা স্ট্রিমিং"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "১০টির বেশি স্ট্যান্ডার্ড এআই প্রদানকারী"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "বাগ সংশোধন"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
#, fuzzy
msgid "Minor bug fixes"
msgstr "বাগ সংশোধন"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "ছোটখাট কিছু উন্নতি"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "লোকাল ডকুমেন্ট পড়া এবং লোড করার কর্মক্ষমতা উন্নত করুন"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "CTRL+Enter দিয়ে পাঠানোর বিকল্প যোগ করুন"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "কোডব্লক উন্নত করুন"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
#, fuzzy
msgid "Fix Kokoro TTS"
msgstr "Kokoro TTS সংশোধন করুন"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "TTS থেকে ইমোজি সরান"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "API কীকে পাসওয়ার্ড ক্ষেত্র হিসেবে সেট করুন"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "Gemini এর জন্য ভাবনার সমর্থন যোগ করুন"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "আপডেট করা অনুবাদ"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "নতুন বৈশিষ্ট্য যুক্ত করা হয়েছে"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr "SearXNG, DuckDuckGo, এবং Tavily সহ ওয়েবসাইট পড়া এবং ওয়েব অনুসন্ধান"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "উন্নত ল্যাটেক্স রেন্ডারিং এবং নথি ব্যবস্থাপনা"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "নতুন থিংকিং উইজেট এবং ওপেনরাউটার হ্যান্ডলার"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "গ্রোকে Llama4-এর জন্য ভিশন সমর্থন"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "নতুন অনুবাদ (ঐতিহ্যবাহী চীনা, বাংলা, হিন্দি)"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "অনেক বাগ ঠিক করা হয়েছে, কিছু বৈশিষ্ট্য যুক্ত করা হয়েছে!"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "নতুন বৈশিষ্ট্য এবং বাগ সংশোধনের জন্য সমর্থন"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "অনেক নতুন বৈশিষ্ট্য এবং বাগ সংশোধন যুক্ত করা হয়েছে"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "নতুন বৈশিষ্ট্য এবং বাগ সংশোধন যুক্ত করা হয়েছে"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr ""
"g4f লাইব্রেরিকে সংস্করণ সহ আপডেট করা হয়েছে, ব্যবহারকারী নির্দেশিকা যোগ করা "
"হয়েছে, এক্সটেনশন ব্রাউজিং উন্নত করা হয়েছে এবং মডেল হ্যান্ডলিং উন্নত করা হয়েছে।"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"বাগ সংশোধন এবং নতুন বৈশিষ্ট্য প্রয়োগ করা হয়েছে। আমরা এক্সটেনশন আর্কিটেকচার পরিবর্তন "
"করেছি, নতুন মডেল যোগ করেছি এবং আরও অনেক ক্ষমতার সাথে ভিশন সমর্থন চালু করেছি।"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "স্থিতিশীল সংস্করণ"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
#, fuzzy
msgid "Added extension"
msgstr "এক্সটেনশন যুক্ত করা হয়েছে"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
#, fuzzy
msgid "Blacklist of commands"
msgstr "কমান্ডের কালো তালিকা"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "স্থানীয়করণ"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "পুনরায় নকশা"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "Qwersyk"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "নিউয়েল: আপনার উন্নত চ্যাট বট"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "কৃত্রিম বুদ্ধিমত্তা;সহকারী;চ্যাট;চ্যাটজিপিটি;জিপিটি;এলএলএম;ওল্লামা;"

#~ msgid "max Tokens"
#~ msgstr "সর্বোচ্চ টোকেন"

#~ msgid "Max tokens of the generated text"
#~ msgstr "তৈরি করা টেক্সটের সর্বোচ্চ টোকেন"

#~ msgid "Any free Provider"
#~ msgstr "যেকোনো বিনামূল্যের প্রদানকারী"

#~ msgid "chat;ai;gpt;chatgpt;assistant;"
#~ msgstr "চ্যাট;এআই;জিপিটি;চ্যাটজিপিটি;সহকারী;"

#, fuzzy
#~ msgid "_Cancel"
#~ msgstr "বাতিল করুন"

#, fuzzy, python-brace-format
#~ msgid "Name of the {provider_name} Model"
#~ msgstr "Groq মডেলের নাম"

#~ msgid "Choose an extension"
#~ msgstr "একটি এক্সটেনশন নির্বাচন করুন"
