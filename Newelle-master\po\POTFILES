src/handlers/embeddings/embedding.py
src/handlers/embeddings/gemini_handler.py
src/handlers/embeddings/ollama_handler.py
src/handlers/embeddings/openai_handler.py
src/handlers/embeddings/wordllama_handler.py
src/handlers/handler.py
src/handlers/llm/claude_handler.py
src/handlers/llm/custom_handler.py
src/handlers/llm/gpt3any_handler.py
src/handlers/llm/gpt4all_handler.py
src/handlers/llm/groq_handler.py
src/handlers/llm/llm.py
src/handlers/llm/g4f_handler.py
src/handlers/llm/newelle_handler.py
src/handlers/llm/ollama_handler.py
src/handlers/llm/deepseek_handler.py
src/handlers/llm/gemini_handler.py
src/handlers/llm/mistral_handler.py
src/handlers/llm/openai_handler.py
src/handlers/llm/openrouter_handler.py
src/handlers/memory/memoripy_handler.py
src/handlers/memory/summary_memoripy_handler.py
src/handlers/memory/user_summary_handler.py
src/handlers/rag/rag_handler.py
src/handlers/rag/llamaindex_handler.py
src/handlers/stt/custom_handler.py
src/handlers/stt/googlesr_handler.py
src/handlers/stt/groqsr_handler.py
src/handlers/stt/openaisr_handler.py
src/handlers/stt/vosk_handler.py
src/handlers/stt/whisper_handler.py
src/handlers/stt/witai_handler.py
src/handlers/stt/sphinx_handler.py
src/handlers/stt/whispercpp_handler.py
src/handlers/tts/custom_openai_tts.py
src/handlers/tts/groq_tts_handler.py
src/handlers/tts/openai_tts_handler.py
src/handlers/tts/custom_handler.py
src/handlers/tts/elevenlabs_handler.py
src/handlers/tts/tts.py
src/handlers/websearch/tavily.py
src/handlers/websearch/duckduckgo_handler.py
src/integrations/website_reader.py
src/integrations/websearch.py
src/ui/profile.py
src/ui/screenrecorder.py
src/ui/thread_editing.py
src/ui/widgets/barchart.py
src/ui/widgets/comborow.py
src/ui/widgets/documents_reader.py
src/ui/widgets/latex.py
src/ui/widgets/markuptextview.py
src/ui/widgets/multiline.py
src/ui/widgets/profilerow.py
src/ui/widgets/terminal_dialog.py
src/ui/widgets/browser.py
src/ui/widgets/code_editor.py
src/ui/widgets/copybox.py
src/ui/widgets/website.py
src/ui/widgets/websearch.py
src/ui/widgets/thinking.py
src/ui/widgets/file.py
src/ui/widgets/tipscarousel.py
src/ui/explorer.py
src/ui/shortcuts.py
src/ui/stdout_monitor.py
src/ui/extension.py
src/ui/mini_window.py
src/ui/presentation.py
src/ui/settings.py
src/utility/audio_recorder.py
src/utility/util.py
src/utility/website_scraper.py
src/utility/message_chunk.py
src/utility/stdout_capture.py
src/utility/replacehelper.py
src/utility/strings.py
src/ui_controller.py
src/constants.py
src/controller.py
src/extensions.py
src/main.py
src/window.py
data/io.github.qwersyk.Newelle.appdata.xml.in
data/io.github.qwersyk.Newelle.desktop.in
data/io.github.qwersyk.Newelle.gschema.xml
