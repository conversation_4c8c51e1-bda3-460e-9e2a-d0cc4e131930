msgid ""
msgstr ""
"Project-Id-Version: 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-07-03 09:00+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Ukrainian\n"
"Language: uk\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "Кінцева точка API"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr "Базова URL-адреса API, змініть це для використання API втручання"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
#, fuzzy
msgid "Automatically Serve"
msgstr "Автоматично запускати сервер"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"Автоматично запускати ollama serve у фоновому режимі, якщо це необхідно і "
"він не запущений. Ви можете припинити його роботу за допомогою killall ollama"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
#, fuzzy
msgid "Custom Model"
msgstr "Користувацька модель"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
#, fuzzy
msgid "Use a custom model"
msgstr "Використовувати користувацьку модель"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
#, fuzzy
msgid "Ollama Model"
msgstr "Модель Ollama"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
#, fuzzy
msgid "Name of the Ollama Model"
msgstr "Назва моделі Ollama"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "API-ключ"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
#, fuzzy
msgid "API Key for "
msgstr "API-ключ для OpenAI"

#: src/handlers/embeddings/openai_handler.py:38
#, fuzzy
msgid "API base url, change this to use different APIs"
msgstr "Базова URL-адреса API, змініть це для використання API втручання"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
#, fuzzy
msgid "Use Custom Model"
msgstr "Використовувати користувацьку модель"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "Модель"

#: src/handlers/embeddings/openai_handler.py:44
#, fuzzy
msgid "Name of the Embedding Model to use"
msgstr "Назва моделі вбудовування для використання"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
#, fuzzy
msgid " Model"
msgstr "Модель"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#, fuzzy
msgid "The API key to use"
msgstr "API-ключ для використання"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#, fuzzy
msgid "The model to use"
msgstr "Модель для використання"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "Максимальна кількість токенів"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "Максимальна кількість токенів для генерації"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
msgid "Message Streaming"
msgstr "Потокова передача повідомлень"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "Поступово передавати вивід повідомлень"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "Команда для виконання для отримання виводу бота"

#: src/handlers/llm/custom_handler.py:21
#, fuzzy, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr ""
"Команда для виконання для отримання відповіді бота, {0} буде замінено JSON-"
"файлом, що містить чат, {1} - системним запитом"

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "Команда для виконання для отримання пропозицій бота"

#: src/handlers/llm/custom_handler.py:22
#, fuzzy, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"Команда для виконання для отримання пропозицій чату, {0} буде замінено JSON-"
"файлом, що містить чат, {1} - додатковими запитами, {2} - кількістю "
"пропозицій для генерації. Повинна повертати JSON-масив, що містить "
"пропозиції як рядки"

#: src/handlers/llm/gpt4all_handler.py:67
#, fuzzy
msgid "RAM Required: "
msgstr "Необхідно оперативної пам'яті: "

#: src/handlers/llm/gpt4all_handler.py:68
#, fuzzy
msgid "Parameters: "
msgstr "Параметри: "

#: src/handlers/llm/gpt4all_handler.py:69
#, fuzzy
msgid "Size: "
msgstr "Розмір: "

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
#, fuzzy
msgid "Model to use"
msgstr "Модель для використання"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
#, fuzzy
msgid "Name of the model to use"
msgstr "Назва моделі для використання"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
#, fuzzy
msgid "Model Manager"
msgstr "Менеджер моделей"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "Список доступних моделей"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr "Оновити G4F"

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "Політика конфіденційності"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "Відкрити веб-сайт політики конфіденційності"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "Увімкнути мислення"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr "Дозволити мислення в моделі, підтримуються лише деякі моделі"

#: src/handlers/llm/ollama_handler.py:176
#, fuzzy
msgid "Add custom model"
msgstr "Додати користувацьку модель"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"Додайте будь-яку модель до цього списку, вказавши name:size\n"
"Або будь-який gguf з hf за допомогою hf.co/username/model"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr "Оновити Ollama"

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "API-ключ (обов'язково)"

#: src/handlers/llm/gemini_handler.py:94
#, fuzzy
msgid "API key got from ai.google.dev"
msgstr "API-ключ, отриманий з ai.google.dev"

#: src/handlers/llm/gemini_handler.py:98
#, fuzzy
msgid "AI Model to use"
msgstr "Модель ШІ для використання"

#: src/handlers/llm/gemini_handler.py:103
#, fuzzy
msgid "Enable System Prompt"
msgstr "Увімкнути системний запит"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr ""
"Деякі моделі не підтримують системний запит (або інструкції розробників), "
"вимкніть його, якщо отримуєте помилки"

#: src/handlers/llm/gemini_handler.py:107
#, fuzzy
msgid "Inject system prompt"
msgstr "Вставити системний запит"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr ""
"Навіть якщо модель не підтримує системні підказки, розмістіть підказки над "
"повідомленням користувача"

#: src/handlers/llm/gemini_handler.py:109
#, fuzzy
msgid "Thinking Settings"
msgstr "Налаштування мислення"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "Налаштування щодо моделей мислення"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr "Показати мислення, вимкніть, якщо ваша модель не підтримує його"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "Увімкнути бюджет мислення"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "Чи вмикати бюджет мислення"

#: src/handlers/llm/gemini_handler.py:112
msgid "Thinking Budget"
msgstr "Бюджет мислення"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "Скільки часу приділяти мисленню"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "Вивід зображень"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr "Увімкнути вивід зображень, підтримується тільки gemini-2.0-flash-exp"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "Увімкнути налаштування безпеки"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr ""
"Увімкнути налаштування безпеки Google, щоб уникнути генерації шкідливого "
"контенту"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
#, fuzzy
msgid "Advanced Parameters"
msgstr "Додаткові параметри"

#: src/handlers/llm/gemini_handler.py:135
#, fuzzy
msgid "Enable advanced parameters"
msgstr "Увімкнути додаткові параметри"

#: src/handlers/llm/openai_handler.py:81
#, fuzzy
msgid "Include parameters like Top-P, Temperature, etc."
msgstr "Включити параметри, такі як Макс. токенів, Top-P, Температура тощо."

#: src/handlers/llm/openai_handler.py:84
#, fuzzy
msgid "Name of the LLM Model to use"
msgstr "Назва моделі LLM для використання"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "Top-P"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr ""
"Альтернатива семплінгу з температурою, яка називається ядерним семплінгом"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "Температура"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr ""
"Яку температуру семплінгу використовувати. Вищі значення зроблять вивід "
"більш випадковим"

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "Штраф за частоту"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr ""
"Число від -2.0 до 2.0. Позитивні значення зменшують ймовірність моделі "
"повторювати той самий рядок дослівно"

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "Штраф за наявність"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr ""
"Число від -2.0 до 2.0. Позитивні значення зменшують ймовірність моделі "
"говорити про нові теми"

#: src/handlers/llm/openai_handler.py:108
#, fuzzy
msgid "Custom Options"
msgstr "Користувацький запит"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr ""

#: src/handlers/llm/openrouter_handler.py:14
msgid "Provider Sorting"
msgstr "Сортування провайдерів"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "Виберіть провайдерів за ціною/пропускною здатністю або затримкою"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "Ціна"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "Пропускна здатність"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "Затримка"

#: src/handlers/llm/openrouter_handler.py:15
msgid "Providers Order"
msgstr "Порядок провайдерів"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr ""
"Додайте порядок провайдерів для використання, імена розділені комою.\n"
"Залиште порожнім, щоб не вказувати"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "Дозволити резервування"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "Дозволити резервування на інших провайдерів"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "Індексувати ваші документи"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"Індексуйте всі документи у вашій папці документів. Ви повинні виконувати цю "
"операцію кожного разу, коли редагуєте/створюєте документ, змінюєте "
"аналізатор документів або модель вбудовування"

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "Команда для виконання"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "{0} буде замінено повним шляхом до моделі"

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr ""
"API-ключ для Google SR, напишіть 'default' для використання стандартного"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "Мова"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "Мова тексту для розпізнавання в форматі IETF"

#: src/handlers/stt/groqsr_handler.py:14
#, fuzzy
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr "API-ключ для Groq SR, напишіть 'default' для використання стандартного"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "Модель Groq"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "Назва моделі Groq"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Вкажіть мову для транскрипції. Використовуйте коди мов ISO 639-1 (наприклад, "
"\"en\" для англійської, \"fr\" для французької тощо)."

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "Кінцева точка для запитів OpenAI"

#: src/handlers/stt/openaisr_handler.py:18
#, fuzzy
msgid "API Key for OpenAI"
msgstr "API-ключ для OpenAI"

#: src/handlers/stt/openaisr_handler.py:25
#, fuzzy
msgid "Whisper Model"
msgstr "Модель Whisper"

#: src/handlers/stt/openaisr_handler.py:26
#, fuzzy
msgid "Name of the OpenAI model"
msgstr "Назва моделі OpenAI"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Необов’язково: Вкажіть мову для транскрипції. Використовуйте коди мов ISO "
"639-1 (наприклад, \"en\" для англійської, \"fr\" для французької тощо)."

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "Шлях до моделі"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "Абсолютний шлях до моделі VOSK (розархівованої)"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
#, fuzzy
msgid "Name of the Whisper model"
msgstr "Назва моделі Whisper"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "Токен доступу до сервера для wit.ai"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "Не вдалося розпізнати аудіо"

#: src/handlers/stt/whispercpp_handler.py:47
#, fuzzy
msgid "Language of the recognition. For example en, it..."
msgstr "Мова розпізнавання."

#: src/handlers/stt/whispercpp_handler.py:48
#, fuzzy
msgid "Model Library"
msgstr "Бібліотека моделей"

#: src/handlers/stt/whispercpp_handler.py:48
#, fuzzy
msgid "Manage Whisper models"
msgstr "Керувати моделями Whisper"

#: src/handlers/stt/whispercpp_handler.py:49
#, fuzzy
msgid "Advanced Settings"
msgstr "Додаткові налаштування"

#: src/handlers/stt/whispercpp_handler.py:49
#, fuzzy
msgid "More advanced settings"
msgstr "Додаткові налаштування"

#: src/handlers/stt/whispercpp_handler.py:50
#, fuzzy
msgid "Temperature to use"
msgstr "Температура"

#: src/handlers/stt/whispercpp_handler.py:51
#, fuzzy
msgid "Prompt for the recognition"
msgstr "Запит для розпізнавання"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr "Запит, який використовуватиметься для розпізнавання"

#: src/handlers/tts/custom_openai_tts.py:17
#, fuzzy
msgid "Endpoint"
msgstr "Кінцева точка"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "Користувацька кінцева точка сервісу для використання"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "Голос"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#, fuzzy
msgid "The voice to use"
msgstr "Голос для використання"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "Інструкції"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr ""
"Інструкції для генерації голосу. Залиште порожнім, щоб уникнути цього поля"

#: src/handlers/tts/custom_handler.py:17
#, fuzzy, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr "{0} буде замінено повним шляхом до файлу, {1} – текстом"

#: src/handlers/tts/elevenlabs_handler.py:11
#, fuzzy
msgid "API Key for ElevenLabs"
msgstr "API-ключ для ElevenLabs"

#: src/handlers/tts/elevenlabs_handler.py:19
#, fuzzy
msgid "Voice ID to use"
msgstr "Ідентифікатор голосу для використання"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "Стабільність"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "стабільність голосу"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "Підсилення подібності"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "Підвищує загальну чіткість голосу та подібність до мовця"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "Перебільшення стилю"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr ""
"Високі значення рекомендуються, якщо стиль мовлення повинен бути "
"перебільшений"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "Виберіть бажаний голос"

#: src/handlers/websearch/tavily.py:20
#, fuzzy
msgid "Token"
msgstr "Токен"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "API-ключ Tavily"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "Максимум результатів"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "Кількість результатів для розгляду"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "Глибина пошуку"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"Глибина пошуку. Розширений пошук призначений для отримання найрелевантніших "
"джерел та фрагментів контенту для вашого запиту, тоді як базовий пошук надає "
"загальні фрагменти контенту з кожного джерела. Базовий пошук коштує 1 API-"
"кредит, тоді як розширений пошук коштує 2 API-кредити."

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "Категорія пошуку"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"Категорія пошуку. Новини корисні для отримання оновлень у реальному часі, "
"зокрема щодо політики, спорту та головних поточних подій, які висвітлюються "
"основними ЗМІ. Загальний пошук призначений для ширших, більш загальних "
"пошуків, які можуть включати широкий спектр джерел."

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "Фрагменти на джерело"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr ""
"Кількість фрагментів вмісту, що вилучаються з кожного джерела. Довжина "
"кожного фрагмента становить максимум 500 символів. Доступно лише при "
"розширеній глибині пошуку."

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "Кількість днів назад від поточної дати для включення"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "Доступно лише якщо тема – новини."

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "Включити відповідь"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"Включити LLM-генеровану відповідь на наданий запит. Базовий пошук повертає "
"швидку відповідь. Розширений повертає більш детальну відповідь."

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "Включити необроблений вміст"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr "Включити очищений та розпарсений HTML-вміст кожного результату пошуку."

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "Включити зображення"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr "Виконати пошук зображень та включити результати у відповідь."

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "Включити описи зображень"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr ""
"Коли увімкнено опцію \"Включити зображення\", також додайте описовий текст "
"для кожного зображення."

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "Включити домени"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr "Список доменів, які слід явно включити в результати пошуку."

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "Виключити домени"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr "Список доменів, які слід явно виключити з результатів пошуку."

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "Регіон"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "Регіон для результатів пошуку"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "Налаштування"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "Назва профілю"

#: src/ui/profile.py:58
#, fuzzy
msgid "Copied Settings"
msgstr "Скопійовані налаштування"

#: src/ui/profile.py:58
msgid "Settings that will be copied to the new profile"
msgstr "Налаштування, які будуть скопійовані в новий профіль"

#: src/ui/profile.py:70
msgid "Create Profile"
msgstr "Створити профіль"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "Імпортувати профіль"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
msgid "Edit Profile"
msgstr "Редагувати профіль"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "Експортувати профіль"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "Експортувати паролі"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "Також експортувати поля, схожі на пароль"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "Експортувати фото профілю"

#: src/ui/profile.py:89
msgid "Also export the profile picture"
msgstr "Також експортувати фотографію профілю"

#: src/ui/profile.py:109 src/ui/explorer.py:692
#, fuzzy
msgid "Create"
msgstr "Створити"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "Застосувати"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr "Налаштування поточного профілю будуть скопійовані в новий"

#: src/ui/profile.py:122 src/ui/profile.py:128
msgid "Newelle Profiles"
msgstr "Профілі Newelle"

#: src/ui/profile.py:123
msgid "Export"
msgstr "Експортувати"

#: src/ui/profile.py:129
msgid "Import"
msgstr "Імпорт"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "Встановити фото профілю"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "Редагування потоку"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "Немає запущених потоків"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "Номер потоку: "

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "Вибрати профіль"

#: src/ui/widgets/profilerow.py:53
msgid "Delete Profile"
msgstr "Видалити профіль"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "Думки"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "Розгорніть, щоб побачити деталі"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "Мислення..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr "LLM мислить... Розгорніть, щоб побачити процес мислення"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "Процес мислення не записано"

#: src/ui/widgets/tipscarousel.py:41
msgid "Newelle Tips"
msgstr "Поради Newelle"

#: src/ui/explorer.py:192
msgid "Folder is Empty"
msgstr "Папка порожня"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr "Файл не знайдено"

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "Відкрити у новій вкладці"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "Відкрити у вбудованому редакторі"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "Відкрити у файловому менеджері"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
msgid "Rename"
msgstr "Перейменувати"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "Видалити"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "Скопіювати повний шлях"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
#, fuzzy
msgid "Failed to open file manager"
msgstr "Не вдалося відкрити файловий менеджер"

#: src/ui/explorer.py:436
#, fuzzy
msgid "New name:"
msgstr "Нове ім'я:"

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "Скасувати"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "Успішно перейменовано"

#: src/ui/explorer.py:476
#, fuzzy, python-brace-format
msgid "Failed to rename: {}"
msgstr "Не вдалося перейменувати: {}"

#: src/ui/explorer.py:497
msgid "Delete File?"
msgstr "Видалити файл?"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "Ви впевнені, що хочете видалити \"{}\"?"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "Успішно видалено"

#: src/ui/explorer.py:525
#, fuzzy, python-brace-format
msgid "Failed to delete: {}"
msgstr "Не вдалося видалити: {}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "Шлях скопійовано в буфер обміну"

#: src/ui/explorer.py:542
#, fuzzy
msgid "Failed to copy path"
msgstr "Не вдалося скопіювати шлях"

#: src/ui/explorer.py:580
#, fuzzy
msgid "Create new folder"
msgstr "Створити нову папку"

#: src/ui/explorer.py:583
msgid "Create new file"
msgstr "Створити новий файл"

#: src/ui/explorer.py:586
msgid "Open Terminal Here"
msgstr "Відкрити термінал тут"

#: src/ui/explorer.py:640
msgid "Create New Folder"
msgstr "Створити нову папку"

#: src/ui/explorer.py:640
#, fuzzy
msgid "Folder name:"
msgstr "Ім'я папки:"

#: src/ui/explorer.py:644
msgid "Create New File"
msgstr "Створити новий файл"

#: src/ui/explorer.py:644
#, fuzzy
msgid "File name:"
msgstr "Ім'я файлу:"

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "Папку успішно створено"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "Файл успішно створено"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "Файл або папка з таким ім'ям вже існує"

#: src/ui/explorer.py:728
#, fuzzy
msgid "folder"
msgstr "папку"

#: src/ui/explorer.py:728
msgid "file"
msgstr "файл"

#: src/ui/explorer.py:730
#, fuzzy, python-brace-format
msgid "Failed to create {}: {}"
msgstr "Не вдалося створити {}: {}"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "Допомога"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "Гарячі клавіші"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "Перезавантажити чат"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "Перезавантажити папку"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "Нова вкладка"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "Вставити зображення"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "Фокусувати вікно повідомлення"

#: src/ui/shortcuts.py:18
#, fuzzy
msgid "Start/stop recording"
msgstr "Почати/зупинити запис"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "Зберегти"

#: src/ui/shortcuts.py:20
#, fuzzy
msgid "Stop TTS"
msgstr "Зупинити TTS"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr "Збільшити"

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr "Зменшити"

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr "Монітор виведення програми"

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr "Очистити вивід"

#: src/ui/stdout_monitor.py:61
#, fuzzy
msgid "Start/Stop monitoring"
msgstr "Почати/зупинити моніторинг"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr "Моніторинг: Активний"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr "Моніторинг: Зупинено"

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr "Рядків: {}"

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr "Рядків: 0"

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "Розширення"

#: src/ui/extension.py:50
#, fuzzy
msgid "Installed Extensions"
msgstr "Встановлені розширення"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "Посібник користувача з розширень"

#: src/ui/extension.py:89
#, fuzzy
msgid "Download new Extensions"
msgstr "Завантажити нові розширення"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "Встановити розширення з файлу..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "Newelle"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "Чат відкрито в міні-вікні"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "Ласкаво просимо до Newelle"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "Ваш ідеальний віртуальний асистент."

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "Сторінка Github"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "Виберіть вашу улюблену мовну модель ШІ"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"Newelle можна використовувати з кількома моделями та провайдерами!\n"
"<b>Примітка: Наполегливо рекомендується прочитати сторінку Посібника з LLM</"
"b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "Посібник з LLM"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "Спілкуйтеся з вашими документами"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr ""
"Newelle може отримувати відповідну інформацію з документів, які ви "
"надсилаєте в чат, або з ваших власних файлів! Інформація, що відповідає "
"вашому запиту, буде надіслана в LLM."

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "Віртуалізація команд"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"Newelle може використовуватися для виконання команд у вашій системі, але "
"звертайте увагу на те, що ви запускаєте! <b>LLM не знаходиться під нашим "
"контролем, тому він може генерувати шкідливий код!</b>\n"
"За замовчуванням, ваші команди будуть <b>віртуалізовані в середовищі "
"Flatpak</b>, але будьте уважні!"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "Ви можете розширити функціональність Newelle за допомогою розширень!"

#: src/ui/presentation.py:136
msgid "Download extensions"
msgstr "Завантажити розширення"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "Помилка доступу"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr "Newelle не має достатньо прав для виконання команд у вашій системі."

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "Почати використання програми"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "Почати спілкування"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "Загальне"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "LLM"

#: src/ui/settings.py:49 src/constants.py:528
#, fuzzy
msgid "Prompts"
msgstr "Запити"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "Знання"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "Мовна модель"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "Інші LLM"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "Інші доступні LLM-провайдери"

#: src/ui/settings.py:73
#, fuzzy
msgid "Advanced LLM Settings"
msgstr "Розширені налаштування LLM"

#: src/ui/settings.py:77
#, fuzzy
msgid "Secondary Language Model"
msgstr "Додаткова мовна модель"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr ""
"Модель, що використовується для вторинних завдань, таких як пропозиція, "
"назва чату та генерація пам'яті"

#: src/ui/settings.py:94
#, fuzzy
msgid "Embedding Model"
msgstr "Модель вбудовування"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"Вбудовування використовується для перетворення тексту на вектори. "
"Використовується довготривалою пам'яттю та RAG. Зміна може вимагати "
"переіндексації документів або скидання пам'яті."

#: src/ui/settings.py:105 src/window.py:647
#, fuzzy
msgid "Long Term Memory"
msgstr "Довготривала пам'ять"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "Зберігати пам'ять про старі розмови"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "Веб-пошук"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "Шукати інформацію в Інтернеті"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "Програма перетворення тексту на мовлення"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "Виберіть, який синтезатор мовлення використовувати"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "Рушій перетворення мовлення на текст"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "Виберіть, який рушій розпізнавання мовлення ви хочете"

#: src/ui/settings.py:150
#, fuzzy
msgid "Automatic Speech To Text"
msgstr "Автоматичне перетворення мовлення на текст"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr "Автоматично перезапускати розпізнавання мови після тексту/TTS"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "Управління запитами"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "Інтерфейс"

#: src/ui/settings.py:162
#, fuzzy
msgid "Interface Size"
msgstr "Розмір інтерфейсу"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "Налаштуйте розмір інтерфейсу"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "Кольорова схема редактора"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "Змінити кольорову схему редактора та блоків коду"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "Приховані файли"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "Показати приховані файли"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "Надіслати за допомогою ENTER"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"Якщо увімкнено, повідомлення будуть надсилатися за допомогою ENTER, для "
"переходу на новий рядок використовуйте CTRL+ENTER. Якщо вимкнено, "
"повідомлення будуть надсилатися за допомогою SHIFT+ENTER, а новий рядок – за "
"допомогою ENTER"

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "Видалити мислення з історії"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr ""
"Не надсилати старі блоки мислення для моделей міркування, щоб зменшити "
"використання токенів"

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "Відображати LaTeX"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "Відображати формули LaTeX у чаті"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "Змінити порядок чатів"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr ""
"Показати найновіші чати вгорі списку чатів (змініть чат, щоб застосувати)"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "Автоматично генерувати назви чатів"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr "Генерувати назви чатів автоматично після перших двох повідомлень"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "Кількість пропозицій"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "Кількість пропозицій повідомлень для відправки в чат "

#: src/ui/settings.py:224
msgid "Username"
msgstr "Ім'я користувача"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr ""
"Змініть мітку, що з'являється перед вашим повідомленням\n"
"Ця інформація не надсилається до LLM за замовчуванням\n"
"Ви можете додати її до запиту за допомогою змінної {USER}"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "Управління нейронною мережею"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "Запускати команди у віртуальній машині"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "Зовнішній термінал"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr "Виберіть зовнішній термінал, де запускатимуться консольні команди"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "Пам'ять програми"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "Як довго програма пам'ятає чат "

#: src/ui/settings.py:266
msgid "Developer"
msgstr "Розробник"

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr ""
"Моніторинг виводу програми в реальному часі, корисно для налагодження та "
"перегляду прогресу завантажень"

#: src/ui/settings.py:270
msgid "Open"
msgstr "Відкрити"

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr "Видалити шлях pip"

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr "Видалити встановлені додаткові залежності"

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr ""

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr ""

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "Автозапуск команд"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "Команди, які напише бот, будуть автоматично виконуватися"

#: src/ui/settings.py:313
#, fuzzy
msgid "Max number of commands"
msgstr "Максимальна кількість команд"

#: src/ui/settings.py:313
#, fuzzy
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr ""
"Максимальна кількість команд, які бот напише після одного запиту користувача"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "Браузер"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "Налаштування браузера"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "Використовувати зовнішній браузер"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr ""
"Використовувати зовнішній браузер для відкриття посилань замість вбудованого"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "Зберігати сесію браузера"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr ""
"Зберігати сесію браузера між перезапусками. Вимкнення цієї функції вимагає "
"перезапуску програми"

#: src/ui/settings.py:361
msgid "Delete browser data"
msgstr "Видалити дані браузера"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "Видалити сесію та дані браузера"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "Початкова сторінка браузера"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "Сторінка, з якої почнеться браузер"

#: src/ui/settings.py:375
#, fuzzy
msgid "Search string"
msgstr "Рядок пошуку"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr "Рядок пошуку, що використовується у браузері, %s замінюється запитом"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "Джерела документів (RAG)"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "Включати вміст з ваших документів у відповіді"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "Аналізатор документів"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr ""
"Аналізатор документів використовує кілька технік для вилучення релевантної "
"інформації з ваших документів"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "Читати документи, якщо не підтримується"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr ""
"Якщо LLM не підтримує читання документів, відповідна інформація про "
"документи, надіслані в чат, буде надана LLM за допомогою вашого аналізатора "
"документів."

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "Максимальна кількість токенів для RAG"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"Максимальна кількість токенів, що будуть використані для RAG. Якщо документи "
"не перевищують цієї кількості токенів,\n"
"включіть їх усі в контекст"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "Папка з документами"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr ""
"Помістіть документи, які ви хочете запитати, у папку документів. Аналізатор "
"документів знайде в них відповідну інформацію, якщо цю опцію увімкнено"

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr "Помістіть усі документи, які ви хочете індексувати, у цю папку"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "Поріг тиші"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr "Поріг тиші в секундах, відсоток гучності, що вважатиметься тишею"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "Час тиші"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr "Час тиші в секундах, перш ніж запис автоматично зупиниться"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "Недостатньо прав"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr ""
"Newelle не має достатньо прав для виконання команд у вашій системі, будь "
"ласка, виконайте наступну команду"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "Зрозуміло"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr "Шлях Pip видалено"

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr ""
"Шлях pip було видалено, тепер ви можете перевстановити залежності. Ця "
"операція вимагає перезапуску програми."

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "Демонстраційний API Newelle"

#: src/constants.py:26
msgid "GPT4Free"
msgstr "GPT4Free"

#: src/constants.py:34
msgid "Local Model"
msgstr "Локальна модель"

#: src/constants.py:35
#, fuzzy
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr ""
"БЕЗ ПІДТРИМКИ GPU, ВИКОРИСТОВУЙТЕ OLLAMA. Запустіть модель LLM локально, "
"більше приватності, але повільніше"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "Екземпляр Ollama"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "Легко запускайте кілька LLM-моделей на власному обладнанні"

#: src/constants.py:47
msgid "Groq"
msgstr "Groq"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "API Google Gemini"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "API OpenAI"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr ""
"API OpenAI. Підтримуються користувацькі кінцеві точки. Використовуйте це для "
"користувацьких провайдерів"

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "Anthropic Claude"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr ""
"Офіційні API для моделей Anthropic Claude, з підтримкою зображень та файлів, "
"потребує API-ключ"

#: src/constants.py:73
#, fuzzy
msgid "Mistral"
msgstr "Містраль"

#: src/constants.py:74
#, fuzzy
msgid "Mistral API"
msgstr "API Містраля"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "API Openrouter.ai, підтримує багато моделей"

#: src/constants.py:87
msgid "Deepseek"
msgstr "Deepseek"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "API Deepseek, найсильніші моделі з відкритим вихідним кодом"

#: src/constants.py:94 src/constants.py:203
msgid "Custom Command"
msgstr "Користувацька команда"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "Використовувати вивід користувацької команди"

#: src/constants.py:104
#, fuzzy
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr "Працює офлайн. Оптимізована реалізація Whisper, написана на C++"

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "Працює офлайн. Підтримується лише англійська мова"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "Розпізнавання мови Google"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "Онлайн-розпізнавання мови Google"

#: src/constants.py:124
#, fuzzy
msgid "Groq Speech Recognition"
msgstr "Розпізнавання мови Groq"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit AI"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr ""
"Безкоштовний API розпізнавання мови wit.ai (мова обирається на веб-сайті)"

#: src/constants.py:137
msgid "Vosk API"
msgstr "API Vosk"

#: src/constants.py:138
msgid "Works Offline"
msgstr "Працює офлайн"

#: src/constants.py:144
msgid "Whisper API"
msgstr "API Whisper"

#: src/constants.py:145
#, fuzzy
msgid "Uses OpenAI Whisper API"
msgstr "Використовує API OpenAI Whisper"

#: src/constants.py:151
msgid "Custom command"
msgstr "Користувацька команда"

#: src/constants.py:152
msgid "Runs a custom command"
msgstr "Виконує користувацьку команду"

#: src/constants.py:161
msgid "Google TTS"
msgstr "Google TTS"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "Перетворення тексту на мовлення від Google"

#: src/constants.py:167
#, fuzzy
msgid "Kokoro TTS"
msgstr "Kokoro TTS"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr ""
"Легкий та швидкий механізм TTS з відкритим вихідним кодом. Залежності ~3 ГБ, "
"модель 400 МБ"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "ElevenLabs TTS"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "Природне звучання TTS"

#: src/constants.py:179 src/constants.py:180
#, fuzzy
msgid "OpenAI TTS"
msgstr "OpenAI TTS"

#: src/constants.py:185
#, fuzzy
msgid "Groq TTS"
msgstr "Groq TTS"

#: src/constants.py:186
#, fuzzy
msgid "Groq TTS API"
msgstr "API Groq TTS"

#: src/constants.py:191 src/constants.py:192
#, fuzzy
msgid "Custom OpenAI TTS"
msgstr "Користувацький OpenAI TTS"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "Espeak TTS"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "Офлайн TTS"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr ""
"Використовувати користувацьку команду як TTS, {0} буде замінено текстом"

#: src/constants.py:212
msgid "WordLlama"
msgstr "WordLlama"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr ""
"Легка локальна модель вбудовування на основі llama. Працює офлайн, дуже "
"низьке використання ресурсів"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "Вбудовування Ollama"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr ""
"Використовувати моделі Ollama для вбудовування. Працює офлайн, дуже низьке "
"використання ресурсів"

#: src/constants.py:231
#, fuzzy
msgid "Use Google Gemini API to get embeddings"
msgstr "Використовувати API Google Gemini для отримання вбудовувань"

#: src/constants.py:239
msgid "User Summary"
msgstr "Користувацький підсумок"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "Створити підсумок розмови користувача"

#: src/constants.py:245
msgid "Memoripy"
msgstr "Memoripy"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"Витягує повідомлення з попередніх розмов за допомогою контекстного "
"відновлення пам'яті, згасання пам'яті, вилучення концептів та інших "
"розширених технік. Виконує 1 виклик llm за повідомлення."

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "Користувацький підсумок + Memoripy"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "Використовуйте обидві технології для довготривалої пам'яті"

#: src/constants.py:260
msgid "Document reader"
msgstr "Читач документів"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr ""
"Класичний підхід RAG – розбиття документів на фрагменти та їх вбудовування, "
"потім порівняння з запитом та повернення найбільш релевантних документів"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr ""
"SearXNG - Приватний пошуковий двигун з можливістю самостійного розміщення"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "Пошук DuckDuckGo"

#: src/constants.py:281
msgid "Tavily"
msgstr "Tavily"

#: src/constants.py:282
msgid "Tavily search"
msgstr "Пошук Tavily"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "Корисний помічник"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr ""
"Загальний запит для покращення відповідей LLM та надання більшого контексту"

#: src/constants.py:384
msgid "Console access"
msgstr "Доступ до консолі"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "Чи може програма виконувати команди терміналу на комп'ютері"

#: src/constants.py:392
msgid "Current directory"
msgstr "Поточна директорія"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "Яка поточна директорія"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "Дозволити LLM шукати в Інтернеті"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "Базова функціональність"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "Показ таблиць та коду (*може працювати без цього)"

#: src/constants.py:419
msgid "Graphs access"
msgstr "Доступ до графіків"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "Чи може програма відображати графіки"

#: src/constants.py:428
msgid "Show image"
msgstr "Показати зображення"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "Показати зображення в чаті"

#: src/constants.py:437
msgid "Custom Prompt"
msgstr "Користувацький запит"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "Додайте свій власний користувацький запит"

#: src/constants.py:480
#, fuzzy
msgid "LLM and Secondary LLM settings"
msgstr "Налаштування LLM та вторинної LLM"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "TTS"

#: src/constants.py:485
#, fuzzy
msgid "Text to Speech settings"
msgstr "Налаштування Text to Speech"

#: src/constants.py:488
msgid "STT"
msgstr "STT"

#: src/constants.py:490
#, fuzzy
msgid "Speech to Text settings"
msgstr "Налаштування Speech to Text"

#: src/constants.py:493
msgid "Embedding"
msgstr "Вбудовування"

#: src/constants.py:495
#, fuzzy
msgid "Embedding settings"
msgstr "Налаштування вбудовування"

#: src/constants.py:498
msgid "Memory"
msgstr "Пам'ять"

#: src/constants.py:500
#, fuzzy
msgid "Memory settings"
msgstr "Налаштування пам'яті"

#: src/constants.py:503
msgid "Websearch"
msgstr "Вебпошук"

#: src/constants.py:505
#, fuzzy
msgid "Websearch settings"
msgstr "Налаштування вебпошуку"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
msgid "Document analyzer settings"
msgstr "Налаштування аналізатора документів"

#: src/constants.py:515
#, fuzzy
msgid "Extensions settings"
msgstr "Налаштування розширень"

#: src/constants.py:518
#, fuzzy
msgid "Inteface"
msgstr "Інтерфейс"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr ""
"Налаштування інтерфейсу, приховані файли, зворотний порядок, масштаб..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr ""
"Загальні налаштування, віртуалізація, пропозиції, довжина пам'яті, "
"автоматичне генерування назви чату, поточна папка..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr ""
"Налаштування запитів, користувацькі додаткові запити, користувацькі запити..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "Чат "

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "Потоки терміналу все ще виконуються у фоновому режимі"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "Коли ви закриєте вікно, вони будуть автоматично завершені"

#: src/main.py:210
msgid "Close"
msgstr "Закрити"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "Чат перезавантажено"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "Папку перезавантажено"

#: src/main.py:254
msgid "Chat is created"
msgstr "Чат створено"

#: src/window.py:120
msgid "Keyboard shorcuts"
msgstr "Гарячі клавіші"

#: src/window.py:121
msgid "About"
msgstr "Про програму"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "Чат"

#: src/window.py:170
msgid "History"
msgstr "Історія"

#: src/window.py:191
msgid "Create a chat"
msgstr "Створити чат"

#: src/window.py:196
#, fuzzy
msgid "Chats"
msgstr "Чати"

#: src/window.py:267
msgid " Stop"
msgstr "Зупинити"

#: src/window.py:282
msgid " Clear"
msgstr "Очистити"

#: src/window.py:297
msgid " Continue"
msgstr "Продовжити"

#: src/window.py:310
msgid " Regenerate"
msgstr "Перегенерувати"

#: src/window.py:376
msgid "Send a message..."
msgstr "Надіслати повідомлення..."

#: src/window.py:467
msgid "Explorer Tab"
msgstr "Вкладка \"Провідник\""

#: src/window.py:468
msgid "Terminal Tab"
msgstr "Вкладка \"Термінал\""

#: src/window.py:469
msgid "Browser Tab"
msgstr "Вкладка \"Браузер\""

#: src/window.py:589
msgid "Ask about a website"
msgstr "Запитайте про вебсайт"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr ""
"Напишіть #https://website.com в чаті, щоб запитати інформацію про вебсайт"

#: src/window.py:590
msgid "Check out our Extensions!"
msgstr "Перегляньте наші розширення!"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "У нас є багато розширень для різних цілей. Перегляньте їх!"

#: src/window.py:591
msgid "Chat with documents!"
msgstr "Спілкуйтеся з документами!"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr ""
"Додайте свої документи до папки документів та спілкуйтеся, використовуючи "
"інформацію, що міститься в них!"

#: src/window.py:592
msgid "Surf the web!"
msgstr "Переглядайте веб!"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr ""
"Увімкніть веб-пошук, щоб дозволити LLM переглядати веб-сторінки та надавати "
"актуальні відповіді"

#: src/window.py:593
msgid "Mini Window"
msgstr "Міні-вікно"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "Задавайте питання на льоту, використовуючи режим міні-вікна"

#: src/window.py:594
#, fuzzy
msgid "Text to Speech"
msgstr "Перетворення тексту на мовлення"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr "Newelle підтримує синтез мовлення! Увімкніть його в налаштуваннях"

#: src/window.py:595
#, fuzzy
msgid "Keyboard Shortcuts"
msgstr "Гарячі клавіші"

#: src/window.py:595
#, fuzzy
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "Керуйте Newelle за допомогою гарячих клавіш"

#: src/window.py:596
#, fuzzy
msgid "Prompt Control"
msgstr "Управління запитами"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr ""
"Newelle надає вам 100% контроль над запитами. Налаштуйте свої запити для "
"вашого використання."

#: src/window.py:597
#, fuzzy
msgid "Thread Editing"
msgstr "Редагування потоку"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "Перевірте програми та процеси, які ви запускаєте з Newelle"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr "Програмовані запити"

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr ""
"Ви можете додавати динамічні запити до Newelle, з умовами та ймовірностями"

#: src/window.py:605
#, fuzzy
msgid "New Chat"
msgstr "Новий чат"

#: src/window.py:623
msgid "Provider Errror"
msgstr "Помилка провайдера"

#: src/window.py:646
#, fuzzy
msgid "Local Documents"
msgstr "Локальні документи"

#: src/window.py:650
msgid "Web search"
msgstr "Веб-пошук"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "Цей провайдер не має списку моделей"

#: src/window.py:901
#, fuzzy
msgid " Models"
msgstr "Моделі"

#: src/window.py:904
#, fuzzy
msgid "Search Models..."
msgstr "Пошук моделей..."

#: src/window.py:1132
msgid "Create new profile"
msgstr "Створити новий профіль"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "Не вдалося розпізнати ваш голос"

#: src/window.py:1303
msgid "Images"
msgstr "Зображення"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "Файли, підтримувані LLM"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "Файли, підтримувані RAG"

#: src/window.py:1333
msgid "Supported Files"
msgstr "Підтримувані файли"

#: src/window.py:1337
msgid "All Files"
msgstr "Усі файли"

#: src/window.py:1343
msgid "Attach file"
msgstr "Прикріпити файл"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "Файл не може бути відправлений, поки програма не завершить роботу"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "Файл не розпізнано"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "Ви більше не можете продовжити повідомлення."

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "Ви більше не можете перегенерувати повідомлення."

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "Чат очищено"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "Повідомлення було скасовано та видалено з історії"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr ""
"Повідомлення не може бути відправлене, поки програма не завершить роботу"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr "Ви не можете редагувати повідомлення, поки програма виконується."

#: src/window.py:3080
#, fuzzy
msgid "Prompt content"
msgstr "Вміст запиту"

#: src/window.py:3339
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"Нейронна мережа має доступ до вашого комп'ютера та будь-яких даних у цьому "
"чаті і може виконувати команди, будьте обережні, ми не несемо "
"відповідальності за нейронну мережу. Не діліться конфіденційною інформацією."

#: src/window.py:3368
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"Нейронна мережа має доступ до будь-яких даних у цьому чаті, будьте обережні, "
"ми не несемо відповідальності за нейронну мережу. Не діліться конфіденційною "
"інформацією."

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "Неправильний шлях до папки"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "Потік не завершено, номер потоку: "

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "Не вдалося відкрити папку"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "Чат порожній"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr ""
"Навчіть Newelle робити більше за допомогою користувацьких розширень та нових "
"модулів ШІ, надаючи вашому чат-боту безмежні можливості."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "Чат-бот зі штучним інтелектом"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "Швидкий вибір профілю"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
#, fuzzy
msgid "Message Editing"
msgstr "Редагування повідомлень"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "Більше 10 стандартних AI-провайдерів"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "Виправлення помилок"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
#, fuzzy
msgid "Minor bug fixes"
msgstr "Виправлення помилок"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "Невеликі покращення"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "Покращено продуктивність читання та завантаження локальних документів"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "Додати опцію надсилання за допомогою CTRL+Enter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "Покращити блоки коду"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
#, fuzzy
msgid "Fix Kokoro TTS"
msgstr "Виправлено Kokoro TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "Видалити емодзі з TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "Встановити ключі API як поля пароля"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "Додати підтримку мислення для Gemini"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "Оновлені переклади"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "Додано нові функції"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr ""
"Читання веб-сайтів та веб-пошук за допомогою SearXNG, DuckDuckGo та Tavily"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "Покращено рендеринг LaTeX та керування документами"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "Новий віджет \"Мислення\" та обробник OpenRouter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "Підтримка Vision для Llama4 на Groq"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "Нові переклади (традиційна китайська, бенгальська, гінді)"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "Виправлено багато помилок, додано деякі функції!"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "Підтримка нових функцій та виправлення помилок"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "Додано багато нових функцій та виправлень помилок"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "Додано нові функції та виправлення помилок"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr ""
"Оновлено бібліотеку g4f з версіонуванням, додано посібники користувача, "
"покращено перегляд розширень та обробку моделей."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"Виправлено помилки та реалізовано нові функції. Ми змінили архітектуру "
"розширень, додали нові моделі, запровадили підтримку бачення та інші "
"можливості."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "Стабільна версія"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
#, fuzzy
msgid "Added extension"
msgstr "Додано розширення"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
#, fuzzy
msgid "Blacklist of commands"
msgstr "Чорний список команд"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "Локалізація"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "Редизайн"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "Qwersyk"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "Newelle: Ваш розширений чат-бот"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "ai;assistant;chat;chatgpt;gpt;llm;ollama;"

#, fuzzy
#~ msgid "max Tokens"
#~ msgstr "Максимальна кількість токенів"

#~ msgid "Max tokens of the generated text"
#~ msgstr "Максимальна кількість токенів згенерованого тексту"

#, fuzzy
#~ msgid "Any free Provider"
#~ msgstr "Будь-який вільний провайдер"

#, fuzzy
#~ msgid "_Cancel"
#~ msgstr "Скасувати"

#, fuzzy, python-brace-format
#~ msgid "Name of the {provider_name} Model"
#~ msgstr "Назва моделі {provider_name}"

#~ msgid "Choose an extension"
#~ msgstr "Виберіть розширення"

#~ msgid " has been removed"
#~ msgstr " було видалено"

#~ msgid "Extension added. New extensions will run from the next launch"
#~ msgstr "Розширення додано. Нові розширення запрацюють з наступного запуску"

#~ msgid "The extension is wrong"
#~ msgstr "Розширення некоректне"

#~ msgid "This is not an extension"
#~ msgstr "Це не розширення"

#~ msgid ""
#~ "AI Model to use, available: gemini-1.5-pro, gemini-1.0-pro, gemini-1.5-"
#~ "flash"
#~ msgstr ""
#~ "AI-модель для використання, доступні: gemini-1.5-pro, gemini-1.0-pro, "
#~ "gemini-1.5-flash"

#~ msgid "OpenAI Model"
#~ msgstr "Модель OpenAI"

#~ msgid ""
#~ "Positive values penalize new tokens based on their existing frequency in "
#~ "the text so far, decreasing the model's likelihood to repeat the same line"
#~ msgstr ""
#~ "Позитивні значення штрафують нові токени на основі їх існуючої частоти в "
#~ "тексті, зменшуючи ймовірність повторення моделлю того самого рядка"

#~ msgid ""
#~ "Positive values penalize new tokens based on whether they appear in the "
#~ "text so far, increasing the model's likelihood to talk about new topics."
#~ msgstr ""
#~ "Позитивні значення штрафують нові токени на основі того, чи з'являються "
#~ "вони в тексті, збільшуючи ймовірність того, що модель говоритиме про нові "
#~ "теми."

#~ msgid "There was an error retriving the model"
#~ msgstr "Виникла помилка під час отримання моделі"

#~ msgid "The change will take effect after you restart the program."
#~ msgstr "Зміни набудуть чинності після перезапуску програми."
