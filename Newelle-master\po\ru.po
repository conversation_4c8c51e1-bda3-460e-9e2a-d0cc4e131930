msgid ""
msgstr ""
"Project-Id-Version: Newelle 0.2.1\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-07-03 09:00+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Russian\n"
"Language: ru\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=3; plural=(n%10==1 && n%100!=11 ? 0 : n%10>=2 && "
"n%10<=4 && (n%100<10 || n%100>=20) ? 1 : 2);\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "Конечная точка API"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr "Базовый URL-адрес API, измените его для использования API-интерфейсов"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid "Automatically Serve"
msgstr "Автоматический запуск"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"Автоматически запускать ollama serve в фоновом режиме, если это необходимо и "
"он не запущен. Вы можете остановить его с помощью killall ollama"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
#, fuzzy
msgid "Custom Model"
msgstr "Пользовательская модель"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
msgid "Use a custom model"
msgstr "Использовать пользовательскую модель"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
msgid "Ollama Model"
msgstr "Модель Ollama"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
msgid "Name of the Ollama Model"
msgstr "Название модели Ollama"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "Ключ API"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
msgid "API Key for "
msgstr "Ключ API для "

#: src/handlers/embeddings/openai_handler.py:38
msgid "API base url, change this to use different APIs"
msgstr "Базовый URL-адрес API, измените его для использования других API"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
#, fuzzy
msgid "Use Custom Model"
msgstr "Использовать пользовательскую модель"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "Модель"

#: src/handlers/embeddings/openai_handler.py:44
msgid "Name of the Embedding Model to use"
msgstr "Название модели эмбеддинга для использования"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
msgid " Model"
msgstr " Модель"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
msgid "The API key to use"
msgstr "Ключ API для использования"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
msgid "The model to use"
msgstr "Модель для использования"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "Максимальное количество токенов"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "Максимальное количество токенов для генерации"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
#, fuzzy
msgid "Message Streaming"
msgstr "Потоковая передача сообщений"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "Постепенно выводить сообщения"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "Команда для выполнения, чтобы получить вывод бота"

#: src/handlers/llm/custom_handler.py:21
#, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr ""
"Команда для получения ответа бота, {0} будет заменено JSON-файлом, "
"содержащим чат, {1} — системной подсказкой"

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "Команда для получения предложений бота"

#: src/handlers/llm/custom_handler.py:22
#, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"Команда для получения предложений чата, {0} будет заменено JSON-файлом, "
"содержащим чат, {1} — дополнительными подсказками, {2} — количеством "
"предложений для генерации. Должен возвращать массив JSON, содержащий "
"предложения в виде строк"

#: src/handlers/llm/gpt4all_handler.py:67
msgid "RAM Required: "
msgstr "Требуется ОЗУ: "

#: src/handlers/llm/gpt4all_handler.py:68
msgid "Parameters: "
msgstr "Параметры: "

#: src/handlers/llm/gpt4all_handler.py:69
msgid "Size: "
msgstr "Размер: "

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
msgid "Model to use"
msgstr "Модель для использования"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
msgid "Name of the model to use"
msgstr "Название используемой модели"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "Model Manager"
msgstr "Менеджер моделей"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "Список доступных моделей"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr "Обновить G4F"

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "Политика конфиденциальности"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "Открыть сайт политики конфиденциальности"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "Включить размышления"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr "Разрешить размышления в модели, поддерживаются только некоторые модели"

#: src/handlers/llm/ollama_handler.py:176
msgid "Add custom model"
msgstr "Добавить пользовательскую модель"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"Добавьте любую модель в этот список, указав имя:размер\n"
"Или любой gguf из hf с hf.co/username/model"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr "Обновить Ollama"

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "Ключ API (обязательно)"

#: src/handlers/llm/gemini_handler.py:94
msgid "API key got from ai.google.dev"
msgstr "Ключ API, полученный с ai.google.dev"

#: src/handlers/llm/gemini_handler.py:98
msgid "AI Model to use"
msgstr "Используемая модель ИИ"

#: src/handlers/llm/gemini_handler.py:103
msgid "Enable System Prompt"
msgstr "Включить системную подсказку"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr ""
"Некоторые модели не поддерживают системные подсказки (или инструкции "
"разработчиков), отключите их, если получаете ошибки."

#: src/handlers/llm/gemini_handler.py:107
msgid "Inject system prompt"
msgstr "Внедрить системную подсказку"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr ""
"Даже если модель не поддерживает системные подсказки, разместите подсказки "
"поверх сообщения пользователя."

#: src/handlers/llm/gemini_handler.py:109
#, fuzzy
msgid "Thinking Settings"
msgstr "Настройки размышлений"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "Настройки моделей размышлений"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr ""
"Показывать размышления, отключите, если ваша модель не поддерживает это"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "Включить бюджет на размышления"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "Включить ли бюджет на размышления"

#: src/handlers/llm/gemini_handler.py:112
msgid "Thinking Budget"
msgstr "Бюджет на размышления"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "Сколько времени уделять размышлениям"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "Вывод изображений"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr "Включить вывод изображений, поддерживается только gemini-2.0-flash-exp"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "Включить настройки безопасности"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr ""
"Включить настройки безопасности Google, чтобы избежать генерации "
"вредоносного контента"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
msgid "Advanced Parameters"
msgstr "Расширенные параметры"

#: src/handlers/llm/gemini_handler.py:135
msgid "Enable advanced parameters"
msgstr "Включить расширенные параметры"

#: src/handlers/llm/openai_handler.py:81
#, fuzzy
msgid "Include parameters like Top-P, Temperature, etc."
msgstr "Включить параметры, такие как Max Tokens, Top-P, Temperature и т. д."

#: src/handlers/llm/openai_handler.py:84
msgid "Name of the LLM Model to use"
msgstr "Название модели LLM для использования"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "Top-P"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr "Альтернатива выборке с температурой, называемая ядерной выборкой"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "Температура"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr ""
"Какую температуру выборки использовать. Более высокие значения сделают вывод "
"более случайным"

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "Штраф за частоту"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr ""
"Число от -2.0 до 2.0. Положительные значения уменьшают вероятность того, что "
"модель повторит ту же строку дословно"

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "Штраф за присутствие"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr ""
"Число от -2.0 до 2.0. Положительные значения уменьшают вероятность того, что "
"модель будет говорить о новых темах"

#: src/handlers/llm/openai_handler.py:108
#, fuzzy
msgid "Custom Options"
msgstr "Пользовательская подсказка"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr ""

#: src/handlers/llm/openrouter_handler.py:14
msgid "Provider Sorting"
msgstr "Сортировка поставщиков"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "Выбирайте поставщиков по ценам/пропускной способности или задержке"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "Цена"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "Пропускная способность"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "Задержка"

#: src/handlers/llm/openrouter_handler.py:15
msgid "Providers Order"
msgstr "Порядок поставщиков"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr ""
"Добавьте порядок использования поставщиков, имена разделены запятой.\n"
"Оставьте пустым, чтобы не указывать"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "Разрешить откат"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "Разрешить откат к другим поставщикам"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "Индексировать ваши документы"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"Индексируйте все документы в вашей папке документов. Вы должны выполнять эту "
"операцию каждый раз, когда редактируете/создаете документ, меняете "
"анализатор документов или меняете модель эмбеддинга"

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "Команда для выполнения"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "{0} будет заменен полным путем к модели"

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr ""
"Ключ API для Google SR, напишите 'default' для использования значения по "
"умолчанию"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "Язык"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "Язык текста для распознавания в формате IETF"

#: src/handlers/stt/groqsr_handler.py:14
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr ""
"Ключ API для Groq SR, напишите 'default' для использования значения по "
"умолчанию"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "Модель Groq"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "Название модели Groq"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Укажите язык для транскрипции. Используйте коды языков ISO 639-1 (например, "
"\"en\" для английского, \"fr\" для французского и т. д.). "

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "Конечная точка для запросов OpenAI"

#: src/handlers/stt/openaisr_handler.py:18
msgid "API Key for OpenAI"
msgstr "Ключ API для OpenAI"

#: src/handlers/stt/openaisr_handler.py:25
msgid "Whisper Model"
msgstr "Модель Whisper"

#: src/handlers/stt/openaisr_handler.py:26
msgid "Name of the OpenAI model"
msgstr "Название модели OpenAI"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Необязательно: укажите язык для транскрипции. Используйте коды языков ISO "
"639-1 (например, «en» для английского, «fr» для французского и т. д.). "

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "Путь к модели"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "Абсолютный путь к модели VOSK (распакованной)"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
msgid "Name of the Whisper model"
msgstr "Название модели Whisper"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "Токен доступа к серверу для wit.ai"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "Не удалось понять аудио"

#: src/handlers/stt/whispercpp_handler.py:47
#, fuzzy
msgid "Language of the recognition. For example en, it..."
msgstr "Язык распознавания."

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Model Library"
msgstr "Библиотека моделей"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Manage Whisper models"
msgstr "Управление моделями Whisper"

#: src/handlers/stt/whispercpp_handler.py:49
#, fuzzy
msgid "Advanced Settings"
msgstr "Расширенные настройки"

#: src/handlers/stt/whispercpp_handler.py:49
#, fuzzy
msgid "More advanced settings"
msgstr "Более расширенные настройки"

#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature to use"
msgstr "Температура для использования"

#: src/handlers/stt/whispercpp_handler.py:51
#, fuzzy
msgid "Prompt for the recognition"
msgstr "Подсказка для распознавания"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr "Подсказка для использования при распознавании"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Endpoint"
msgstr "Конечная точка"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "Пользовательская конечная точка службы для использования"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "Голос"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
msgid "The voice to use"
msgstr "Голос для использования"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "Инструкции"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr ""
"Инструкции для генерации голоса. Оставьте пустым, чтобы пропустить поле"

#: src/handlers/tts/custom_handler.py:17
#, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr "{0} будет заменен полным путем к файлу, {1} — текстом"

#: src/handlers/tts/elevenlabs_handler.py:11
msgid "API Key for ElevenLabs"
msgstr "Ключ API для ElevenLabs"

#: src/handlers/tts/elevenlabs_handler.py:19
msgid "Voice ID to use"
msgstr "Идентификатор голоса для использования"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "Стабильность"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "Стабильность голоса"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "Увеличение схожести"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "Повышает общую четкость голоса и схожесть с говорящим"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "Преувеличение стиля"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr ""
"Высокие значения рекомендуются, если стиль речи должен быть преувеличен"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "Выберите предпочитаемый голос"

#: src/handlers/websearch/tavily.py:20
msgid "Token"
msgstr "Токен"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "Ключ API Tavily"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "Макс. результатов"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "Количество результатов для рассмотрения"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "Глубина поиска"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"Глубина поиска. Расширенный поиск настроен для извлечения наиболее "
"релевантных источников и фрагментов контента для вашего запроса, в то время "
"как базовый поиск предоставляет общие фрагменты контента из каждого "
"источника. Базовый поиск стоит 1 API-кредит, а расширенный — 2 API-кредита."

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "Категория поиска"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"Категория поиска. Новости полезны для получения обновлений в реальном "
"времени, особенно о политике, спорте и крупных текущих событиях, освещаемых "
"основными СМИ. Общие — для более широких, более общих поисков, которые могут "
"включать широкий спектр источников."

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "Блоки на источник"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr ""
"Количество фрагментов контента для извлечения из каждого источника. Длина "
"каждого фрагмента не более 500 символов. Доступно только при расширенном "
"поиске."

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "Количество дней назад от текущей даты для включения"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "Доступно только если тема - новости."

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "Включить ответ"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"Включите ответ, сгенерированный LLM, на предоставленный запрос. Базовый "
"поиск возвращает быстрый ответ. Расширенный возвращает более подробный ответ."

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "Включить необработанный контент"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr ""
"Включить очищенный и разобранный HTML-контент каждого результата поиска."

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "Включить изображения"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr "Выполнить поиск изображений и включить результаты в ответ."

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "Включить описания изображений"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr ""
"Когда включено «Включить изображения», также добавьте описательный текст для "
"каждого изображения."

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "Включить домены"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr "Список доменов для включения в результаты поиска."

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "Исключить домены"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr "Список доменов для исключения из результатов поиска."

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "Регион"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "Регион для результатов поиска"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "Настройки"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "Имя профиля"

#: src/ui/profile.py:58
#, fuzzy
msgid "Copied Settings"
msgstr "Скопированные настройки"

#: src/ui/profile.py:58
msgid "Settings that will be copied to the new profile"
msgstr "Настройки, которые будут скопированы в новый профиль"

#: src/ui/profile.py:70
msgid "Create Profile"
msgstr "Создать профиль"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "Импортировать профиль"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
msgid "Edit Profile"
msgstr "Редактировать профиль"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "Экспортировать профиль"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "Экспортировать пароли"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "Также экспортировать поля типа паролей"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "Экспортировать фото профиля"

#: src/ui/profile.py:89
msgid "Also export the profile picture"
msgstr "Также экспортировать изображение профиля"

#: src/ui/profile.py:109 src/ui/explorer.py:692
#, fuzzy
msgid "Create"
msgstr "Создать"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "Применить"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr "Настройки текущего профиля будут скопированы в новый"

#: src/ui/profile.py:122 src/ui/profile.py:128
msgid "Newelle Profiles"
msgstr "Профили Newelle"

#: src/ui/profile.py:123
msgid "Export"
msgstr "Экспорт"

#: src/ui/profile.py:129
msgid "Import"
msgstr "Импорт"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "Установить изображение профиля"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "Редактирование потока"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "Нет запущенных потоков"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "Номер потока: "

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "Выбрать профиль"

#: src/ui/widgets/profilerow.py:53
msgid "Delete Profile"
msgstr "Удалить профиль"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "Мысли"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "Развернуть, чтобы увидеть детали"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "Размышления..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr "LLM размышляет... Разверните, чтобы увидеть ход мыслей"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "Процесс размышлений не записан"

#: src/ui/widgets/tipscarousel.py:41
msgid "Newelle Tips"
msgstr "Советы Newelle"

#: src/ui/explorer.py:192
msgid "Folder is Empty"
msgstr "Папка пуста"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr "Файл не найден"

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "Открыть в новой вкладке"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "Открыть во встроенном редакторе"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "Открыть в файловом менеджере"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
msgid "Rename"
msgstr "Переименовать"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "Удалить"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "Скопировать полный путь"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
#, fuzzy
msgid "Failed to open file manager"
msgstr "Не удалось открыть файловый менеджер"

#: src/ui/explorer.py:436
#, fuzzy
msgid "New name:"
msgstr "Новое имя:"

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "Отмена"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "Успешно переименовано"

#: src/ui/explorer.py:476
#, fuzzy, python-brace-format
msgid "Failed to rename: {}"
msgstr "Не удалось переименовать: {}"

#: src/ui/explorer.py:497
msgid "Delete File?"
msgstr "Удалить файл?"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "Вы уверены, что хотите удалить «{}»?"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "Успешно удалено"

#: src/ui/explorer.py:525
#, fuzzy, python-brace-format
msgid "Failed to delete: {}"
msgstr "Не удалось удалить: {}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "Путь скопирован в буфер обмена"

#: src/ui/explorer.py:542
#, fuzzy
msgid "Failed to copy path"
msgstr "Не удалось скопировать путь"

#: src/ui/explorer.py:580
#, fuzzy
msgid "Create new folder"
msgstr "Создать новую папку"

#: src/ui/explorer.py:583
msgid "Create new file"
msgstr "Создать новый файл"

#: src/ui/explorer.py:586
msgid "Open Terminal Here"
msgstr "Открыть терминал здесь"

#: src/ui/explorer.py:640
msgid "Create New Folder"
msgstr "Создать новую папку"

#: src/ui/explorer.py:640
#, fuzzy
msgid "Folder name:"
msgstr "Имя папки:"

#: src/ui/explorer.py:644
msgid "Create New File"
msgstr "Создать новый файл"

#: src/ui/explorer.py:644
#, fuzzy
msgid "File name:"
msgstr "Имя файла:"

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "Папка успешно создана"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "Файл успешно создан"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "Файл или папка с таким именем уже существует"

#: src/ui/explorer.py:728
#, fuzzy
msgid "folder"
msgstr "папка"

#: src/ui/explorer.py:728
msgid "file"
msgstr "файл"

#: src/ui/explorer.py:730
#, fuzzy, python-brace-format
msgid "Failed to create {}: {}"
msgstr "Не удалось создать {}: {}"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "Помощь"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "Быстрые клавиши"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "Перезагрузить чат"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "Перезагрузить папку"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "Новая вкладка"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "Вставить изображение"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "Фокусировать поле сообщения"

#: src/ui/shortcuts.py:18
msgid "Start/stop recording"
msgstr "Начать/остановить запись"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "Сохранить"

#: src/ui/shortcuts.py:20
#, fuzzy
msgid "Stop TTS"
msgstr "Остановить TTS"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr "Увеличить"

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr "Уменьшить"

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr "Монитор вывода программы"

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr "Очистить вывод"

#: src/ui/stdout_monitor.py:61
msgid "Start/Stop monitoring"
msgstr "Начать/Остановить мониторинг"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr "Мониторинг: Активен"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr "Мониторинг: Остановлен"

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr "Строки: {}"

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr "Строки: 0"

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "Расширения"

#: src/ui/extension.py:50
#, fuzzy
msgid "Installed Extensions"
msgstr "Установленные расширения"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "Руководство пользователя по расширениям"

#: src/ui/extension.py:89
#, fuzzy
msgid "Download new Extensions"
msgstr "Загрузить новые расширения"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "Установить расширение из файла..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "Newelle"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "Чат открыт в мини-окне"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "Добро пожаловать в Newelle"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "Ваш идеальный виртуальный помощник."

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "Страница Github"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "Выберите свою любимую языковую модель ИИ"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"Newelle может использоваться с несколькими моделями и провайдерами!\n"
"<b>Примечание: Настоятельно рекомендуется прочитать страницу Руководство по "
"LLM</b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "Руководство по LLM"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "Общайтесь со своими документами"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr ""
"Newelle может извлекать соответствующую информацию из документов, которые вы "
"отправляете в чат, или из ваших собственных файлов! Информация, относящаяся "
"к вашему запросу, будет отправлена в LLM."

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "Виртуализация команд"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"Newelle может использоваться для выполнения команд в вашей системе, но "
"будьте внимательны к тому, что вы запускаете! <b>LLM не находится под нашим "
"контролем, поэтому он может генерировать вредоносный код!</b>\n"
"По умолчанию ваши команды будут <b>виртуализированы в среде Flatpak</b>, но "
"будьте внимательны!"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "Вы можете расширить функциональность Newelle с помощью расширений!"

#: src/ui/presentation.py:136
#, fuzzy
msgid "Download extensions"
msgstr "Загрузить расширения"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "Ошибка разрешений"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr ""
"У Newelle недостаточно разрешений для выполнения команд в вашей системе."

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "Начать использовать приложение"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "Начать чат"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "Общие"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "LLM"

#: src/ui/settings.py:49 src/constants.py:528
msgid "Prompts"
msgstr "Подсказки"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "Знания"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "Языковая модель"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "Другие LLM"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "Другие доступные провайдеры LLM"

#: src/ui/settings.py:73
#, fuzzy
msgid "Advanced LLM Settings"
msgstr "Расширенные настройки LLM"

#: src/ui/settings.py:77
msgid "Secondary Language Model"
msgstr "Вторичная языковая модель"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr ""
"Модель, используемая для второстепенных задач, таких как предложение, имя "
"чата и генерация памяти"

#: src/ui/settings.py:94
msgid "Embedding Model"
msgstr "Модель внедрения"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"Внедрение используется для преобразования текста в векторы. Используется "
"долговременной памятью и RAG. Изменение может потребовать повторной "
"индексации документов или сброса памяти."

#: src/ui/settings.py:105 src/window.py:647
#, fuzzy
msgid "Long Term Memory"
msgstr "Долговременная память"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "Сохранять память о старых разговорах"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "Веб-поиск"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "Искать информацию в Интернете"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "Программа преобразования текста в речь"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "Выберите, какую программу преобразования текста в речь использовать"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "Движок преобразования речи в текст"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "Выберите, какой движок распознавания речи вы хотите использовать"

#: src/ui/settings.py:150
msgid "Automatic Speech To Text"
msgstr "Автоматическое преобразование речи в текст"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr ""
"Автоматически перезапускать преобразование речи в текст в конце текста/TTS"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "Управление подсказками"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "Интерфейс"

#: src/ui/settings.py:162
#, fuzzy
msgid "Interface Size"
msgstr "Размер интерфейса"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "Настроить размер интерфейса"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "Цветовая схема редактора"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "Изменить цветовую схему редактора и блоков кода"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "Скрытые файлы"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "Показать скрытые файлы"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "Отправить с ENTER"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"Если включено, сообщения будут отправляться с ENTER, для перехода на новую "
"строку используйте CTRL+ENTER. Если отключено, сообщения будут отправляться "
"с SHIFT+ENTER, а новая строка — с ENTER"

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "Удалить мысли из истории"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr ""
"Не отправлять старые блоки мышления для моделей рассуждения, чтобы уменьшить "
"использование токенов"

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "Отображать LaTeX"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "Отображать формулы LaTeX в чате"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "Обратный порядок чата"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr ""
"Показывать самые свежие чаты сверху в списке чатов (измените чат для "
"применения)"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "Автоматически генерировать имена чатов"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr "Автоматически генерировать имена чатов после первых двух сообщений"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "Количество предложений"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "Количество предложений сообщений для отправки в чат "

#: src/ui/settings.py:224
msgid "Username"
msgstr "Имя пользователя"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr ""
"Измените метку, которая появляется перед вашим сообщением\n"
"Эта информация по умолчанию не отправляется в LLM\n"
"Вы можете добавить ее в подсказку, используя переменную {USER}"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "Управление нейронной сетью"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "Запускать команды в виртуальной машине"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "Внешний терминал"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr "Выберите внешний терминал для выполнения команд консоли"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "Память программы"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "Как долго программа запоминает чат "

#: src/ui/settings.py:266
msgid "Developer"
msgstr "Разработчик"

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr ""
"Мониторинг вывода программы в реальном времени, полезно для отладки и "
"просмотра хода загрузки"

#: src/ui/settings.py:270
msgid "Open"
msgstr "Открыть"

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr "Удалить путь pip"

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr "Удалить установленные дополнительные зависимости"

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr ""

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr ""

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "Автоматический запуск команд"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "Команды, которые будет написывать бот, будут выполняться автоматически"

#: src/ui/settings.py:313
#, fuzzy
msgid "Max number of commands"
msgstr "Максимальное количество команд"

#: src/ui/settings.py:313
#, fuzzy
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr ""
"Максимальное количество команд, которое бот напишет после одного запроса "
"пользователя"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "Браузер"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "Настройки браузера"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "Использовать внешний браузер"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr "Использовать внешний браузер для открытия ссылок вместо встроенного"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "Сохранять сессию браузера"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr ""
"Сохранять сессию браузера между перезапусками. Отключение этой опции требует "
"перезапуска программы"

#: src/ui/settings.py:361
msgid "Delete browser data"
msgstr "Удалить данные браузера"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "Удалить сессию и данные браузера"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "Начальная страница браузера"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "Страница, с которой будет запускаться браузер"

#: src/ui/settings.py:375
#, fuzzy
msgid "Search string"
msgstr "Строка поиска"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr "Строка поиска, используемая в браузере, %s заменяется запросом"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "Источники документов (RAG)"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "Включать содержимое ваших документов в ответы"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "Анализатор документов"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr ""
"Анализатор документов использует несколько методов для извлечения "
"релевантной информации из ваших документов"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "Читать документы, если не поддерживается"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr ""
"Если LLM не поддерживает чтение документов, соответствующая информация о "
"документах, отправленных в чат, будет передана LLM с помощью вашего "
"анализатора документов."

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "Максимальное количество токенов для RAG"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"Максимальное количество токенов, которое будет использоваться для RAG. Если "
"документы не превышают это количество токенов,\n"
"загрузите их все в контекст"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "Папка документов"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr ""
"Поместите документы, которые вы хотите запросить, в папку документов. "
"Анализатор документов найдет в них релевантную информацию, если эта опция "
"включена."

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr ""
"Поместите все документы, которые вы хотите проиндексировать, в эту папку"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "Порог тишины"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr ""
"Порог тишины в секундах, процент громкости, который будет считаться тишиной"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "Время тишины"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr "Время тишины в секундах до автоматической остановки записи"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "Недостаточно разрешений"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr ""
"У Newelle недостаточно разрешений для выполнения команд в вашей системе, "
"пожалуйста, выполните следующую команду"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "Понятно"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr "Путь Pip удален"

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr ""
"Путь pip был удален, теперь вы можете переустановить зависимости. Эта "
"операция требует перезапуска приложения."

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "Демо API Newelle"

#: src/constants.py:26
msgid "GPT4Free"
msgstr "GPT4Free"

#: src/constants.py:34
msgid "Local Model"
msgstr "Локальная модель"

#: src/constants.py:35
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr ""
"БЕЗ ПОДДЕРЖКИ ГРАФИЧЕСКОГО ПРОЦЕССОРА, ВМЕСТО ЭТОГО ИСПОЛЬЗУЙТЕ OLLAMA. "
"Запустите модель LLM локально, больше конфиденциальности, но медленнее"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "Экземпляр Ollama"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "Легко запускайте несколько моделей LLM на своем оборудовании"

#: src/constants.py:47
msgid "Groq"
msgstr "Groq"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "API Google Gemini"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "API OpenAI"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr ""
"OpenAI API. Поддерживаются пользовательские конечные точки. Используйте для "
"пользовательских поставщиков"

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "Anthropic Claude"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr ""
"Официальные API для моделей Anthropic Claude, с поддержкой изображений и "
"файлов, требуется ключ API"

#: src/constants.py:73
msgid "Mistral"
msgstr "Mistral"

#: src/constants.py:74
msgid "Mistral API"
msgstr "API Mistral"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "API Openrouter.ai, поддерживает множество моделей"

#: src/constants.py:87
msgid "Deepseek"
msgstr "Deepseek"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "API Deepseek, самые мощные модели с открытым исходным кодом"

#: src/constants.py:94 src/constants.py:203
#, fuzzy
msgid "Custom Command"
msgstr "Пользовательская команда"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "Использовать вывод пользовательской команды"

#: src/constants.py:104
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr "Работает офлайн. Оптимизированная реализация Whisper на C++"

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "Работает офлайн. Поддерживается только английский язык"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "Распознавание речи Google"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "Онлайн-распознавание речи Google"

#: src/constants.py:124
msgid "Groq Speech Recognition"
msgstr "Распознавание речи Groq"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit AI"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr ""
"Бесплатный API для распознавания речи wit.ai (язык выбирается на сайте)"

#: src/constants.py:137
msgid "Vosk API"
msgstr "Vosk API"

#: src/constants.py:138
msgid "Works Offline"
msgstr "Работает офлайн"

#: src/constants.py:144
msgid "Whisper API"
msgstr "Whisper API"

#: src/constants.py:145
msgid "Uses OpenAI Whisper API"
msgstr "Использует OpenAI Whisper API"

#: src/constants.py:151
#, fuzzy
msgid "Custom command"
msgstr "Пользовательская команда"

#: src/constants.py:152
#, fuzzy
msgid "Runs a custom command"
msgstr "Запускает пользовательскую команду"

#: src/constants.py:161
msgid "Google TTS"
msgstr "Google TTS"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "Преобразование текста в речь от Google"

#: src/constants.py:167
msgid "Kokoro TTS"
msgstr "Kokoro TTS"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr ""
"Легкий и быстрый движок TTS с открытым исходным кодом. ~3 ГБ зависимостей, "
"400 МБ модель"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "ElevenLabs TTS"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "Естественное звучание TTS"

#: src/constants.py:179 src/constants.py:180
msgid "OpenAI TTS"
msgstr "OpenAI TTS"

#: src/constants.py:185
#, fuzzy
msgid "Groq TTS"
msgstr "Groq TTS"

#: src/constants.py:186
msgid "Groq TTS API"
msgstr "API Groq TTS"

#: src/constants.py:191 src/constants.py:192
msgid "Custom OpenAI TTS"
msgstr "Пользовательский OpenAI TTS"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "Espeak TTS"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "Офлайн TTS"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr ""
"Использовать пользовательскую команду как TTS, {0} будет заменен текстом"

#: src/constants.py:212
msgid "WordLlama"
msgstr "WordLlama"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr ""
"Легкая локальная модель эмбеддинга на основе llama. Работает офлайн, очень "
"низкое потребление ресурсов"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "Внедрение Ollama"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr ""
"Используйте модели Ollama для эмбеддинга. Работает офлайн, очень низкое "
"потребление ресурсов"

#: src/constants.py:231
msgid "Use Google Gemini API to get embeddings"
msgstr "Используйте API Google Gemini для получения эмбеддингов"

#: src/constants.py:239
msgid "User Summary"
msgstr "Сводка пользователя"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "Создать сводку разговора пользователя"

#: src/constants.py:245
msgid "Memoripy"
msgstr "Memoripy"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"Извлечение сообщений из предыдущих разговоров с использованием контекстного "
"извлечения памяти, затухания памяти, извлечения концепций и других передовых "
"методов. Выполняет 1 вызов LLM на сообщение."

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "Сводка пользователя + Memoripy"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "Использовать обе технологии для долговременной памяти"

#: src/constants.py:260
msgid "Document reader"
msgstr "Чтение документов"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr ""
"Классический подход RAG - разбиение документов на фрагменты и их внедрение, "
"затем сравнение их с запросом и возврат наиболее релевантных документов"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr "SearXNG - Частная и самохостинговая поисковая система"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "Поиск DuckDuckGo"

#: src/constants.py:281
msgid "Tavily"
msgstr "Tavily"

#: src/constants.py:282
msgid "Tavily search"
msgstr "Поиск Tavily"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "Полезный помощник"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr ""
"Подсказка общего назначения для улучшения ответов LLM и предоставления "
"большего контекста"

#: src/constants.py:384
msgid "Console access"
msgstr "Доступ к консоли"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "Может ли программа выполнять команды терминала на компьютере"

#: src/constants.py:392
msgid "Current directory"
msgstr "Текущая директория"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "Какая текущая директория"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "Разрешить LLM искать в интернете"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "Базовая функциональность"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "Показывать таблицы и код (*может работать без этого)"

#: src/constants.py:419
msgid "Graphs access"
msgstr "Доступ к графикам"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "Может ли программа отображать графики"

#: src/constants.py:428
msgid "Show image"
msgstr "Показать изображение"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "Показывать изображение в чате"

#: src/constants.py:437
#, fuzzy
msgid "Custom Prompt"
msgstr "Пользовательская подсказка"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "Добавьте свою собственную подсказку"

#: src/constants.py:480
#, fuzzy
msgid "LLM and Secondary LLM settings"
msgstr "Настройки LLM и вторичного LLM"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "TTS"

#: src/constants.py:485
msgid "Text to Speech settings"
msgstr "Настройки преобразования текста в речь"

#: src/constants.py:488
msgid "STT"
msgstr "STT"

#: src/constants.py:490
msgid "Speech to Text settings"
msgstr "Настройки преобразования речи в текст"

#: src/constants.py:493
msgid "Embedding"
msgstr "Внедрение"

#: src/constants.py:495
#, fuzzy
msgid "Embedding settings"
msgstr "Настройки внедрения"

#: src/constants.py:498
msgid "Memory"
msgstr "Память"

#: src/constants.py:500
#, fuzzy
msgid "Memory settings"
msgstr "Настройки памяти"

#: src/constants.py:503
msgid "Websearch"
msgstr "Веб-поиск"

#: src/constants.py:505
#, fuzzy
msgid "Websearch settings"
msgstr "Настройки веб-поиска"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
msgid "Document analyzer settings"
msgstr "Настройки анализатора документов"

#: src/constants.py:515
#, fuzzy
msgid "Extensions settings"
msgstr "Настройки расширений"

#: src/constants.py:518
#, fuzzy
msgid "Inteface"
msgstr "Интерфейс"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr "Настройки интерфейса, скрытые файлы, обратный порядок, масштаб..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr ""
"Общие настройки, виртуализация, предложения, длина памяти, автоматическая "
"генерация имени чата, текущая папка..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr ""
"Настройки подсказок, пользовательская дополнительная подсказка, "
"пользовательские подсказки..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "Чат "

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "Потоки терминала все еще работают в фоновом режиме"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "При закрытии окна они будут автоматически завершены"

#: src/main.py:210
msgid "Close"
msgstr "Закрыть"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "Чат перезагружен"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "Папка перезагружена"

#: src/main.py:254
msgid "Chat is created"
msgstr "Чат создан"

#: src/window.py:120
#, fuzzy
msgid "Keyboard shorcuts"
msgstr "Быстрые клавиши"

#: src/window.py:121
msgid "About"
msgstr "О программе"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "Чат"

#: src/window.py:170
msgid "History"
msgstr "История"

#: src/window.py:191
msgid "Create a chat"
msgstr "Создать чат"

#: src/window.py:196
#, fuzzy
msgid "Chats"
msgstr "Чаты"

#: src/window.py:267
msgid " Stop"
msgstr " Остановить"

#: src/window.py:282
msgid " Clear"
msgstr " Очистить"

#: src/window.py:297
msgid " Continue"
msgstr " Продолжить"

#: src/window.py:310
msgid " Regenerate"
msgstr " Перегенерировать"

#: src/window.py:376
msgid "Send a message..."
msgstr "Отправить сообщение..."

#: src/window.py:467
msgid "Explorer Tab"
msgstr "Вкладка Проводник"

#: src/window.py:468
msgid "Terminal Tab"
msgstr "Вкладка Терминал"

#: src/window.py:469
msgid "Browser Tab"
msgstr "Вкладка Браузер"

#: src/window.py:589
msgid "Ask about a website"
msgstr "Спросить о веб-сайте"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr ""
"Напишите #https://website.com в чате, чтобы запросить информацию о веб-сайте"

#: src/window.py:590
msgid "Check out our Extensions!"
msgstr "Ознакомьтесь с нашими расширениями!"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "У нас много расширений для разных целей. Проверьте!"

#: src/window.py:591
msgid "Chat with documents!"
msgstr "Общайтесь с документами!"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr ""
"Добавьте свои документы в папку документов и общайтесь, используя "
"информацию, содержащуюся в них!"

#: src/window.py:592
msgid "Surf the web!"
msgstr "Путешествуйте по интернету!"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr ""
"Включите веб-поиск, чтобы LLM мог просматривать веб-страницы и предоставлять "
"актуальные ответы"

#: src/window.py:593
msgid "Mini Window"
msgstr "Мини-окно"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "Задавайте вопросы на лету, используя режим мини-окна"

#: src/window.py:594
msgid "Text to Speech"
msgstr "Преобразование текста в речь"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr ""
"Newelle поддерживает преобразование текста в речь! Включите это в настройках"

#: src/window.py:595
#, fuzzy
msgid "Keyboard Shortcuts"
msgstr "Быстрые клавиши"

#: src/window.py:595
#, fuzzy
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "Управление Newelle с помощью горячих клавиш"

#: src/window.py:596
#, fuzzy
msgid "Prompt Control"
msgstr "Управление подсказками"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr ""
"Newelle предоставляет вам 100% контроль над подсказками. Настраивайте свои "
"подсказки для своего использования."

#: src/window.py:597
#, fuzzy
msgid "Thread Editing"
msgstr "Редактирование потока"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "Проверьте программы и процессы, которые вы запускаете из Newelle"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr "Программируемые подсказки"

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr ""
"Вы можете добавлять динамические подсказки в Newelle, с условиями и "
"вероятностями"

#: src/window.py:605
#, fuzzy
msgid "New Chat"
msgstr "Новый чат"

#: src/window.py:623
msgid "Provider Errror"
msgstr "Ошибка провайдера"

#: src/window.py:646
msgid "Local Documents"
msgstr "Локальные документы"

#: src/window.py:650
msgid "Web search"
msgstr "Веб-поиск"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "У этого поставщика нет списка моделей"

#: src/window.py:901
msgid " Models"
msgstr " Модели"

#: src/window.py:904
msgid "Search Models..."
msgstr "Поиск моделей..."

#: src/window.py:1132
msgid "Create new profile"
msgstr "Создать новый профиль"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "Не удалось распознать ваш голос"

#: src/window.py:1303
msgid "Images"
msgstr "Изображения"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "Файлы, поддерживаемые LLM"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "Файлы, поддерживаемые RAG"

#: src/window.py:1333
msgid "Supported Files"
msgstr "Поддерживаемые файлы"

#: src/window.py:1337
msgid "All Files"
msgstr "Все файлы"

#: src/window.py:1343
msgid "Attach file"
msgstr "Прикрепить файл"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "Файл не может быть отправлен, пока программа не завершится"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "Файл не распознан"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "Вы больше не можете продолжить сообщение."

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "Вы больше не можете перегенерировать сообщение."

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "Чат очищен"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "Сообщение было отменено и удалено из истории"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr "Сообщение не может быть отправлено, пока программа не завершится"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr "Нельзя редактировать сообщение во время работы программы."

#: src/window.py:3080
#, fuzzy
msgid "Prompt content"
msgstr "Содержимое подсказки"

#: src/window.py:3339
#, fuzzy
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"Внимание! Нейронная сеть имеет доступ к вашему компьютеру и любым данным в "
"этом чате, а также может выполнять команды. Будьте осторожны, мы не несем "
"ответственности за нейронную сеть. Не делитесь конфиденциальной информацией."

#: src/window.py:3368
#, fuzzy
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"Внимание! Нейронная сеть имеет доступ к любым данным в этом чате. Будьте "
"осторожны, мы не несем ответственности за нейронную сеть. Не делитесь "
"конфиденциальной информацией."

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "Неверный путь к папке"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "Поток не завершен, номер потока: "

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "Не удалось открыть папку"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "Чат пуст"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr ""
"Обучайте Newelle делать больше с помощью пользовательских расширений и новых "
"модулей ИИ, предоставляя вашему чат-боту безграничные возможности."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "Чат-бот с ИИ"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "Быстрый выбор профиля"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
#, fuzzy
msgid "Message Editing"
msgstr "Редактирование сообщений"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "Более 10 стандартных поставщиков ИИ"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "Исправления ошибок"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
#, fuzzy
msgid "Minor bug fixes"
msgstr "Исправления ошибок"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "Небольшие улучшения"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "Улучшить производительность чтения и загрузки локальных документов"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "Добавить опцию отправки с помощью CTRL+Enter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "Улучшить блоки кода"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
msgid "Fix Kokoro TTS"
msgstr "Исправить Kokoro TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "Удалить эмодзи из TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "Установить ключи API как поля паролей"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "Добавить поддержку размышлений для Gemini"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "Обновленные переводы"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "Добавлены новые функции"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr "Чтение веб-сайтов и веб-поиск с SearXNG, DuckDuckGo и Tavily"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "Улучшена отрисовка LaTeX и управление документами"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "Новый виджет мышления и обработчик OpenRouter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "Поддержка Vision для Llama4 на Groq"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "Новые переводы (традиционный китайский, бенгальский, хинди)"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "Исправлено много ошибок, добавлены некоторые функции!"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "Поддержка новых функций и исправление ошибок"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "Добавлено много новых функций и исправлений ошибок"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "Добавлены новые функции и исправлены ошибки"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr ""
"Обновлена библиотека g4f с версионированием, добавлены руководства "
"пользователя, улучшен просмотр расширений и расширена обработка моделей."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"Внедрены исправления ошибок и новые функции. Мы изменили архитектуру "
"расширений, добавили новые модели и внедрили поддержку зрения, а также "
"другие возможности."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "Стабильная версия"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
#, fuzzy
msgid "Added extension"
msgstr "Добавлено расширение"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
#, fuzzy
msgid "Blacklist of commands"
msgstr "Чёрный список команд"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "Локализация"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "Редизайн"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "Кверсик"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "Newelle: Ваш продвинутый чат-бот"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "ai;assistant;chat;chatgpt;gpt;llm;ollama;"

#~ msgid "max Tokens"
#~ msgstr "Макс. токены"

#~ msgid "Max tokens of the generated text"
#~ msgstr "Максимальное количество токенов сгенерированного текста"

#, fuzzy
#~ msgid "_Cancel"
#~ msgstr "Отмена"

#~ msgid "Choose an extension"
#~ msgstr "Выберите расширение"

#~ msgid " has been removed"
#~ msgstr " удалено"

#~ msgid "Extension added. New extensions will run from the next launch"
#~ msgstr ""
#~ "Расширение добавлено. Новые расширения будут запущены с следующего запуска"

#~ msgid "The extension is wrong"
#~ msgstr "Неверное расширение"

#~ msgid "This is not an extension"
#~ msgstr "Это не расширение"

#~ msgid "Chat has been stopped"
#~ msgstr "Чат был остановлен"

#~ msgid "The change will take effect after you restart the program."
#~ msgstr "Изменения вступят в силу после перезапуска программы."
