name: Rebuild Locales

on:
  workflow_dispatch:

jobs:
  run-script:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.head_ref }}
          fetch-depth: 0

      - name: Rebuild locales
        run: |
          sudo apt update
          sudo apt install gettext
          (
            find src -type f -name "*.py" -exec grep -lE '_\(|N\(_' {} \;
            find data -type f \( -name "*.xml" -o -name "*.in" \)
          ) >po/POTFILES
          xgettext -o po/newelle.pot $(cat po/POTFILES)
          cd po
          for file in $(ls *.po); do
              msgmerge -U "$file" newelle.pot
          done
          rm -f *~
          cd ..

      - name: Commit changes
        run: |
          if [ -n "$(git status --porcelain)" ]; then
            git config --local user.name "github-actions[bot]"
            git config --local user.email "github-actions[bot]@users.noreply.github.com"
            git add .
            git commit -m "Automated changes by github actions"
            git push origin ${GITHUB_REF##*/}
          fi
