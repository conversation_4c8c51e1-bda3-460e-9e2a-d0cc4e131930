msgid ""
msgstr ""
"Project-Id-Version: 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-07-03 09:00+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Czech <<EMAIL>>\n"
"Language: cs\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "Koncový bod API"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr "Základní URL API, změňte pro použití API pro inference"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid "Automatically Serve"
msgstr "Automatické spuštění"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"V případě potřeby automaticky spustí ollama serve na pozadí, pokud již "
"nebude spuštěn. Můžete jej ukončit pomocí killall ollama"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
msgid "Custom Model"
msgstr "Vlastní model"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
msgid "Use a custom model"
msgstr "Použít vlastní model"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
msgid "Ollama Model"
msgstr "Ollama model"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
msgid "Name of the Ollama Model"
msgstr "Název Ollama modelu"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "API klíč"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
msgid "API Key for "
msgstr "API klíč pro "

#: src/handlers/embeddings/openai_handler.py:38
msgid "API base url, change this to use different APIs"
msgstr "Základní URL API, změňte pro použití jiných API"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
msgid "Use Custom Model"
msgstr "Použít vlastní model"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "Model"

#: src/handlers/embeddings/openai_handler.py:44
msgid "Name of the Embedding Model to use"
msgstr "Název modelu pro vkládání (Embedding)"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
msgid " Model"
msgstr " Model"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
msgid "The API key to use"
msgstr "API klíč k použití"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
msgid "The model to use"
msgstr "Model k použití"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "Maximální počet tokenů"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "Maximální počet tokenů k vygenerování"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
msgid "Message Streaming"
msgstr "Streamování zpráv"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "Postupné streamování výstupu zpráv"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "Příkaz pro získání výstupu bota"

#: src/handlers/llm/custom_handler.py:21
#, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr ""
"Příkaz ke spuštění pro získání odpovědi bota, {0} bude nahrazeno JSON "
"souborem obsahujícím chat, {1} systémovou výzvou"

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "Příkaz pro získání návrhů bota"

#: src/handlers/llm/custom_handler.py:22
#, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"Příkaz ke spuštění pro získání návrhů chatu, {0} bude nahrazeno JSON "
"souborem obsahujícím chat, {1} dalšími výzvami, {2} počtem návrhů k "
"vygenerování. Musí vrátit JSON pole obsahující návrhy jako řetězce"

#: src/handlers/llm/gpt4all_handler.py:67
msgid "RAM Required: "
msgstr "Požadovaná RAM: "

#: src/handlers/llm/gpt4all_handler.py:68
msgid "Parameters: "
msgstr "Parametry: "

#: src/handlers/llm/gpt4all_handler.py:69
msgid "Size: "
msgstr "Velikost: "

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
msgid "Model to use"
msgstr "Model k použití"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
msgid "Name of the model to use"
msgstr "Název modelu k použití"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "Model Manager"
msgstr "Správce modelů"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "Seznam dostupných modelů"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr "Aktualizovat G4F"

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "Zásady ochrany osobních údajů"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "Otevřít webové stránky se zásadami ochrany osobních údajů"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "Povolit myšlení"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr "Povolit myšlení v modelu, podporovány jsou pouze některé modely"

#: src/handlers/llm/ollama_handler.py:176
msgid "Add custom model"
msgstr "Přidat vlastní model"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"Přidejte jakýkoli model do tohoto seznamu zadáním název:velikost\n"
"Nebo jakýkoli gguf z hf s hf.co/uživatelské_jméno/model"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr "Aktualizovat Ollamu"

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "API klíč (povinný)"

#: src/handlers/llm/gemini_handler.py:94
msgid "API key got from ai.google.dev"
msgstr "API klíč získaný z ai.google.dev"

#: src/handlers/llm/gemini_handler.py:98
msgid "AI Model to use"
msgstr "Model AI k použití"

#: src/handlers/llm/gemini_handler.py:103
msgid "Enable System Prompt"
msgstr "Povolit systémovou výzvu"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr ""
"Některé modely nepodporují systémovou výzvu (nebo pokyny pro vývojáře), "
"vypněte ji, pokud se zobrazí chyby."

#: src/handlers/llm/gemini_handler.py:107
msgid "Inject system prompt"
msgstr "Vložit systémovou výzvu"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr ""
"I když model nepodporuje systémové výzvy, umístěte výzvy nad zprávu uživatele"

#: src/handlers/llm/gemini_handler.py:109
msgid "Thinking Settings"
msgstr "Nastavení myšlení"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "Nastavení týkající se modelů myšlení"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr "Zobrazit myšlení, vypněte, pokud váš model tuto funkci nepodporuje"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "Povolit rozpočet na myšlení"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "Zda povolit rozpočet na myšlení"

#: src/handlers/llm/gemini_handler.py:112
msgid "Thinking Budget"
msgstr "Rozpočet na myšlení"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "Kolik času věnovat přemýšlení"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "Výstup obrázků"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr "Povolit výstup obrázků, podporováno pouze modelem gemini-2.0-flash-exp"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "Povolit nastavení bezpečnosti"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr ""
"Povolit bezpečnostní nastavení Google, aby se zabránilo generování "
"škodlivého obsahu"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
msgid "Advanced Parameters"
msgstr "Pokročilé parametry"

#: src/handlers/llm/gemini_handler.py:135
msgid "Enable advanced parameters"
msgstr "Povolit pokročilé parametry"

#: src/handlers/llm/openai_handler.py:81
#, fuzzy
msgid "Include parameters like Top-P, Temperature, etc."
msgstr "Zahrnout parametry jako Max Tokens, Top-P, Teplota atd."

#: src/handlers/llm/openai_handler.py:84
msgid "Name of the LLM Model to use"
msgstr "Název LLM modelu k použití"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "Top-P"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr "Alternativa k vzorkování s teplotou, nazývaná jaderné vzorkování"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "Teplota"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr "Jakou teplotu vzorkování použít. Vyšší hodnoty učiní výstup náhodnější"

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "Penalizace frekvence"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr ""
"Číslo mezi -2,0 a 2,0. Kladné hodnoty snižují pravděpodobnost, že model bude "
"doslova opakovat stejný řádek"

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "Penalizace přítomnosti"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr ""
"Číslo mezi -2,0 a 2,0. Kladné hodnoty snižují pravděpodobnost, že model bude "
"mluvit o nových tématech"

#: src/handlers/llm/openai_handler.py:108
#, fuzzy
msgid "Custom Options"
msgstr "Vlastní výzva"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr ""

#: src/handlers/llm/openrouter_handler.py:14
msgid "Provider Sorting"
msgstr "Řazení poskytovatelů"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "Vyberte poskytovatele na základě ceny/propustnosti nebo latence"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "Cena"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "Propustnost"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "Latence"

#: src/handlers/llm/openrouter_handler.py:15
msgid "Providers Order"
msgstr "Pořadí poskytovatelů"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr ""
"Přidejte pořadí poskytovatelů k použití, jména oddělená čárkou.\n"
"Prázdné, pokud nechcete specifikovat"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "Povolit zálohy"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "Povolit záložní řešení pro jiné poskytovatele"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "Indexovat vaše dokumenty"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"Indexujte všechny dokumenty ve vaší složce dokumentů. Tuto operaci musíte "
"spustit pokaždé, když upravíte/vytvoříte dokument, změníte analyzátor "
"dokumentů nebo změníte model vkládání"

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "Příkaz ke spuštění"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "{0} bude nahrazeno plnou cestou k modelu"

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr "API klíč pro Google SR, napište 'default' pro použití výchozího"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "Jazyk"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "Jazyk textu k rozpoznání ve formátu IETF"

#: src/handlers/stt/groqsr_handler.py:14
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr "API klíč pro Groq SR, napište 'default' pro použití výchozího"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "Groq model"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "Název modelu Groq"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Zadejte jazyk pro transkripci. Použijte kódy jazyků ISO 639-1 (např. „en“ "
"pro angličtinu, „fr“ pro francouzštinu atd.). "

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "Koncový bod pro požadavky OpenAI"

#: src/handlers/stt/openaisr_handler.py:18
msgid "API Key for OpenAI"
msgstr "API klíč pro OpenAI"

#: src/handlers/stt/openaisr_handler.py:25
msgid "Whisper Model"
msgstr "Whisper Model"

#: src/handlers/stt/openaisr_handler.py:26
msgid "Name of the OpenAI model"
msgstr "Název modelu OpenAI"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Volitelné: Zadejte jazyk pro transkripci. Použijte kódy jazyků ISO 639-1 "
"(např. „en“ pro angličtinu, „fr“ pro francouzštinu atd.). "

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "Cesta k modelu"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "Absolutní cesta k modelu VOSK (rozbalený)"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
msgid "Name of the Whisper model"
msgstr "Název modelu Whisper"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "Přístupový token serveru pro wit.ai"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "Nepodařilo se porozumět zvuku"

#: src/handlers/stt/whispercpp_handler.py:47
#, fuzzy
msgid "Language of the recognition. For example en, it..."
msgstr "Jazyk rozpoznání."

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Model Library"
msgstr "Knihovna modelů"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Manage Whisper models"
msgstr "Spravovat modely Whisper"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "Advanced Settings"
msgstr "Pokročilé nastavení"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "More advanced settings"
msgstr "Další pokročilá nastavení"

#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature to use"
msgstr "Teplota k použití"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt for the recognition"
msgstr "Výzva pro rozpoznání"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr "Výzva k použití pro rozpoznání"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Endpoint"
msgstr "Koncový bod"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "Vlastní koncový bod služby k použití"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "Hlas"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
msgid "The voice to use"
msgstr "Hlas k použití"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "Pokyny"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr ""
"Pokyny pro generování hlasu. Ponechte prázdné, aby se toto pole nepoužilo"

#: src/handlers/tts/custom_handler.py:17
#, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr "{0} bude nahrazeno plnou cestou k souboru, {1} textem"

#: src/handlers/tts/elevenlabs_handler.py:11
msgid "API Key for ElevenLabs"
msgstr "API klíč pro ElevenLabs"

#: src/handlers/tts/elevenlabs_handler.py:19
msgid "Voice ID to use"
msgstr "ID hlasu k použití"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "Stabilita"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "stabilita hlasu"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "Zvýšení podobnosti"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "Zvyšuje celkovou čistotu hlasu a podobnost řečníka"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "Přehánění stylu"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr "Vysoké hodnoty se doporučují, pokud styl řeči musí být přehnaný"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "Vyberte preferovaný hlas"

#: src/handlers/websearch/tavily.py:20
msgid "Token"
msgstr "Token"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "API klíč Tavily"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "Max. výsledků"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "Počet výsledků k zohlednění"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "Hloubka hledání"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"Hloubka hledání. Pokročilé vyhledávání je přizpůsobeno pro načítání "
"nejrelevantnějších zdrojů a úryvků obsahu pro váš dotaz, zatímco základní "
"vyhledávání poskytuje obecné úryvky obsahu z každého zdroje. Základní "
"vyhledávání stojí 1 API kredit, zatímco pokročilé vyhledávání stojí 2 API "
"kredity."

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "Kategorie hledání"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"Kategorie hledání. Zprávy jsou užitečné pro získávání aktuálních informací, "
"zejména o politice, sportu a hlavních aktuálních událostech pokrývaných "
"hlavními mediálními zdroji. Obecné je pro širší, obecnější hledání, které "
"může zahrnovat širokou škálu zdrojů."

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "Bloky na zdroj"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr ""
"Počet bloků obsahu k načtení z každého zdroje. Délka každého bloku je "
"maximálně 500 znaků. K dispozici pouze při pokročilé hloubce hledání."

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "Počet dnů zpět od aktuálního data k zahrnutí"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "Dostupné pouze, pokud je tématem zprávy."

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "Zahrnout odpověď"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"Zahrnout odpověď generovanou LLM k poskytnutému dotazu. Základní hledání "
"vrátí rychlou odpověď. Pokročilé vrátí podrobnější odpověď."

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "Zahrnout syrový obsah"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr "Zahrnout vyčištěný a analyzovaný HTML obsah každého výsledku hledání."

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "Zahrnout obrázky"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr "Proveďte hledání obrázků a zahrňte výsledky do odpovědi."

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "Zahrnout popisy obrázků"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr ""
"Pokud je povoleno Zahrnout obrázky, přidejte také popisný text pro každý "
"obrázek."

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "Zahrnout domény"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr "Seznam domén, které se mají konkrétně zahrnout do výsledků hledání."

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "Vyloučit domény"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr "Seznam domén, které se mají konkrétně vyloučit z výsledků hledání."

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "Region"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "Region pro výsledky hledání"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "Nastavení"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "Název profilu"

#: src/ui/profile.py:58
msgid "Copied Settings"
msgstr "Kopírované nastavení"

#: src/ui/profile.py:58
msgid "Settings that will be copied to the new profile"
msgstr "Nastavení, která budou zkopírována do nového profilu"

#: src/ui/profile.py:70
msgid "Create Profile"
msgstr "Vytvořit profil"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "Importovat profil"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
msgid "Edit Profile"
msgstr "Upravit profil"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "Exportovat profil"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "Exportovat hesla"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "Exportovat také pole podobná heslům"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "Exportovat obrázek profilu"

#: src/ui/profile.py:89
msgid "Also export the profile picture"
msgstr "Exportovat také profilový obrázek"

#: src/ui/profile.py:109 src/ui/explorer.py:692
msgid "Create"
msgstr "Vytvořit"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "Použít"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr "Nastavení aktuálního profilu budou zkopírována do nového"

#: src/ui/profile.py:122 src/ui/profile.py:128
msgid "Newelle Profiles"
msgstr "Profily Newelle"

#: src/ui/profile.py:123
msgid "Export"
msgstr "Exportovat"

#: src/ui/profile.py:129
msgid "Import"
msgstr "Importovat"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "Nastavit profilový obrázek"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "Úprava vlákna"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "Žádná vlákna nejsou spuštěna"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "Číslo vlákna: "

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "Vybrat profil"

#: src/ui/widgets/profilerow.py:53
msgid "Delete Profile"
msgstr "Smazat profil"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "Myšlenky"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "Rozbalte pro zobrazení podrobností"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "Přemýšlím..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr "LLM přemýšlí... Rozbalte pro zobrazení průběhu myšlení"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "Žádný zaznamenaný myšlenkový proces"

#: src/ui/widgets/tipscarousel.py:41
msgid "Newelle Tips"
msgstr "Tipy Newelle"

#: src/ui/explorer.py:192
msgid "Folder is Empty"
msgstr "Složka je prázdná"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr "Soubor nenalezen"

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "Otevřít v nové kartě"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "Otevřít v integrovaném editoru"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "Otevřít ve správci souborů"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
msgid "Rename"
msgstr "Přejmenovat"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "Smazat"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "Zkopírovat plnou cestu"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
msgid "Failed to open file manager"
msgstr "Nepodařilo se otevřít správce souborů"

#: src/ui/explorer.py:436
msgid "New name:"
msgstr "Nový název:"

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "Zrušit"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "Úspěšně přejmenováno"

#: src/ui/explorer.py:476
#, python-brace-format
msgid "Failed to rename: {}"
msgstr "Nepodařilo se přejmenovat: {}"

#: src/ui/explorer.py:497
msgid "Delete File?"
msgstr "Smazat soubor?"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "Opravdu chcete smazat „{}“?"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "Úspěšně smazáno"

#: src/ui/explorer.py:525
#, python-brace-format
msgid "Failed to delete: {}"
msgstr "Nepodařilo se smazat: {}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "Cesta zkopírována do schránky"

#: src/ui/explorer.py:542
msgid "Failed to copy path"
msgstr "Nepodařilo se zkopírovat cestu"

#: src/ui/explorer.py:580
msgid "Create new folder"
msgstr "Vytvořit novou složku"

#: src/ui/explorer.py:583
msgid "Create new file"
msgstr "Vytvořit nový soubor"

#: src/ui/explorer.py:586
msgid "Open Terminal Here"
msgstr "Otevřít zde terminál"

#: src/ui/explorer.py:640
msgid "Create New Folder"
msgstr "Vytvořit novou složku"

#: src/ui/explorer.py:640
msgid "Folder name:"
msgstr "Název složky:"

#: src/ui/explorer.py:644
msgid "Create New File"
msgstr "Vytvořit nový soubor"

#: src/ui/explorer.py:644
msgid "File name:"
msgstr "Název souboru:"

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "Složka úspěšně vytvořena"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "Soubor úspěšně vytvořen"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "Soubor nebo složka s tímto názvem již existuje"

#: src/ui/explorer.py:728
msgid "folder"
msgstr "složka"

#: src/ui/explorer.py:728
msgid "file"
msgstr "soubor"

#: src/ui/explorer.py:730
#, python-brace-format
msgid "Failed to create {}: {}"
msgstr "Nepodařilo se vytvořit {}: {}"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "Nápověda"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "Zkratky"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "Načíst chat"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "Načíst složku"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "Nová záložka"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "Vložit obrázek"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "Zaměřit se na pole zprávy"

#: src/ui/shortcuts.py:18
msgid "Start/stop recording"
msgstr "Spustit/zastavit nahrávání"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "Uložit"

#: src/ui/shortcuts.py:20
msgid "Stop TTS"
msgstr "Zastavit TTS"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr "Přiblížit"

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr "Oddálit"

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr "Monitor výstupu programu"

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr "Vyčistit výstup"

#: src/ui/stdout_monitor.py:61
msgid "Start/Stop monitoring"
msgstr "Spustit/Zastavit monitorování"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr "Monitorování: Aktivní"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr "Monitorování: Zastaveno"

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr "Řádky: {}"

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr "Řádky: 0"

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "Rozšíření"

#: src/ui/extension.py:50
msgid "Installed Extensions"
msgstr "Nainstalovaná rozšíření"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "Uživatelská příručka k rozšířením"

#: src/ui/extension.py:89
msgid "Download new Extensions"
msgstr "Stáhnout nová rozšíření"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "Nainstalovat rozšíření ze souboru..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "Newelle"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "Chat je otevřen v mini okně"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "Vítejte v Newelle"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "Váš dokonalý virtuální asistent."

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "Stránka na Githubu"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "Vyberte si svůj oblíbený jazykový model AI"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"Newelle lze použít s více modely a poskytovateli!\n"
"<b>Poznámka: Důrazně doporučujeme přečíst si stránku Průvodce LLM</b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "Průvodce LLM"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "Chatujte s vašimi dokumenty"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr ""
"Newelle dokáže získávat relevantní informace z dokumentů, které odešlete v "
"chatu, nebo z vašich vlastních souborů! Informace relevantní k vašemu dotazu "
"budou odeslány do LLM."

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "Virtualizace příkazů"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"Newelle lze použít ke spouštění příkazů ve vašem systému, ale dávejte pozor "
"na to, co spouštíte! <b>LLM není pod naší kontrolou, takže může generovat "
"škodlivý kód!</b>\n"
"Ve výchozím nastavení budou vaše příkazy <b>virtualizovány v prostředí "
"Flatpak</b>, ale buďte opatrní!"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "Funkcionalitu Newelle můžete rozšířit pomocí rozšíření!"

#: src/ui/presentation.py:136
msgid "Download extensions"
msgstr "Stáhnout rozšíření"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "Chyba oprávnění"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr ""
"Newelle nemá dostatečná oprávnění ke spouštění příkazů ve vašem systému."

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "Začněte používat aplikaci"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "Začít chatovat"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "Obecné"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "LLM"

#: src/ui/settings.py:49 src/constants.py:528
msgid "Prompts"
msgstr "Výzvy"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "Znalosti"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "Jazykový model"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "Ostatní LLM"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "Další dostupní poskytovatelé LLM"

#: src/ui/settings.py:73
msgid "Advanced LLM Settings"
msgstr "Pokročilé nastavení LLM"

#: src/ui/settings.py:77
msgid "Secondary Language Model"
msgstr "Sekundární jazykový model"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr ""
"Model používaný pro sekundární úkoly, jako je nabídka, název chatu a "
"generování paměti"

#: src/ui/settings.py:94
msgid "Embedding Model"
msgstr "Model vkládání"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"Vkládání se používá k transformaci textu na vektory. Používá se pro "
"Dlouhodobou paměť a RAG. Změna může vyžadovat opětovnou indexaci dokumentů "
"nebo resetování paměti."

#: src/ui/settings.py:105 src/window.py:647
msgid "Long Term Memory"
msgstr "Dlouhodobá paměť"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "Udržovat paměť starých konverzací"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "Webové hledání"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "Vyhledávat informace na webu"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "Program převodu textu na řeč"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "Vyberte, který program pro převod textu na řeč použít"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "Engine pro převod řeči na text"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "Vyberte, který engine pro rozpoznávání řeči chcete"

#: src/ui/settings.py:150
msgid "Automatic Speech To Text"
msgstr "Automatický převod řeči na text"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr "Automaticky restartovat převod řeči na text po dokončení textu/TTS"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "Ovládání výzev"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "Rozhraní"

#: src/ui/settings.py:162
msgid "Interface Size"
msgstr "Velikost rozhraní"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "Upravte velikost rozhraní"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "Barevné schéma editoru"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "Změnit barevné schéma editoru a bloků kódu"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "Skryté soubory"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "Zobrazit skryté soubory"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "Odeslat pomocí ENTER"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"Pokud je povoleno, zprávy se budou odesílat pomocí ENTER, pro nový řádek "
"použijte CTRL+ENTER. Pokud je zakázáno, zprávy se budou odesílat pomocí "
"SHIFT+ENTER a nový řádek pomocí ENTER"

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "Odstranit myšlenky z historie"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr ""
"Neposílat staré myšlenkové bloky pro modely uvažování, aby se snížilo "
"využití tokenů"

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "Zobrazit LaTeX"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "Zobrazit LaTeX vzorce v chatu"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "Obrátit pořadí chatu"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr ""
"Zobrazit nejnovější chaty nahoře v seznamu chatů (pro použití změnit chat)"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "Automaticky generovat názvy chatů"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr "Automaticky generovat názvy chatů po prvních dvou zprávách"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "Počet nabídek"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "Počet návrhů zpráv k odeslání do chatu "

#: src/ui/settings.py:224
msgid "Username"
msgstr "Uživatelské jméno"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr ""
"Změňte popisek, který se zobrazí před vaší zprávou\n"
"Tyto informace nejsou ve výchozím nastavení odesílány do LLM\n"
"Můžete je přidat do výzvy pomocí proměnné {USER}"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "Ovládání neuronové sítě"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "Spouštět příkazy ve virtuálním stroji"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "Externí terminál"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr "Vyberte externí terminál, ve kterém se mají spouštět konzolové příkazy"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "Paměť programu"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "Jak dlouho si program pamatuje chat "

#: src/ui/settings.py:266
msgid "Developer"
msgstr "Vývojář"

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr ""
"Monitorujte výstup programu v reálném čase, užitečné pro ladění a sledování "
"průběhu stahování"

#: src/ui/settings.py:270
msgid "Open"
msgstr "Otevřít"

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr "Smazat cestu pip"

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr "Odebrat nainstalované extra závislosti"

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr ""

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr ""

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "Automatické spouštění příkazů"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "Příkazy, které bot napíše, se automaticky spustí"

#: src/ui/settings.py:313
msgid "Max number of commands"
msgstr "Max. počet příkazů"

#: src/ui/settings.py:313
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr ""
"Maximální počet příkazů, které bot napíše po jediném uživatelském požadavku"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "Prohlížeč"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "Nastavení prohlížeče"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "Použít externí prohlížeč"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr "Použít externí prohlížeč pro otevírání odkazů namísto integrovaného"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "Udržet relaci prohlížeče"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr ""
"Zachovat relaci prohlížeče mezi restarty. Vypnutí této možnosti vyžaduje "
"restart programu"

#: src/ui/settings.py:361
msgid "Delete browser data"
msgstr "Smazat data prohlížeče"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "Smazat relaci a data prohlížeče"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "Počáteční stránka prohlížeče"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "Stránka, na které se prohlížeč spustí"

#: src/ui/settings.py:375
msgid "Search string"
msgstr "Vyhledávací řetězec"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr "Vyhledávací řetězec použitý v prohlížeči, %s je nahrazen dotazem"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "Zdroje dokumentů (RAG)"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "Zahrnout obsah z vašich dokumentů do odpovědí"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "Analyzátor dokumentů"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr ""
"Analyzátor dokumentů používá více technik k extrakci relevantních informací "
"z vašich dokumentů"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "Číst dokumenty, pokud nejsou podporovány"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr ""
"Pokud LLM nepodporuje čtení dokumentů, relevantní informace o dokumentech "
"odeslaných v chatu budou předány LLM pomocí vašeho analyzátoru dokumentů."

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "Maximální počet tokenů pro RAG"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"Maximální počet tokenů, které mají být použity pro RAG. Pokud dokumenty "
"nepřesáhnou tento počet tokenů,\n"
"vložte je všechny do kontextu"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "Složka dokumentů"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr ""
"Vložte dokumenty, které chcete dotazovat, do vaší složky dokumentů. "
"Analyzátor dokumentů v nich najde relevantní informace, pokud je tato "
"možnost povolena"

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr "Vložte všechny dokumenty, které chcete indexovat, do této složky"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "Prah ticha"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr ""
"Prah ticha v sekundách, procento hlasitosti, které má být považováno za ticho"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "Čas ticha"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr "Čas ticha v sekundách před automatickým zastavením nahrávání"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "Nedostatečná oprávnění"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr ""
"Newelle nemá dostatečná oprávnění ke spouštění příkazů ve vašem systému, "
"spusťte prosím následující příkaz"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "Rozumím"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr "Cesta pip smazána"

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr ""
"Cesta pip byla smazána, nyní můžete přeinstalovat závislosti. Tato operace "
"vyžaduje restart aplikace."

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "Demo API Newelle"

#: src/constants.py:26
msgid "GPT4Free"
msgstr "GPT4Free"

#: src/constants.py:34
msgid "Local Model"
msgstr "Lokální model"

#: src/constants.py:35
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr ""
"BEZ PODPORY GPU, POUŽIJTE MÍSTO TOHO OLLAMA. Spusťte model LLM lokálně, více "
"soukromí, ale pomalejší"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "Instance Ollamy"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "Snadno spusťte více LLM modelů na vlastním hardwaru"

#: src/constants.py:47
msgid "Groq"
msgstr "Groq"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "API Google Gemini"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "API OpenAI"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr ""
"OpenAI API. Podporovány vlastní koncové body. Použijte pro vlastní "
"poskytovatele"

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "Anthropic Claude"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr ""
"Oficiální API pro modely Anthropic Claude, s podporou obrázků a souborů, "
"vyžaduje API klíč"

#: src/constants.py:73
msgid "Mistral"
msgstr "Mistral"

#: src/constants.py:74
msgid "Mistral API"
msgstr "API Mistral"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "Openrouter.ai API, podporuje mnoho modelů"

#: src/constants.py:87
msgid "Deepseek"
msgstr "Deepseek"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "Deepseek API, nejsilnější open source modely"

#: src/constants.py:94 src/constants.py:203
msgid "Custom Command"
msgstr "Vlastní příkaz"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "Použijte výstup vlastního příkazu"

#: src/constants.py:104
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr "Funguje offline. Optimalizovaná implementace Whisper napsaná v C++"

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "Funguje offline. Podporována je pouze angličtina"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "Rozpoznávání řeči Google"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "Online rozpoznávání řeči Google"

#: src/constants.py:124
msgid "Groq Speech Recognition"
msgstr "Groq Rozpoznávání řeči"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit AI"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr "Bezplatné API pro rozpoznávání řeči wit.ai (jazyk vybraný na webu)"

#: src/constants.py:137
msgid "Vosk API"
msgstr "API Vosk"

#: src/constants.py:138
msgid "Works Offline"
msgstr "Funguje offline"

#: src/constants.py:144
msgid "Whisper API"
msgstr "API Whisper"

#: src/constants.py:145
msgid "Uses OpenAI Whisper API"
msgstr "Používá API OpenAI Whisper"

#: src/constants.py:151
msgid "Custom command"
msgstr "Vlastní příkaz"

#: src/constants.py:152
msgid "Runs a custom command"
msgstr "Spustí vlastní příkaz"

#: src/constants.py:161
msgid "Google TTS"
msgstr "Google TTS"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "Převod textu na řeč od Google"

#: src/constants.py:167
msgid "Kokoro TTS"
msgstr "Kokoro TTS"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr "Lehký a rychlý open source TTS engine. ~3GB závislostí, 400MB model"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "ElevenLabs TTS"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "TTS s přirozeným zvukem"

#: src/constants.py:179 src/constants.py:180
msgid "OpenAI TTS"
msgstr "OpenAI TTS"

#: src/constants.py:185
msgid "Groq TTS"
msgstr "Groq TTS"

#: src/constants.py:186
msgid "Groq TTS API"
msgstr "Groq TTS API"

#: src/constants.py:191 src/constants.py:192
msgid "Custom OpenAI TTS"
msgstr "Vlastní OpenAI TTS"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "Espeak TTS"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "Offline TTS"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr "Použít vlastní příkaz jako TTS, {0} bude nahrazeno textem"

#: src/constants.py:212
msgid "WordLlama"
msgstr "WordLlama"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr ""
"Lehký lokální model vkládání založený na llama. Funguje offline, velmi nízká "
"spotřeba zdrojů"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "Ollama Vkládání"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr ""
"Použijte modely Ollama pro vkládání (Embedding). Funguje offline, velmi "
"nízká spotřeba zdrojů"

#: src/constants.py:231
msgid "Use Google Gemini API to get embeddings"
msgstr "Použijte API Google Gemini k získání vkládání"

#: src/constants.py:239
msgid "User Summary"
msgstr "Shrnutí uživatele"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "Generovat shrnutí uživatelské konverzace"

#: src/constants.py:245
msgid "Memoripy"
msgstr "Memoripy"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"Extrahujte zprávy z předchozích konverzací pomocí kontextového načítání "
"paměti, rozpadu paměti, extrakce konceptů a dalších pokročilých technik. "
"Provádí 1 volání LLM na zprávu."

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "Shrnutí uživatele + Memoripy"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "Použít obě technologie pro dlouhodobou paměť"

#: src/constants.py:260
msgid "Document reader"
msgstr "Čtečka dokumentů"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr ""
"Klasický RAG přístup – rozdělit dokumenty na části a vložit je, poté je "
"porovnat s dotazem a vrátit nejrelevantnější dokumenty"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr "SearXNG - Soukromý a samostatně hostovatelný vyhledávač"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "Vyhledávání DuckDuckGo"

#: src/constants.py:281
msgid "Tavily"
msgstr "Tavily"

#: src/constants.py:282
msgid "Tavily search"
msgstr "Vyhledávání Tavily"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "Užitečný asistent"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr "Obecná výzva pro vylepšení odpovědí LLM a poskytnutí více kontextu"

#: src/constants.py:384
msgid "Console access"
msgstr "Přístup ke konzoli"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "Může program spouštět terminálové příkazy na počítači"

#: src/constants.py:392
msgid "Current directory"
msgstr "Aktuální adresář"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "Jaký je aktuální adresář"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "Povolit LLM hledat na internetu"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "Základní funkcionalita"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "Zobrazování tabulek a kódu (*může fungovat i bez toho)"

#: src/constants.py:419
msgid "Graphs access"
msgstr "Přístup ke grafům"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "Může program zobrazovat grafy"

#: src/constants.py:428
msgid "Show image"
msgstr "Zobrazit obrázek"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "Zobrazit obrázek v chatu"

#: src/constants.py:437
msgid "Custom Prompt"
msgstr "Vlastní výzva"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "Přidejte si vlastní výzvu"

#: src/constants.py:480
msgid "LLM and Secondary LLM settings"
msgstr "Nastavení LLM a sekundárního LLM"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "TTS"

#: src/constants.py:485
msgid "Text to Speech settings"
msgstr "Nastavení převodu textu na řeč"

#: src/constants.py:488
msgid "STT"
msgstr "STT"

#: src/constants.py:490
msgid "Speech to Text settings"
msgstr "Nastavení převodu řeči na text"

#: src/constants.py:493
msgid "Embedding"
msgstr "Vkládání"

#: src/constants.py:495
msgid "Embedding settings"
msgstr "Nastavení vkládání"

#: src/constants.py:498
msgid "Memory"
msgstr "Paměť"

#: src/constants.py:500
msgid "Memory settings"
msgstr "Nastavení paměti"

#: src/constants.py:503
msgid "Websearch"
msgstr "Webové hledání"

#: src/constants.py:505
msgid "Websearch settings"
msgstr "Nastavení webového hledání"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
msgid "Document analyzer settings"
msgstr "Nastavení analyzátoru dokumentů"

#: src/constants.py:515
msgid "Extensions settings"
msgstr "Nastavení rozšíření"

#: src/constants.py:518
msgid "Inteface"
msgstr "Rozhraní"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr "Nastavení rozhraní, skryté soubory, obrácené pořadí, zoom..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr ""
"Obecná nastavení, virtualizace, nabídky, délka paměti, automatické "
"generování názvu chatu, aktuální složka..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr "Nastavení výzev, vlastní extra výzva, vlastní výzvy..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "Chat "

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "Terminálová vlákna stále běží na pozadí"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "Když zavřete okno, budou automaticky ukončeny"

#: src/main.py:210
msgid "Close"
msgstr "Zavřít"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "Chat byl restartován"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "Složka byla restartována"

#: src/main.py:254
msgid "Chat is created"
msgstr "Chat je vytvořen"

#: src/window.py:120
msgid "Keyboard shorcuts"
msgstr "Klávesové zkratky"

#: src/window.py:121
msgid "About"
msgstr "O aplikaci"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "Chat"

#: src/window.py:170
msgid "History"
msgstr "Historie"

#: src/window.py:191
msgid "Create a chat"
msgstr "Vytvořit chat"

#: src/window.py:196
msgid "Chats"
msgstr "Chaty"

#: src/window.py:267
msgid " Stop"
msgstr " Zastavit"

#: src/window.py:282
msgid " Clear"
msgstr " Vymazat"

#: src/window.py:297
msgid " Continue"
msgstr " Pokračovat"

#: src/window.py:310
msgid " Regenerate"
msgstr " Znovu vygenerovat"

#: src/window.py:376
msgid "Send a message..."
msgstr "Odeslat zprávu..."

#: src/window.py:467
msgid "Explorer Tab"
msgstr "Záložka Průzkumník"

#: src/window.py:468
msgid "Terminal Tab"
msgstr "Záložka Terminál"

#: src/window.py:469
msgid "Browser Tab"
msgstr "Záložka Prohlížeč"

#: src/window.py:589
msgid "Ask about a website"
msgstr "Zeptejte se na webovou stránku"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr ""
"Napište #https://website.com do chatu pro získání informací o webové stránce"

#: src/window.py:590
msgid "Check out our Extensions!"
msgstr "Podívejte se na naše rozšíření!"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "Máme spoustu rozšíření pro různé věci. Zkontrolujte je!"

#: src/window.py:591
msgid "Chat with documents!"
msgstr "Chatujte s dokumenty!"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr ""
"Přidejte své dokumenty do složky dokumentů a chatujte s využitím informací "
"obsažených v nich!"

#: src/window.py:592
msgid "Surf the web!"
msgstr "Surfujte po webu!"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr ""
"Povolte webové vyhledávání, aby LLM mohla surfovat po webu a poskytovat "
"aktuální odpovědi"

#: src/window.py:593
msgid "Mini Window"
msgstr "Mini okno"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "Klást otázky za pochodu pomocí režimu mini okna"

#: src/window.py:594
msgid "Text to Speech"
msgstr "Převod textu na řeč"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr "Newelle podporuje převod textu na řeč! Povolte jej v nastavení"

#: src/window.py:595
msgid "Keyboard Shortcuts"
msgstr "Klávesové zkratky"

#: src/window.py:595
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "Ovládejte Newelle pomocí klávesových zkratek"

#: src/window.py:596
msgid "Prompt Control"
msgstr "Ovládání výzev"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr ""
"Newelle vám poskytuje 100% kontrolu nad výzvami. Upravte si výzvy pro vaše "
"použití."

#: src/window.py:597
msgid "Thread Editing"
msgstr "Úprava vlákna"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "Zkontrolujte programy a procesy, které spouštíte z Newelle"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr "Programovatelné výzvy"

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr ""
"Do Newelle můžete přidávat dynamické výzvy s podmínkami a pravděpodobnostmi"

#: src/window.py:605
msgid "New Chat"
msgstr "Nový chat"

#: src/window.py:623
msgid "Provider Errror"
msgstr "Chyba poskytovatele"

#: src/window.py:646
msgid "Local Documents"
msgstr "Místní dokumenty"

#: src/window.py:650
msgid "Web search"
msgstr "Webové hledání"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "Tento poskytovatel nemá seznam modelů"

#: src/window.py:901
msgid " Models"
msgstr " Modely"

#: src/window.py:904
msgid "Search Models..."
msgstr "Hledat modely..."

#: src/window.py:1132
msgid "Create new profile"
msgstr "Vytvořit nový profil"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "Nepodařilo se rozpoznat váš hlas"

#: src/window.py:1303
msgid "Images"
msgstr "Obrázky"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "Soubory podporované LLM"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "Soubory podporované RAG"

#: src/window.py:1333
msgid "Supported Files"
msgstr "Podporované soubory"

#: src/window.py:1337
msgid "All Files"
msgstr "Všechny soubory"

#: src/window.py:1343
msgid "Attach file"
msgstr "Připojit soubor"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "Soubor nelze odeslat, dokud program neskončí"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "Soubor není rozpoznán"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "Zprávu již nelze pokračovat."

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "Zprávu již nelze znovu vygenerovat."

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "Chat je vymazán"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "Zpráva byla zrušena a smazána z historie"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr "Zprávu nelze odeslat, dokud program neskončí"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr "Nemůžete upravovat zprávu, když program běží."

#: src/window.py:3080
msgid "Prompt content"
msgstr "Obsah výzvy"

#: src/window.py:3339
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"Neuronová síť má přístup k vašemu počítači a veškerým datům v tomto chatu a "
"může spouštět příkazy, buďte opatrní, neneseme odpovědnost za neuronovou "
"síť. Nesdílejte žádné citlivé informace."

#: src/window.py:3368
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"Neuronová síť má přístup ke všem datům v tomto chatu, buďte opatrní, "
"neneseme odpovědnost za neuronovou síť. Nesdílejte žádné citlivé informace."

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "Špatná cesta ke složce"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "Vlákno nebylo dokončeno, číslo vlákna: "

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "Nepodařilo se otevřít složku"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "Chat je prázdný"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr ""
"Trénujte Newelle, aby dělala více s vlastními rozšířeními a novými AI "
"moduly, čímž získá váš chatbot nekonečné možnosti."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "AI chatbot"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "Rychlý výběr profilu"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
msgid "Message Editing"
msgstr "Úprava zpráv"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "Více než 10 standardních poskytovatelů AI"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "Opravy chyb"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
#, fuzzy
msgid "Minor bug fixes"
msgstr "Opravy chyb"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "Malá vylepšení"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "Vylepšení výkonu čtení a načítání lokálních dokumentů"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "Přidat možnost odesílání pomocí CTRL+Enter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "Vylepšit bloky kódu"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
msgid "Fix Kokoro TTS"
msgstr "Opravit Kokoro TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "Odebrat emotikony z TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "Nastavit API klíče jako pole pro hesla"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "Přidat podporu myšlení pro Gemini"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "Aktualizované překlady"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "Přidány nové funkce"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr ""
"Čtení webových stránek a vyhledávání na webu s SearXNG, DuckDuckGo a Tavily"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "Vylepšené vykreslování LaTeXu a správa dokumentů"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "Nový widget pro přemýšlení a obsluha OpenRouter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "Podpora zraku pro Llama4 na Groq"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "Nové překlady (tradiční čínština, bengálština, hindština)"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "Opraveno mnoho chyb, přidány některé funkce!"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "Podpora nových funkcí a opravy chyb"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "Přidáno mnoho nových funkcí a opraveny chyby"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "Přidány nové funkce a opraveny chyby"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr ""
"Aktualizována knihovna g4f s verzováním, přidány uživatelské příručky, "
"vylepšeno procházení rozšíření a vylepšena správa modelů."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"Byly implementovány opravy chyb a nové funkce. Upravili jsme architekturu "
"rozšíření, přidali nové modely a zavedli podporu vidění, spolu s dalšími "
"možnostmi."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "Stabilní verze"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
msgid "Added extension"
msgstr "Přidáno rozšíření"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
msgid "Blacklist of commands"
msgstr "Černá listina příkazů"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "Lokalizace"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "Přepracování"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "Qwersyk"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "Newelle: Váš pokročilý chat bot"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "ai;asistent;chat;chatgpt;gpt;llm;ollama;"

#~ msgid "max Tokens"
#~ msgstr "Max tokenů"

#~ msgid "Max tokens of the generated text"
#~ msgstr "Maximální počet tokenů generovaného textu"
