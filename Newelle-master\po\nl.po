# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
msgid ""
msgstr ""
"Project-Id-Version: \n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-07-03 09:00+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: \n"
"Language: nl\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 3.3.2\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "API-eindpunt"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr "API-basis-URL, wijzig deze om interferentie-API's te gebruiken"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid "Automatically Serve"
msgstr "Automatisch serveren"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"Voer Ollama automatisch op de achtergrond uit wanneer nodig als het niet "
"actief is. U kunt het uitschakelen met killall ollama."

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
#, fuzzy
msgid "Custom Model"
msgstr "Aangepast model"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
msgid "Use a custom model"
msgstr "Gebruik een aangepast model"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
msgid "Ollama Model"
msgstr "Ollama-model"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
msgid "Name of the Ollama Model"
msgstr "Naam van het Ollama-model"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "API-sleutel"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
msgid "API Key for "
msgstr "API-sleutel voor "

#: src/handlers/embeddings/openai_handler.py:38
msgid "API base url, change this to use different APIs"
msgstr "API-basis-URL, wijzig deze om verschillende API's te gebruiken"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
#, fuzzy
msgid "Use Custom Model"
msgstr "Aangepast model gebruiken"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "Model"

#: src/handlers/embeddings/openai_handler.py:44
msgid "Name of the Embedding Model to use"
msgstr "Naam van het inbeddingsmodel om te gebruiken"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
msgid " Model"
msgstr " Model"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
msgid "The API key to use"
msgstr "De API-sleutel om te gebruiken"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
msgid "The model to use"
msgstr "Het te gebruiken model"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "Max. tokens"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "Het maximum aantal te genereren tokens"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
#, fuzzy
msgid "Message Streaming"
msgstr "Berichtstreaming"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "Uitvoer van berichten geleidelijk streamen"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "Commando om uit te voeren om botoutput te verkrijgen"

#: src/handlers/llm/custom_handler.py:21
#, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr ""
"Commando om uit te voeren om botreactie te verkrijgen, {0} wordt vervangen "
"door een JSON-bestand met de chat, {1} door de systeemprompt"

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "Commando om uit te voeren om botsuggesties te verkrijgen"

#: src/handlers/llm/custom_handler.py:22
#, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"Commando om uit te voeren om chatsuggesties te verkrijgen, {0} wordt "
"vervangen door een JSON-bestand met de chat, {1} door de extra prompts, {2} "
"door het aantal te genereren suggesties. Moet een JSON-array retourneren met "
"de suggesties als strings"

#: src/handlers/llm/gpt4all_handler.py:67
msgid "RAM Required: "
msgstr "Vereist RAM: "

#: src/handlers/llm/gpt4all_handler.py:68
msgid "Parameters: "
msgstr "Parameters: "

#: src/handlers/llm/gpt4all_handler.py:69
msgid "Size: "
msgstr "Grootte: "

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
msgid "Model to use"
msgstr "Model om te gebruiken"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
msgid "Name of the model to use"
msgstr "Naam van het te gebruiken model"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "Model Manager"
msgstr "Modelbeheerder"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "Lijst met beschikbare modellen"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr "G4F bijwerken"

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "Privacybeleid"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "Open website privacybeleid"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "Denken inschakelen"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr ""
"Denken in het model toestaan, slechts enkele modellen worden ondersteund"

#: src/handlers/llm/ollama_handler.py:176
msgid "Add custom model"
msgstr "Aangepast model toevoegen"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"Voeg elk model toe aan deze lijst door naam:grootte te plaatsen\n"
"Of een gguf van hf met hf.co/gebruikersnaam/model"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr "Ollama bijwerken"

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "API-sleutel (vereist)"

#: src/handlers/llm/gemini_handler.py:94
msgid "API key got from ai.google.dev"
msgstr "API-sleutel verkregen van ai.google.dev"

#: src/handlers/llm/gemini_handler.py:98
msgid "AI Model to use"
msgstr "AI-model om te gebruiken"

#: src/handlers/llm/gemini_handler.py:103
msgid "Enable System Prompt"
msgstr "Systeemprompt inschakelen"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr ""
"Sommige modellen ondersteunen geen systeemprompt (of instructies voor "
"ontwikkelaars), schakel deze uit als u hierdoor fouten krijgt"

#: src/handlers/llm/gemini_handler.py:107
msgid "Inject system prompt"
msgstr "Systeemprompt injecteren"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr ""
"Zelfs als het model geen systeemprompts ondersteunt, plaats de prompts dan "
"boven aan het gebruikersbericht"

#: src/handlers/llm/gemini_handler.py:109
#, fuzzy
msgid "Thinking Settings"
msgstr "Denkinstellingen"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "Instellingen over denkmodellen"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr "Toon denken, schakel dit uit als uw model dit niet ondersteunt"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "Denkbudget inschakelen"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "Als denkbudget ingeschakeld moet worden"

#: src/handlers/llm/gemini_handler.py:112
msgid "Thinking Budget"
msgstr "Denkbudget"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "Hoeveel tijd te besteden aan denken"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "Afbeeldingsuitvoer"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr ""
"Afbeeldingsuitvoer inschakelen, alleen ondersteund door gemini-2.0-flash-exp"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "Veiligheidsinstellingen inschakelen"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr ""
"Schakel Google veiligheidsinstellingen in om te voorkomen dat schadelijke "
"inhoud wordt gegenereerd"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
msgid "Advanced Parameters"
msgstr "Geavanceerde parameters"

#: src/handlers/llm/gemini_handler.py:135
msgid "Enable advanced parameters"
msgstr "Geavanceerde parameters inschakelen"

#: src/handlers/llm/openai_handler.py:81
#, fuzzy
msgid "Include parameters like Top-P, Temperature, etc."
msgstr "Inclusief parameters zoals Max Tokens, Top-P, Temperatuur, enz."

#: src/handlers/llm/openai_handler.py:84
msgid "Name of the LLM Model to use"
msgstr "Naam van het te gebruiken LLM-model"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "Top-P"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr ""
"Een alternatief voor bemonstering met temperatuur, genaamd nucleus-"
"bemonstering"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "Temperatuur"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr ""
"Welke bemonsteringstemperatuur te gebruiken. Hogere waarden maken de uitvoer "
"willekeuriger"

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "Frequentieboete"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr ""
"Getal tussen -2.0 en 2.0. Positieve waarden verlagen de waarschijnlijkheid "
"dat het model dezelfde regel letterlijk herhaalt"

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "Aanwezigheidsboete"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr ""
"Getal tussen -2.0 en 2.0. Positieve waarden verlagen de waarschijnlijkheid "
"dat het model over nieuwe onderwerpen praat"

#: src/handlers/llm/openai_handler.py:108
#, fuzzy
msgid "Custom Options"
msgstr "Aangepaste prompt"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr ""

#: src/handlers/llm/openrouter_handler.py:14
msgid "Provider Sorting"
msgstr "Sorteervolgorde van providers"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "Kies providers op basis van prijs/doorvoer of latentie"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "Prijs"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "Doorvoer"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "Latentie"

#: src/handlers/llm/openrouter_handler.py:15
msgid "Providers Order"
msgstr "Volgorde van providers"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr ""
"Voeg de volgorde van te gebruiken providers toe, namen gescheiden door een "
"komma.\n"
"Leeg laten om niet te specificeren"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "Fallbacks toestaan"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "Fallbacks naar andere providers toestaan"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "Uw documenten indexeren"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"Indexeer alle documenten in uw documentmap. U moet deze bewerking uitvoeren "
"telkens wanneer u een document bewerkt/creëert, de documentanalysator "
"wijzigt of het inbeddingsmodel wijzigt"

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "Commando om uit te voeren"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "{0} wordt vervangen door het volledige pad van het model"

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr ""
"API-sleutel voor Google SR, schrijf 'default' om de standaard te gebruiken"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "Taal"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "De taal van de tekst die in IETF moet worden herkend"

#: src/handlers/stt/groqsr_handler.py:14
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr ""
"API-sleutel voor Groq SR, schrijf 'default' om de standaard te gebruiken"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "Groq Model"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "Naam van het Groq Model"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Specificeer de taal voor transcriptie. Gebruik ISO 639-1 taalcodes (bijv. "
"\"en\" voor Engels, \"fr\" voor Frans, enz.)."

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "Eindpunt voor OpenAI-verzoeken"

#: src/handlers/stt/openaisr_handler.py:18
msgid "API Key for OpenAI"
msgstr "API-sleutel voor OpenAI"

#: src/handlers/stt/openaisr_handler.py:25
msgid "Whisper Model"
msgstr "Whisper-model"

#: src/handlers/stt/openaisr_handler.py:26
msgid "Name of the OpenAI model"
msgstr "Naam van het OpenAI-model"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Optioneel: Specificeer de taal voor transcriptie. Gebruik ISO 639-1 "
"taalcodes (bijv. \"en\" voor Engels, \"fr\" voor Frans, enz.)."

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "Modelpad"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "Absoluut pad naar het VOSK-model (uitgepakt)"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
msgid "Name of the Whisper model"
msgstr "Naam van het Whisper-model"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "Server toegangstoken voor wit.ai"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "Kon de audio niet begrijpen"

#: src/handlers/stt/whispercpp_handler.py:47
#, fuzzy
msgid "Language of the recognition. For example en, it..."
msgstr "Taal van de herkenning."

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Model Library"
msgstr "Modellenbibliotheek"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Manage Whisper models"
msgstr "Whisper-modellen beheren"

#: src/handlers/stt/whispercpp_handler.py:49
#, fuzzy
msgid "Advanced Settings"
msgstr "Geavanceerde instellingen"

#: src/handlers/stt/whispercpp_handler.py:49
#, fuzzy
msgid "More advanced settings"
msgstr "Meer geavanceerde instellingen"

#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature to use"
msgstr "Temperatuur om te gebruiken"

#: src/handlers/stt/whispercpp_handler.py:51
#, fuzzy
msgid "Prompt for the recognition"
msgstr "Prompt voor de herkenning"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr "Prompt om te gebruiken voor de herkenning"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Endpoint"
msgstr "Eindpunt"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "Aangepast eindpunt van de te gebruiken service"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "Stem"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
msgid "The voice to use"
msgstr "De te gebruiken stem"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "Instructies"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr ""
"Instructies voor de stemgeneratie. Laat dit veld leeg om het te vermijden"

#: src/handlers/tts/custom_handler.py:17
#, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr ""
"{0} wordt vervangen door het volledige pad van het bestand, {1} door de tekst"

#: src/handlers/tts/elevenlabs_handler.py:11
msgid "API Key for ElevenLabs"
msgstr "API-sleutel voor ElevenLabs"

#: src/handlers/tts/elevenlabs_handler.py:19
msgid "Voice ID to use"
msgstr "Stem-ID om te gebruiken"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "Stabiliteit"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "stabiliteit van de stem"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "Gelijkenisboost"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "Verhoogt de algehele stemhelderheid en sprekergelijkenis"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "Stijl overdreven"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr ""
"Hoge waarden worden aanbevolen als de stijl van de spraak moet worden "
"overdreven"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "Kies de gewenste stem"

#: src/handlers/websearch/tavily.py:20
msgid "Token"
msgstr "Token"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "Tavily API-sleutel"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "Max. resultaten"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "Aantal te overwegen resultaten"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "De diepte van de zoekopdracht"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"De diepte van de zoekopdracht. Geavanceerd zoeken is afgestemd op het "
"ophalen van de meest relevante bronnen en inhoudsfragmenten voor uw "
"zoekopdracht, terwijl basis zoeken algemene inhoudsfragmenten van elke bron "
"biedt. Een basis zoekopdracht kost 1 API-tegoed, terwijl een geavanceerde "
"zoekopdracht 2 API-tegoeden kost."

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "De categorie van de zoekopdracht"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"De categorie van de zoekopdracht. Nieuws is nuttig voor het ophalen van "
"realtime updates, met name over politiek, sport en belangrijke actuele "
"gebeurtenissen die door reguliere mediabronnen worden behandeld. Algemeen is "
"voor bredere, meer algemene zoekopdrachten die een breed scala aan bronnen "
"kunnen omvatten."

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "Stukken per bron"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr ""
"Het aantal inhoudsfragmenten dat van elke bron moet worden opgehaald. De "
"lengte van elk fragment is maximaal 500 tekens. Alleen beschikbaar wanneer "
"de zoekdiepte geavanceerd is."

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "Aantal dagen terug vanaf de huidige datum om op te nemen"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "Alleen beschikbaar als het onderwerp nieuws is."

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "Antwoord opnemen"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"Voeg een door LLM gegenereerd antwoord toe aan de opgegeven zoekopdracht. "
"Basis zoeken retourneert een snel antwoord. Geavanceerd retourneert een "
"gedetailleerder antwoord."

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "Ruwe inhoud opnemen"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr ""
"Inclusief de opgeschoonde en geparste HTML-inhoud van elk zoekresultaat."

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "Afbeeldingen opnemen"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr ""
"Voer een afbeeldingenzoekopdracht uit en neem de resultaten op in de reactie."

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "Afbeeldingsbeschrijvingen opnemen"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr ""
"Wanneer 'Afbeeldingen opnemen' is ingeschakeld, voeg dan ook een "
"beschrijvende tekst toe voor elke afbeelding."

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "Domeinen opnemen"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr ""
"Een lijst met domeinen die specifiek moeten worden opgenomen in de "
"zoekresultaten."

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "Domeinen uitsluiten"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr ""
"Een lijst met domeinen die specifiek moeten worden uitgesloten van de "
"zoekresultaten."

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "Regio"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "Regio voor de zoekresultaten"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "Instellingen"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "Profielnaam"

#: src/ui/profile.py:58
#, fuzzy
msgid "Copied Settings"
msgstr "Gekopieerde instellingen"

#: src/ui/profile.py:58
msgid "Settings that will be copied to the new profile"
msgstr "Instellingen die naar het nieuwe profiel worden gekopieerd"

#: src/ui/profile.py:70
msgid "Create Profile"
msgstr "Profiel aanmaken"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "Profiel importeren"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
msgid "Edit Profile"
msgstr "Profiel bewerken"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "Profiel exporteren"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "Wachtwoorden exporteren"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "Exporteer ook wachtwoordachtige velden"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "Profielafbeelding exporteren"

#: src/ui/profile.py:89
msgid "Also export the profile picture"
msgstr "Exporteer ook de profielfoto"

#: src/ui/profile.py:109 src/ui/explorer.py:692
#, fuzzy
msgid "Create"
msgstr "Aanmaken"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "Toepassen"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr ""
"De instellingen van het huidige profiel worden gekopieerd naar het nieuwe "
"profiel"

#: src/ui/profile.py:122 src/ui/profile.py:128
msgid "Newelle Profiles"
msgstr "Newelle Profielen"

#: src/ui/profile.py:123
msgid "Export"
msgstr "Exporteren"

#: src/ui/profile.py:129
msgid "Import"
msgstr "Importeren"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "Profielfoto instellen"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "Thread bewerken"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "Geen threads actief"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "Threadnummer: "

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "Profiel selecteren"

#: src/ui/widgets/profilerow.py:53
msgid "Delete Profile"
msgstr "Profiel verwijderen"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "Gedachten"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "Uitvouwen om details te zien"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "Aan het denken..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr "De LLM denkt na... Uitvouwen om denkproces te zien"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "Geen denkproces vastgelegd"

#: src/ui/widgets/tipscarousel.py:41
msgid "Newelle Tips"
msgstr "Newelle Tips"

#: src/ui/explorer.py:192
msgid "Folder is Empty"
msgstr "Map is leeg"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr "Bestand niet gevonden"

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "Openen in nieuw tabblad"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "Openen in geïntegreerde editor"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "Openen in bestandsbeheerder"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
msgid "Rename"
msgstr "Hernoemen"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "Verwijderen"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "Volledig pad kopiëren"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
#, fuzzy
msgid "Failed to open file manager"
msgstr "Bestandsbeheerder kon niet worden geopend"

#: src/ui/explorer.py:436
#, fuzzy
msgid "New name:"
msgstr "Nieuwe naam:"

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "Annuleren"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "Succesvol hernoemd"

#: src/ui/explorer.py:476
#, fuzzy, python-brace-format
msgid "Failed to rename: {}"
msgstr "Hernoemen mislukt: {}"

#: src/ui/explorer.py:497
msgid "Delete File?"
msgstr "Bestand verwijderen?"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "Weet u zeker dat u \"{}\" wilt verwijderen?"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "Succesvol verwijderd"

#: src/ui/explorer.py:525
#, fuzzy, python-brace-format
msgid "Failed to delete: {}"
msgstr "Verwijderen mislukt: {}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "Pad gekopieerd naar klembord"

#: src/ui/explorer.py:542
#, fuzzy
msgid "Failed to copy path"
msgstr "Pad kopiëren mislukt"

#: src/ui/explorer.py:580
#, fuzzy
msgid "Create new folder"
msgstr "Nieuwe map aanmaken"

#: src/ui/explorer.py:583
msgid "Create new file"
msgstr "Nieuw bestand aanmaken"

#: src/ui/explorer.py:586
msgid "Open Terminal Here"
msgstr "Open Terminal hier"

#: src/ui/explorer.py:640
msgid "Create New Folder"
msgstr "Nieuwe map aanmaken"

#: src/ui/explorer.py:640
#, fuzzy
msgid "Folder name:"
msgstr "Mapnaam:"

#: src/ui/explorer.py:644
msgid "Create New File"
msgstr "Nieuw bestand aanmaken"

#: src/ui/explorer.py:644
#, fuzzy
msgid "File name:"
msgstr "Bestandsnaam:"

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "Map succesvol aangemaakt"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "Bestand succesvol aangemaakt"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "Een bestand of map met die naam bestaat al"

#: src/ui/explorer.py:728
#, fuzzy
msgid "folder"
msgstr "map"

#: src/ui/explorer.py:728
msgid "file"
msgstr "bestand"

#: src/ui/explorer.py:730
#, fuzzy, python-brace-format
msgid "Failed to create {}: {}"
msgstr "Maken van {}: {} mislukt"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "Help"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "Sneltoetsen"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "Chat opnieuw laden"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "Map opnieuw laden"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "Nieuw tabblad"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "Afbeelding plakken"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "Berichtvak focussen"

#: src/ui/shortcuts.py:18
msgid "Start/stop recording"
msgstr "Opname starten/stoppen"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "Opslaan"

#: src/ui/shortcuts.py:20
#, fuzzy
msgid "Stop TTS"
msgstr "TTS stoppen"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr "Inzoomen"

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr "Uitzoomen"

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr "Programma-uitvoer monitor"

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr "Uitvoer wissen"

#: src/ui/stdout_monitor.py:61
msgid "Start/Stop monitoring"
msgstr "Monitoring starten/stoppen"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr "Monitoring: Actief"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr "Monitoring: Gestopt"

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr "Regels: {}"

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr "Regels: 0"

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "Extensies"

#: src/ui/extension.py:50
#, fuzzy
msgid "Installed Extensions"
msgstr "Geïnstalleerde extensies"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "Gebruikershandleiding voor extensies"

#: src/ui/extension.py:89
#, fuzzy
msgid "Download new Extensions"
msgstr "Nieuwe extensies downloaden"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "Extensie installeren vanuit bestand..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "Newelle"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "Chat is geopend in mini-venster"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "Welkom bij Newelle"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "Uw ultieme virtuele assistent."

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "Github-pagina"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "Kies uw favoriete AI-taalmodel"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"Newelle kan worden gebruikt met meerdere modellen en providers!\n"
"<b>Opmerking: Het wordt sterk aangeraden om de handleiding voor LLM te "
"lezen</b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "Handleiding voor LLM"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "Chatten met uw documenten"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr ""
"Newelle kan relevante informatie ophalen uit documenten die u in de chat "
"verstuurt of uit uw eigen bestanden! Informatie die relevant is voor uw "
"zoekopdracht wordt naar de LLM gestuurd."

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "Commando virtualisatie"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"Newelle kan worden gebruikt om commando's uit te voeren op uw systeem, maar "
"let op wat u uitvoert! <b>De LLM staat niet onder onze controle, dus het kan "
"kwaadaardige code genereren!</b>\n"
"Standaard worden uw commando's <b>gevirtualiseerd in de Flatpak-omgeving</"
"b>, maar let op!"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "U kunt de functionaliteiten van Newelle uitbreiden met extensies!"

#: src/ui/presentation.py:136
#, fuzzy
msgid "Download extensions"
msgstr "Extensies downloaden"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "Toestemmingsfout"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr ""
"Newelle heeft onvoldoende machtigingen om commando's op uw systeem uit te "
"voeren."

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "Begin met het gebruiken van de app"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "Begin met chatten"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "Algemeen"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "LLM"

#: src/ui/settings.py:49 src/constants.py:528
msgid "Prompts"
msgstr "Prompts"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "Kennis"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "Taalmodel"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "Andere LLM's"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "Andere beschikbare LLM-providers"

#: src/ui/settings.py:73
#, fuzzy
msgid "Advanced LLM Settings"
msgstr "Geavanceerde LLM-instellingen"

#: src/ui/settings.py:77
msgid "Secondary Language Model"
msgstr "Secundair taalmodel"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr ""
"Model gebruikt voor secundaire taken, zoals aanbod, chatnaam en "
"geheugengeneratie"

#: src/ui/settings.py:94
msgid "Embedding Model"
msgstr "Inbeddingsmodel"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"Inbedding wordt gebruikt om tekst om te zetten in vectoren. Wordt gebruikt "
"door Langetermijngeheugen en RAG. Het wijzigen ervan kan vereisen dat u "
"documenten opnieuw indexeert of het geheugen opnieuw instelt."

#: src/ui/settings.py:105 src/window.py:647
#, fuzzy
msgid "Long Term Memory"
msgstr "Langetermijngeheugen"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "Geheugen van oude gesprekken bewaren"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "Webzoekopdracht"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "Informatie op het web zoeken"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "Tekst naar spraakprogramma"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "Kies welke tekst naar spraak te gebruiken"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "Spraak naar tekst-engine"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "Kies welke spraakherkenningsengine u wilt"

#: src/ui/settings.py:150
msgid "Automatic Speech To Text"
msgstr "Automatische spraak naar tekst"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr ""
"Automatisch spraak naar tekst opnieuw starten aan het einde van een tekst/TTS"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "Promptbeheer"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "Interface"

#: src/ui/settings.py:162
#, fuzzy
msgid "Interface Size"
msgstr "Interfagrootte"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "Pas de grootte van de interface aan"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "Kleurenschema van editor"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "Wijzig het kleurenschema van de editor en codeblokken"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "Verborgen bestanden"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "Verborgen bestanden tonen"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "Verzenden met ENTER"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"Indien ingeschakeld, worden berichten verzonden met ENTER. Gebruik "
"CTRL+ENTER voor een nieuwe regel. Indien uitgeschakeld, worden berichten "
"verzonden met SHIFT+ENTER en een nieuwe regel met enter"

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "Denken uit geschiedenis verwijderen"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr ""
"Stuur geen oude denkblokken voor redeneermodellen om het tokengebruik te "
"verminderen"

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "LaTeX weergeven"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "LaTeX-formules weergeven in chat"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "Chatvolgorde omkeren"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr ""
"Toon meest recente chats bovenaan in chatlijst (wijzig chat om toe te passen)"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "Automatisch chatnamen genereren"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr "Chatnamen automatisch genereren na de eerste twee berichten"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "Aantal aanbiedingen"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "Aantal berichtsuggesties om naar de chat te sturen"

#: src/ui/settings.py:224
msgid "Username"
msgstr "Gebruikersnaam"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr ""
"Wijzig het label dat voor uw bericht verschijnt\n"
"Deze informatie wordt standaard niet naar de LLM gestuurd\n"
"U kunt het toevoegen aan een prompt met behulp van de variabele {USER}"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "Neuraal netwerkbeheer"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "Commando's uitvoeren in een virtuele machine"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "Externe terminal"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr "Kies de externe terminal om de consolecommando's uit te voeren"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "Programmgeheugen"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "Hoe lang het programma de chat onthoudt"

#: src/ui/settings.py:266
msgid "Developer"
msgstr "Ontwikkelaar"

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr ""
"Monitor de programma-uitvoer in real-time, nuttig voor debuggen en het zien "
"van downloadvoortgang"

#: src/ui/settings.py:270
msgid "Open"
msgstr "Openen"

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr "Pip-pad verwijderen"

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr "Verwijder de extra geïnstalleerde afhankelijkheden"

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr ""

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr ""

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "Automatisch uitvoeren van commando's"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "Commando's die de bot schrijft, worden automatisch uitgevoerd"

#: src/ui/settings.py:313
#, fuzzy
msgid "Max number of commands"
msgstr "Max. aantal commando's"

#: src/ui/settings.py:313
#, fuzzy
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr ""
"Maximaal aantal commando's dat de bot zal schrijven na één gebruikersverzoek"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "Browser"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "Instellingen voor de browser"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "Externe browser gebruiken"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr ""
"Gebruik een externe browser om links te openen in plaats van de geïntegreerde"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "Browsersessie behouden"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr ""
"Browsersessie behouden tussen herstarts. Als u dit uitschakelt, moet het "
"programma opnieuw worden gestart"

#: src/ui/settings.py:361
msgid "Delete browser data"
msgstr "Browsergegevens verwijderen"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "Browsersessie en gegevens verwijderen"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "Startpagina van de browser"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "De pagina waar de browser zal starten"

#: src/ui/settings.py:375
#, fuzzy
msgid "Search string"
msgstr "Zoekreeks"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr ""
"De zoekreeks die in de browser wordt gebruikt, %s wordt vervangen door de "
"zoekopdracht"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "Documentbronnen (RAG)"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "Inhoud van uw documenten opnemen in de antwoorden"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "Documentanalysator"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr ""
"De documentanalysator gebruikt meerdere technieken om relevante informatie "
"over uw documenten te extraheren"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "Documenten lezen indien niet ondersteund"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr ""
"Als de LLM het lezen van documenten niet ondersteunt, wordt relevante "
"informatie over documenten die in de chat zijn verzonden, aan de LLM gegeven "
"met behulp van uw documentanalysator."

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "Maximaal aantal tokens voor RAG"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"Het maximale aantal tokens dat voor RAG moet worden gebruikt. Als de "
"documenten dit tokentelling niet overschrijden,\n"
"dump ze allemaal in de context"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "Documentmap"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr ""
"Plaats de documenten die u wilt opvragen in uw documentmap. De "
"documentanalysator vindt er relevante informatie in als deze optie is "
"ingeschakeld"

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr "Plaats alle documenten die u wilt indexeren in deze map"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "Stiltegrens"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr ""
"Stiltegrens in seconden, percentage van het volume dat als stilte moet "
"worden beschouwd"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "Stiltetijd"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr "Stiltetijd in seconden voordat de opname automatisch stopt"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "Niet genoeg rechten"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr ""
"Newelle heeft onvoldoende machtigingen om commando's op uw systeem uit te "
"voeren, voer het volgende commando uit"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "Begrepen"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr "Pip-pad verwijderd"

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr ""
"Het pip-pad is verwijderd, u kunt nu de afhankelijkheden opnieuw "
"installeren. Deze bewerking vereist een herstart van de applicatie."

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "Newelle Demo API"

#: src/constants.py:26
msgid "GPT4Free"
msgstr "GPT4Free"

#: src/constants.py:34
msgid "Local Model"
msgstr "Lokaal model"

#: src/constants.py:35
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr ""
"GEEN GPU ONDERSTEUNING, GEBRUIK OLLAMA IN PLAATS. Voer een LLM-model lokaal "
"uit, meer privacy maar langzamer"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "Ollama-instantie"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "Eenvoudig meerdere LLM-modellen op uw eigen hardware uitvoeren"

#: src/constants.py:47
msgid "Groq"
msgstr "Groq"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "Google Gemini API"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "OpenAI API"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr ""
"OpenAI API. Aangepaste eindpunten ondersteund. Gebruik dit voor aangepaste "
"providers"

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "Anthropic Claude"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr ""
"Officiële API's voor Anthropic Claude's modellen, met afbeeldingen- en "
"bestandondersteuning, vereist een API-sleutel"

#: src/constants.py:73
msgid "Mistral"
msgstr "Mistral"

#: src/constants.py:74
msgid "Mistral API"
msgstr "Mistral API"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "Openrouter.ai API, ondersteunt veel modellen"

#: src/constants.py:87
msgid "Deepseek"
msgstr "Deepseek"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "Deepseek API, sterkste open source modellen"

#: src/constants.py:94 src/constants.py:203
#, fuzzy
msgid "Custom Command"
msgstr "Aangepast commando"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "Gebruik de uitvoer van een aangepast commando"

#: src/constants.py:104
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr ""
"Werkt offline. Geoptimaliseerde Whisper-implementatie geschreven in C++"

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "Werkt offline. Alleen Engels wordt ondersteund"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "Google spraakherkenning"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "Google spraakherkenning online"

#: src/constants.py:124
msgid "Groq Speech Recognition"
msgstr "Groq spraakherkenning"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit AI"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr "wit.ai spraakherkenning gratis API (taal gekozen op de website)"

#: src/constants.py:137
msgid "Vosk API"
msgstr "Vosk API"

#: src/constants.py:138
msgid "Works Offline"
msgstr "Werkt offline"

#: src/constants.py:144
msgid "Whisper API"
msgstr "Whisper API"

#: src/constants.py:145
msgid "Uses OpenAI Whisper API"
msgstr "Gebruikt OpenAI Whisper API"

#: src/constants.py:151
#, fuzzy
msgid "Custom command"
msgstr "Aangepast commando"

#: src/constants.py:152
#, fuzzy
msgid "Runs a custom command"
msgstr "Voert een aangepast commando uit"

#: src/constants.py:161
msgid "Google TTS"
msgstr "Google TTS"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "Google's tekst naar spraak"

#: src/constants.py:167
msgid "Kokoro TTS"
msgstr "Kokoro TTS"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr ""
"Lichtgewicht en snelle open source TTS-engine. ~3GB afhankelijkheden, 400MB "
"model"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "ElevenLabs TTS"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "Natuurlijk klinkende TTS"

#: src/constants.py:179 src/constants.py:180
msgid "OpenAI TTS"
msgstr "OpenAI TTS"

#: src/constants.py:185
#, fuzzy
msgid "Groq TTS"
msgstr "Groq TTS"

#: src/constants.py:186
msgid "Groq TTS API"
msgstr "Groq TTS API"

#: src/constants.py:191 src/constants.py:192
msgid "Custom OpenAI TTS"
msgstr "Aangepaste OpenAI TTS"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "Espeak TTS"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "Offline TTS"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr ""
"Gebruik een aangepast commando als TTS, {0} wordt vervangen door de tekst"

#: src/constants.py:212
msgid "WordLlama"
msgstr "WordLlama"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr ""
"Licht lokaal inbeddingsmodel gebaseerd op Llama. Werkt offline, zeer laag "
"resourcegebruik"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "Ollama-inbedding"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr ""
"Gebruik Ollama-modellen voor inbedding. Werkt offline, zeer laag "
"resourcegebruik"

#: src/constants.py:231
msgid "Use Google Gemini API to get embeddings"
msgstr "Gebruik Google Gemini API om inbeddingen te verkrijgen"

#: src/constants.py:239
msgid "User Summary"
msgstr "Gebruikerssamenvatting"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "Genereer een samenvatting van het gesprek van de gebruiker"

#: src/constants.py:245
msgid "Memoripy"
msgstr "Memoripy"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"Haal berichten op uit eerdere gesprekken met behulp van contextuele "
"geheugenherstel, geheugenverval, conceptextractie en andere geavanceerde "
"technieken. Voert 1 LLM-oproep per bericht uit."

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "Gebruikerssamenvatting + Memoripy"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "Gebruik beide technologieën voor langetermijngeheugen"

#: src/constants.py:260
msgid "Document reader"
msgstr "Documentlezer"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr ""
"Klassieke RAG-aanpak - chunk documenten en embed ze, vergelijk ze vervolgens "
"met de zoekopdracht en retourneer de meest relevante documenten"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr "SearXNG - Privé en zelf te hosten zoekmachine"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "DuckDuckGo zoeken"

#: src/constants.py:281
msgid "Tavily"
msgstr "Tavily"

#: src/constants.py:282
msgid "Tavily search"
msgstr "Tavily zoeken"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "Behulpzame assistent"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr ""
"Algemeen doelprompt om de LLM-antwoorden te verbeteren en meer context te "
"geven"

#: src/constants.py:384
msgid "Console access"
msgstr "Consoletoegang"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "Kan het programma terminalcommando's uitvoeren op de computer"

#: src/constants.py:392
msgid "Current directory"
msgstr "Huidige map"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "Wat is de huidige map"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "Sta de LLM toe om op internet te zoeken"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "Basisfunctionaliteit"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "Tabellen en code tonen (*kan ook zonder)"

#: src/constants.py:419
msgid "Graphs access"
msgstr "Grafiektoegang"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "Kan het programma grafieken weergeven"

#: src/constants.py:428
msgid "Show image"
msgstr "Afbeelding tonen"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "Afbeelding tonen in chat"

#: src/constants.py:437
#, fuzzy
msgid "Custom Prompt"
msgstr "Aangepaste prompt"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "Voeg uw eigen aangepaste prompt toe"

#: src/constants.py:480
#, fuzzy
msgid "LLM and Secondary LLM settings"
msgstr "LLM- en secundaire LLM-instellingen"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "TTS"

#: src/constants.py:485
msgid "Text to Speech settings"
msgstr "Tekst naar spraak instellingen"

#: src/constants.py:488
msgid "STT"
msgstr "STT"

#: src/constants.py:490
msgid "Speech to Text settings"
msgstr "Spraak naar tekst instellingen"

#: src/constants.py:493
msgid "Embedding"
msgstr "Inbedding"

#: src/constants.py:495
#, fuzzy
msgid "Embedding settings"
msgstr "Inbeddingsinstellingen"

#: src/constants.py:498
msgid "Memory"
msgstr "Geheugen"

#: src/constants.py:500
#, fuzzy
msgid "Memory settings"
msgstr "Geheugeninstellingen"

#: src/constants.py:503
msgid "Websearch"
msgstr "Webzoekopdracht"

#: src/constants.py:505
#, fuzzy
msgid "Websearch settings"
msgstr "Webzoekinstellingen"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
msgid "Document analyzer settings"
msgstr "Instellingen documentanalysator"

#: src/constants.py:515
#, fuzzy
msgid "Extensions settings"
msgstr "Extensie-instellingen"

#: src/constants.py:518
#, fuzzy
msgid "Inteface"
msgstr "Interface"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr ""
"Interface-instellingen, verborgen bestanden, omgekeerde volgorde, zoom..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr ""
"Algemene instellingen, virtualisatie, aanbiedingen, geheugenlengte, "
"automatisch chatnaam genereren, huidige map..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr "Promptinstellingen, aangepaste extra prompt, aangepaste prompts..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "Chat"

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "Terminal-threads draaien nog steeds op de achtergrond"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "Wanneer u het venster sluit, worden ze automatisch beëindigd"

#: src/main.py:210
msgid "Close"
msgstr "Sluiten"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "Chat is opnieuw opgestart"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "Map is opnieuw opgestart"

#: src/main.py:254
msgid "Chat is created"
msgstr "Chat is aangemaakt"

#: src/window.py:120
msgid "Keyboard shorcuts"
msgstr "Sneltoetsen"

#: src/window.py:121
msgid "About"
msgstr "Over"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "Chat"

#: src/window.py:170
msgid "History"
msgstr "Geschiedenis"

#: src/window.py:191
msgid "Create a chat"
msgstr "Een chat aanmaken"

#: src/window.py:196
#, fuzzy
msgid "Chats"
msgstr "Chats"

#: src/window.py:267
msgid " Stop"
msgstr " Stoppen"

#: src/window.py:282
msgid " Clear"
msgstr " Wissen"

#: src/window.py:297
msgid " Continue"
msgstr " Doorgaan"

#: src/window.py:310
msgid " Regenerate"
msgstr " Hergenereren"

#: src/window.py:376
msgid "Send a message..."
msgstr "Stuur een bericht..."

#: src/window.py:467
msgid "Explorer Tab"
msgstr "Verkenner-tabblad"

#: src/window.py:468
msgid "Terminal Tab"
msgstr "Terminal-tabblad"

#: src/window.py:469
msgid "Browser Tab"
msgstr "Browser-tabblad"

#: src/window.py:589
msgid "Ask about a website"
msgstr "Vraag over een website"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr ""
"Schrijf #https://website.com in de chat om informatie over een website te "
"vragen"

#: src/window.py:590
msgid "Check out our Extensions!"
msgstr "Bekijk onze extensies!"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "We hebben veel extensies voor verschillende dingen. Bekijk het!"

#: src/window.py:591
msgid "Chat with documents!"
msgstr "Chatten met documenten!"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr ""
"Voeg uw documenten toe aan uw documentenmap en chat met behulp van de "
"informatie die daarin is opgenomen!"

#: src/window.py:592
msgid "Surf the web!"
msgstr "Surf op het web!"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr ""
"Schakel webzoekopdracht in om de LLM toe te staan op het web te surfen en "
"actuele antwoorden te geven"

#: src/window.py:593
msgid "Mini Window"
msgstr "Mini-venster"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "Stel vragen direct met de mini-venstermodus"

#: src/window.py:594
msgid "Text to Speech"
msgstr "Tekst naar spraak"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr ""
"Newelle ondersteunt tekst-naar-spraak! Schakel het in via de instellingen"

#: src/window.py:595
#, fuzzy
msgid "Keyboard Shortcuts"
msgstr "Sneltoetsen"

#: src/window.py:595
#, fuzzy
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "Bedien Newelle met sneltoetsen"

#: src/window.py:596
#, fuzzy
msgid "Prompt Control"
msgstr "Promptbeheer"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr "Newelle geeft u 100% promptbeheer. Stem uw prompts af op uw gebruik."

#: src/window.py:597
#, fuzzy
msgid "Thread Editing"
msgstr "Thread bewerken"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "Controleer de programma's en processen die u vanuit Newelle uitvoert"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr "Programmeerbare prompts"

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr ""
"U kunt dynamische prompts aan Newelle toevoegen, met voorwaarden en "
"waarschijnlijkheden"

#: src/window.py:605
#, fuzzy
msgid "New Chat"
msgstr "Nieuwe chat"

#: src/window.py:623
msgid "Provider Errror"
msgstr "Providerfout"

#: src/window.py:646
msgid "Local Documents"
msgstr "Lokale documenten"

#: src/window.py:650
msgid "Web search"
msgstr "Webzoekopdracht"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "Deze provider heeft geen modellenlijst"

#: src/window.py:901
msgid " Models"
msgstr " Modellen"

#: src/window.py:904
msgid "Search Models..."
msgstr "Modellen zoeken..."

#: src/window.py:1132
msgid "Create new profile"
msgstr "Nieuw profiel aanmaken"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "Kon uw stem niet herkennen"

#: src/window.py:1303
msgid "Images"
msgstr "Afbeeldingen"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "LLM ondersteunde bestanden"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "RAG ondersteunde bestanden"

#: src/window.py:1333
msgid "Supported Files"
msgstr "Ondersteunde bestanden"

#: src/window.py:1337
msgid "All Files"
msgstr "Alle bestanden"

#: src/window.py:1343
msgid "Attach file"
msgstr "Bestand bijvoegen"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "Het bestand kan pas worden verzonden als het programma is voltooid"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "Het bestand wordt niet herkend"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "U kunt het bericht niet meer voortzetten."

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "U kunt het bericht niet meer opnieuw genereren."

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "Chat is gewist"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "Het bericht is geannuleerd en uit de geschiedenis verwijderd"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr "Het bericht kan pas worden verzonden als het programma is voltooid"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr "U kunt een bericht niet bewerken terwijl het programma actief is."

#: src/window.py:3080
#, fuzzy
msgid "Prompt content"
msgstr "Promptinhoud"

#: src/window.py:3339
#, fuzzy
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"Het neurale netwerk heeft toegang tot uw computer en alle gegevens in deze "
"chat en kan commando's uitvoeren. Wees voorzichtig, wij zijn niet "
"verantwoordelijk voor het neurale netwerk. Deel geen gevoelige informatie."

#: src/window.py:3368
#, fuzzy
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"Het neurale netwerk heeft toegang tot alle gegevens in deze chat. Wees "
"voorzichtig, wij zijn niet verantwoordelijk voor het neurale netwerk. Deel "
"geen gevoelige informatie."

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "Verkeerd map-pad"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "Thread is niet voltooid, threadnummer: "

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "Map kon niet worden geopend"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "Chat is leeg"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr ""
"Train Newelle om meer te doen met aangepaste extensies en nieuwe AI-modules, "
"waardoor uw chatbot eindeloze mogelijkheden krijgt."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "AI-chatbot"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "Snelle profielselectie"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
#, fuzzy
msgid "Message Editing"
msgstr "Bericht bewerken"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "Meer dan 10 standaard AI-providers"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "Bugfixes"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
#, fuzzy
msgid "Minor bug fixes"
msgstr "Bugfixes"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "Kleine verbeteringen"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "Verbeterde lees- en laadprestaties van lokale documenten"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "Optie toevoegen om met CTRL+Enter te verzenden"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "Codeblokken verbeteren"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
msgid "Fix Kokoro TTS"
msgstr "Kokoro TTS repareren"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "Emoji's verwijderen uit TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "API-sleutels instellen als wachtwoordvelden"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "Denkondersteuning voor Gemini toevoegen"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "Bijgewerkte vertalingen"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "Nieuwe functies toegevoegd"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr "Website lezen en web zoeken met SearXNG, DuckDuckGo en Tavily"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "Verbeterde LaTeX-weergave en documentbeheer"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "Nieuwe Thinking Widget en OpenRouter handler"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "Visieondersteuning voor Llama4 op Groq"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "Nieuwe vertalingen (Traditioneel Chinees, Bengaals, Hindi)"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "Veel bugs verholpen, enkele functies toegevoegd!"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "Ondersteuning voor nieuwe functies en bugfixes"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "Veel nieuwe functies en bugfixes toegevoegd"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "Nieuwe functies en bugfixes toegevoegd"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr ""
"De g4f-bibliotheek bijgewerkt met versiebeheer, gebruikershandleidingen "
"toegevoegd, browsen van extensies verbeterd en modelbeheer verbeterd."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"Bugfixes en nieuwe functies zijn geïmplementeerd. We hebben de extensie "
"architectuur aangepast, nieuwe modellen toegevoegd en visieondersteuning "
"geïntroduceerd, samen met meer mogelijkheden."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "Stabiele versie"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
#, fuzzy
msgid "Added extension"
msgstr "Extensie toegevoegd"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
#, fuzzy
msgid "Blacklist of commands"
msgstr "Zwarte lijst van commando's"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "Lokalisatie"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "Redesign"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "Qwersyk"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "Newelle: Uw geavanceerde chatbot"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "ai;assistent;chat;chatgpt;gpt;llm;ollama;"

#~ msgid "max Tokens"
#~ msgstr "Max. tokens"

#~ msgid "Max tokens of the generated text"
#~ msgstr "Max. tokens van de gegenereerde tekst"

#, fuzzy
#~ msgid "_Cancel"
#~ msgstr "Annuleren"

#~ msgid "Choose an extension"
#~ msgstr "Kies een uitbreiding"

#~ msgid " has been removed"
#~ msgstr " is verwijderd"

#~ msgid "Extension added. New extensions will run from the next launch"
#~ msgstr ""
#~ "Extensie toegevoegd. Nieuwe extensies worden vanaf de volgende lancering "
#~ "uitgevoerd"

#~ msgid "The extension is wrong"
#~ msgstr "De extensie is fout"

#~ msgid "This is not an extension"
#~ msgstr "Dit is geen extensie"

#~ msgid "Chat has been stopped"
#~ msgstr "De chat is gestopt"

#~ msgid "The change will take effect after you restart the program."
#~ msgstr ""
#~ "De wijziging wordt van kracht nadat u het programma opnieuw hebt gestart."
