msgid ""
msgstr ""
"Project-Id-Version: 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-07-03 09:00+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Spanish <<EMAIL>>\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "Punto de conexión de la API"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr "URL base de la API, cámbiela para usar APIs de inferencia"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid "Automatically Serve"
msgstr "Servir automáticamente"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"Ejecuta automáticamente ollama serve en segundo plano cuando sea necesario "
"si no se está ejecutando. Puedes detenerlo con killall ollama"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
msgid "Custom Model"
msgstr "Modelo personalizado"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
msgid "Use a custom model"
msgstr "Usar un modelo personalizado"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
msgid "Ollama Model"
msgstr "Modelo Ollama"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
msgid "Name of the Ollama Model"
msgstr "Nombre del modelo Ollama"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "Clave de API"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
msgid "API Key for "
msgstr "Clave de API para "

#: src/handlers/embeddings/openai_handler.py:38
msgid "API base url, change this to use different APIs"
msgstr "URL base de la API, cámbiela para usar diferentes APIs"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
msgid "Use Custom Model"
msgstr "Usar modelo personalizado"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "Modelo"

#: src/handlers/embeddings/openai_handler.py:44
msgid "Name of the Embedding Model to use"
msgstr "Nombre del modelo de embedding a usar"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
msgid " Model"
msgstr " Modelo"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
msgid "The API key to use"
msgstr "La clave de API a usar"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
msgid "The model to use"
msgstr "El modelo a usar"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "Tokens máximos"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "El número máximo de tokens a generar"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
msgid "Message Streaming"
msgstr "Transmisión de mensajes"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "Transmite la salida del mensaje gradualmente"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "Comando a ejecutar para obtener la salida del bot"

#: src/handlers/llm/custom_handler.py:21
#, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr ""
"Comando a ejecutar para obtener la respuesta del bot, {0} será reemplazado "
"por un archivo JSON que contiene el chat, {1} por el mensaje del sistema"

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "Comando a ejecutar para obtener sugerencias del bot"

#: src/handlers/llm/custom_handler.py:22
#, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"Comando a ejecutar para obtener sugerencias de chat, {0} será reemplazado "
"por un archivo JSON que contiene el chat, {1} por los prompts adicionales, "
"{2} por el número de sugerencias a generar. Debe devolver un array JSON que "
"contenga las sugerencias como cadenas de texto"

#: src/handlers/llm/gpt4all_handler.py:67
msgid "RAM Required: "
msgstr "RAM requerida: "

#: src/handlers/llm/gpt4all_handler.py:68
msgid "Parameters: "
msgstr "Parámetros: "

#: src/handlers/llm/gpt4all_handler.py:69
msgid "Size: "
msgstr "Tamaño: "

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
msgid "Model to use"
msgstr "Modelo a usar"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
msgid "Name of the model to use"
msgstr "Nombre del modelo a usar"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "Model Manager"
msgstr "Gestor de modelos"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "Lista de modelos disponibles"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr "Actualizar G4F"

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "Política de privacidad"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "Abrir el sitio web de la política de privacidad"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "Habilitar pensamiento"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr "Permitir pensar en el modelo, solo algunos modelos son compatibles"

#: src/handlers/llm/ollama_handler.py:176
msgid "Add custom model"
msgstr "Añadir modelo personalizado"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"Añada cualquier modelo a esta lista poniendo nombre:tamaño\n"
"O cualquier gguf de hf con hf.co/username/model"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr "Actualizar Ollama"

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "Clave de API (requerido)"

#: src/handlers/llm/gemini_handler.py:94
msgid "API key got from ai.google.dev"
msgstr "Clave de API obtenida de ai.google.dev"

#: src/handlers/llm/gemini_handler.py:98
msgid "AI Model to use"
msgstr "Modelo de IA a usar"

#: src/handlers/llm/gemini_handler.py:103
msgid "Enable System Prompt"
msgstr "Habilitar prompt del sistema"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr ""
"Algunos modelos no soportan el prompt del sistema (o instrucciones del "
"desarrollador), desactívalo si obtienes errores al respecto"

#: src/handlers/llm/gemini_handler.py:107
msgid "Inject system prompt"
msgstr "Inyectar prompt del sistema"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr ""
"Incluso si el modelo no soporta los prompts del sistema, pon los prompts "
"encima del mensaje del usuario"

#: src/handlers/llm/gemini_handler.py:109
msgid "Thinking Settings"
msgstr "Configuración de pensamiento"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "Configuración sobre los modelos de pensamiento"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr "Mostrar pensamiento, desactívalo si tu modelo no lo soporta"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "Habilitar presupuesto de pensamiento"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "Si habilitar el presupuesto de pensamiento"

#: src/handlers/llm/gemini_handler.py:112
msgid "Thinking Budget"
msgstr "Presupuesto de pensamiento"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "Cuánto tiempo dedicar a pensar"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "Salida de imagen"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr "Habilitar salida de imagen, solo soportado por gemini-2.0-flash-exp"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "Habilitar configuración de seguridad"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr ""
"Habilitar la configuración de seguridad de Google para evitar generar "
"contenido dañino"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
msgid "Advanced Parameters"
msgstr "Parámetros avanzados"

#: src/handlers/llm/gemini_handler.py:135
msgid "Enable advanced parameters"
msgstr "Habilitar parámetros avanzados"

#: src/handlers/llm/openai_handler.py:81
#, fuzzy
msgid "Include parameters like Top-P, Temperature, etc."
msgstr "Incluir parámetros como Max Tokens, Top-P, Temperatura, etc."

#: src/handlers/llm/openai_handler.py:84
msgid "Name of the LLM Model to use"
msgstr "Nombre del modelo LLM a usar"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "Top-P"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr ""
"Una alternativa al muestreo con temperatura, llamado muestreo de núcleo"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "Temperatura"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr ""
"Qué temperatura de muestreo usar. Valores más altos harán que la salida sea "
"más aleatoria"

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "Penalización de frecuencia"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr ""
"Número entre -2.0 y 2.0. Los valores positivos disminuyen la probabilidad de "
"que el modelo repita la misma línea palabra por palabra"

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "Penalización de presencia"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr ""
"Número entre -2.0 y 2.0. Los valores positivos disminuyen la probabilidad de "
"que el modelo hable de nuevos temas"

#: src/handlers/llm/openai_handler.py:108
#, fuzzy
msgid "Custom Options"
msgstr "Prompt personalizado"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr ""

#: src/handlers/llm/openrouter_handler.py:14
msgid "Provider Sorting"
msgstr "Ordenación de proveedores"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "Elija proveedores según el precio/rendimiento o la latencia"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "Precio"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "Rendimiento"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "Latencia"

#: src/handlers/llm/openrouter_handler.py:15
msgid "Providers Order"
msgstr "Orden de proveedores"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr ""
"Añada el orden de los proveedores a usar, nombres separados por comas.\n"
"Deje en blanco para no especificar"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "Permitir alternativas"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "Permitir alternativas a otros proveedores"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "Indexar sus documentos"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"Indexa todos los documentos de tu carpeta de documentos. Debes ejecutar esta "
"operación cada vez que edites/crees un documento, cambies el analizador de "
"documentos o cambies el modelo de embedding"

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "Comando a ejecutar"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "{0} será reemplazado por la ruta completa del modelo"

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr ""
"Clave de API para Google SR, escriba 'default' para usar la predeterminada"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "Idioma"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "El idioma del texto a reconocer en IETF"

#: src/handlers/stt/groqsr_handler.py:14
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr ""
"Clave de API para Groq SR, escriba 'default' para usar la predeterminada"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "Modelo Groq"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "Nombre del modelo Groq"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Especifica el idioma para la transcripción. Usa códigos de idioma ISO 639-1 "
"(ej. \"es\" para español, \"fr\" para francés, etc.)."

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "Punto de conexión para solicitudes de OpenAI"

#: src/handlers/stt/openaisr_handler.py:18
msgid "API Key for OpenAI"
msgstr "Clave de API para OpenAI"

#: src/handlers/stt/openaisr_handler.py:25
msgid "Whisper Model"
msgstr "Modelo Whisper"

#: src/handlers/stt/openaisr_handler.py:26
msgid "Name of the OpenAI model"
msgstr "Nombre del modelo de OpenAI"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Opcional: Especifica el idioma para la transcripción. Usa códigos de idioma "
"ISO 639-1 (ej. \"es\" para español, \"fr\" para francés, etc.)."

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "Ruta del modelo"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "Ruta absoluta al modelo VOSK (descomprimido)"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
msgid "Name of the Whisper model"
msgstr "Nombre del modelo Whisper"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "Token de acceso al servidor para wit.ai"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "No se pudo entender el audio"

#: src/handlers/stt/whispercpp_handler.py:47
#, fuzzy
msgid "Language of the recognition. For example en, it..."
msgstr "Idioma del reconocimiento."

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Model Library"
msgstr "Biblioteca de modelos"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Manage Whisper models"
msgstr "Gestionar modelos Whisper"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "Advanced Settings"
msgstr "Configuración avanzada"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "More advanced settings"
msgstr "Más configuración avanzada"

#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature to use"
msgstr "Temperatura a usar"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt for the recognition"
msgstr "Prompt para el reconocimiento"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr "Prompt a usar para el reconocimiento"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Endpoint"
msgstr "Punto de conexión"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "Punto de conexión personalizado del servicio a usar"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "Voz"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
msgid "The voice to use"
msgstr "La voz a usar"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "Instrucciones"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr ""
"Instrucciones para la generación de voz. Déjelo en blanco para omitir este "
"campo"

#: src/handlers/tts/custom_handler.py:17
#, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr ""
"{0} será reemplazado por la ruta completa del archivo, {1} por el texto"

#: src/handlers/tts/elevenlabs_handler.py:11
msgid "API Key for ElevenLabs"
msgstr "Clave de API para ElevenLabs"

#: src/handlers/tts/elevenlabs_handler.py:19
msgid "Voice ID to use"
msgstr "ID de voz a usar"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "Estabilidad"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "Estabilidad de la voz"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "Aumento de similitud"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "Aumenta la claridad general de la voz y la similitud del hablante"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "Exageración de estilo"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr "Se recomiendan valores altos si el estilo del habla debe ser exagerado"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "Elige la voz preferida"

#: src/handlers/websearch/tavily.py:20
msgid "Token"
msgstr "Token"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "Clave de API de Tavily"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "Resultados máximos"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "Número de resultados a considerar"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "La profundidad de la búsqueda"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"La profundidad de la búsqueda. La búsqueda avanzada está diseñada para "
"recuperar las fuentes y fragmentos de contenido más relevantes para su "
"consulta, mientras que la búsqueda básica proporciona fragmentos de "
"contenido genéricos de cada fuente. Una búsqueda básica cuesta 1 crédito de "
"API, mientras que una búsqueda avanzada cuesta 2 créditos de API."

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "La categoría de la búsqueda"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"La categoría de la búsqueda. Noticias es útil para recuperar actualizaciones "
"en tiempo real, particularmente sobre política, deportes y eventos actuales "
"importantes cubiertos por fuentes de medios de comunicación. General es para "
"búsquedas más amplias y de propósito general que pueden incluir una amplia "
"gama de fuentes."

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "Fragmentos por fuente"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr ""
"El número de fragmentos de contenido a recuperar de cada fuente. La longitud "
"de cada fragmento es de un máximo de 500 caracteres. Disponible solo cuando "
"la profundidad de búsqueda es avanzada."

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "Número de días hacia atrás desde la fecha actual para incluir"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "Disponible solo si el tema es noticias."

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "Incluir respuesta"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"Incluye una respuesta generada por el LLM a la consulta proporcionada. La "
"búsqueda básica devuelve una respuesta rápida. La avanzada devuelve una "
"respuesta más detallada."

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "Incluir contenido sin procesar"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr ""
"Incluir el contenido HTML limpio y analizado de cada resultado de búsqueda."

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "Incluir imágenes"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr ""
"Realizar una búsqueda de imágenes e incluir los resultados en la respuesta."

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "Incluir descripciones de imágenes"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr ""
"Cuando la opción Incluir imágenes está habilitada, también añade un texto "
"descriptivo para cada imagen."

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "Incluir dominios"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr ""
"Una lista de dominios a incluir específicamente en los resultados de "
"búsqueda."

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "Excluir dominios"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr ""
"Una lista de dominios a excluir específicamente de los resultados de "
"búsqueda."

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "Región"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "Región para los resultados de búsqueda"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "Configuración"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "Nombre del perfil"

#: src/ui/profile.py:58
msgid "Copied Settings"
msgstr "Configuración copiada"

#: src/ui/profile.py:58
msgid "Settings that will be copied to the new profile"
msgstr "Configuración que se copiará al nuevo perfil"

#: src/ui/profile.py:70
msgid "Create Profile"
msgstr "Crear perfil"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "Importar perfil"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
msgid "Edit Profile"
msgstr "Editar perfil"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "Exportar perfil"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "Exportar contraseñas"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "También exportar campos tipo contraseña"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "Exportar foto de perfil"

#: src/ui/profile.py:89
msgid "Also export the profile picture"
msgstr "También exportar la imagen de perfil"

#: src/ui/profile.py:109 src/ui/explorer.py:692
msgid "Create"
msgstr "Crear"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "Aplicar"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr "La configuración del perfil actual se copiará en el nuevo"

#: src/ui/profile.py:122 src/ui/profile.py:128
msgid "Newelle Profiles"
msgstr "Perfiles de Newelle"

#: src/ui/profile.py:123
msgid "Export"
msgstr "Exportar"

#: src/ui/profile.py:129
msgid "Import"
msgstr "Importar"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "Establecer imagen de perfil"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "Edición de hilos"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "No hay hilos en ejecución"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "Número de hilo: "

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "Seleccionar perfil"

#: src/ui/widgets/profilerow.py:53
msgid "Delete Profile"
msgstr "Eliminar perfil"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "Pensamientos"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "Expandir para ver detalles"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "Pensando..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr "El LLM está pensando... Expanda para ver el proceso de pensamiento"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "No se registró ningún proceso de pensamiento"

#: src/ui/widgets/tipscarousel.py:41
msgid "Newelle Tips"
msgstr "Consejos de Newelle"

#: src/ui/explorer.py:192
msgid "Folder is Empty"
msgstr "La carpeta está vacía"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr "Archivo no encontrado"

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "Abrir en nueva pestaña"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "Abrir en editor integrado"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "Abrir en el gestor de archivos"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
msgid "Rename"
msgstr "Renombrar"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "Eliminar"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "Copiar ruta completa"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
msgid "Failed to open file manager"
msgstr "Error al abrir el gestor de archivos"

#: src/ui/explorer.py:436
msgid "New name:"
msgstr "Nuevo nombre:"

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "Cancelar"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "Renombrado con éxito"

#: src/ui/explorer.py:476
#, python-brace-format
msgid "Failed to rename: {}"
msgstr "Fallo al renombrar: {}"

#: src/ui/explorer.py:497
msgid "Delete File?"
msgstr "¿Eliminar archivo?"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "¿Estás seguro de que quieres eliminar \"{}\"?"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "Eliminado con éxito"

#: src/ui/explorer.py:525
#, python-brace-format
msgid "Failed to delete: {}"
msgstr "Fallo al eliminar: {}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "Ruta copiada al portapapeles"

#: src/ui/explorer.py:542
msgid "Failed to copy path"
msgstr "Fallo al copiar ruta"

#: src/ui/explorer.py:580
msgid "Create new folder"
msgstr "Crear nueva carpeta"

#: src/ui/explorer.py:583
msgid "Create new file"
msgstr "Crear nuevo archivo"

#: src/ui/explorer.py:586
msgid "Open Terminal Here"
msgstr "Abrir Terminal Aquí"

#: src/ui/explorer.py:640
msgid "Create New Folder"
msgstr "Crear nueva carpeta"

#: src/ui/explorer.py:640
msgid "Folder name:"
msgstr "Nombre de la carpeta:"

#: src/ui/explorer.py:644
msgid "Create New File"
msgstr "Crear nuevo archivo"

#: src/ui/explorer.py:644
msgid "File name:"
msgstr "Nombre del archivo:"

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "Carpeta creada con éxito"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "Archivo creado con éxito"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "Ya existe un archivo o carpeta con ese nombre"

#: src/ui/explorer.py:728
msgid "folder"
msgstr "carpeta"

#: src/ui/explorer.py:728
msgid "file"
msgstr "archivo"

#: src/ui/explorer.py:730
#, python-brace-format
msgid "Failed to create {}: {}"
msgstr "Fallo al crear {}: {}"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "Ayuda"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "Atajos de teclado"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "Recargar chat"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "Recargar carpeta"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "Nueva pestaña"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "Pegar imagen"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "Enfocar cuadro de mensaje"

#: src/ui/shortcuts.py:18
msgid "Start/stop recording"
msgstr "Iniciar/detener grabación"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "Guardar"

#: src/ui/shortcuts.py:20
msgid "Stop TTS"
msgstr "Detener TTS"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr "Acercar"

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr "Alejar"

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr "Monitor de salida del programa"

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr "Limpiar salida"

#: src/ui/stdout_monitor.py:61
msgid "Start/Stop monitoring"
msgstr "Iniciar/Detener monitoreo"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr "Monitoreo: Activo"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr "Monitoreo: Detenido"

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr "Líneas: {}"

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr "Líneas: 0"

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "Extensiones"

#: src/ui/extension.py:50
msgid "Installed Extensions"
msgstr "Extensiones instaladas"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "Guía de usuario de extensiones"

#: src/ui/extension.py:89
msgid "Download new Extensions"
msgstr "Descargar nuevas extensiones"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "Instalar extensión desde archivo..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "Newelle"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "El chat está abierto en una mini ventana"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "Bienvenido a Newelle"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "Tu asistente virtual definitivo."

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "Página de Github"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "Elige tu modelo de lenguaje de IA favorito"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"¡Newelle puede usarse con múltiples modelos y proveedores!\n"
"<b>Nota: Se recomienda encarecidamente leer la página de la Guía de LLM</b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "Guía de LLM"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "Chatea con tus documentos"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr ""
"¡Newelle puede recuperar información relevante de los documentos que envíes "
"en el chat o de tus propios archivos! La información relevante para tu "
"consulta se enviará al LLM."

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "Virtualización de comandos"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"Newelle se puede usar para ejecutar comandos en su sistema, ¡pero preste "
"atención a lo que ejecuta! <b>El LLM no está bajo nuestro control, ¡así que "
"podría generar código malicioso!</b>\n"
"Por defecto, sus comandos serán <b>virtualizados en el entorno Flatpak</b>, "
"¡pero preste atención!"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "¡Puedes ampliar las funcionalidades de Newelle usando extensiones!"

#: src/ui/presentation.py:136
msgid "Download extensions"
msgstr "Descargar extensiones"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "Error de permiso"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr ""
"Newelle no tiene suficientes permisos para ejecutar comandos en su sistema."

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "Empezar a usar la aplicación"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "Empezar a chatear"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "General"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "LLM"

#: src/ui/settings.py:49 src/constants.py:528
msgid "Prompts"
msgstr "Prompts"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "Conocimiento"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "Modelo de Lenguaje"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "Otros LLM"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "Otros proveedores de LLM disponibles"

#: src/ui/settings.py:73
msgid "Advanced LLM Settings"
msgstr "Configuración avanzada de LLM"

#: src/ui/settings.py:77
msgid "Secondary Language Model"
msgstr "Modelo de lenguaje secundario"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr ""
"Modelo utilizado para tareas secundarias, como ofertas, nombres de chat y "
"generación de memoria"

#: src/ui/settings.py:94
msgid "Embedding Model"
msgstr "Modelo de incrustación (Embedding)"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"El embedding se usa para transformar texto en vectores. Usado por la memoria "
"a largo plazo y RAG. Cambiarlo podría requerir que reindices documentos o "
"reinicies la memoria."

#: src/ui/settings.py:105 src/window.py:647
msgid "Long Term Memory"
msgstr "Memoria a Largo Plazo"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "Mantener memoria de conversaciones antiguas"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "Búsqueda Web"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "Buscar información en la Web"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "Programa de Texto a Voz"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "Elige qué texto a voz usar"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "Motor de Voz a Texto"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "Elige qué motor de reconocimiento de voz quieres"

#: src/ui/settings.py:150
msgid "Automatic Speech To Text"
msgstr "Voz a Texto Automática"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr "Reiniciar automáticamente la voz a texto al final de un texto/TTS"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "Control de prompts"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "Interfaz"

#: src/ui/settings.py:162
msgid "Interface Size"
msgstr "Tamaño de la interfaz"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "Ajustar el tamaño de la interfaz"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "Esquema de color del editor"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "Cambiar el esquema de color del editor y los bloques de código"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "Archivos ocultos"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "Mostrar archivos ocultos"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "Enviar con ENTER"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"Si está habilitado, los mensajes se enviarán con ENTER, para ir a una nueva "
"línea use CTRL+ENTER. Si está deshabilitado, los mensajes se enviarán con "
"SHIFT+ENTER, y la nueva línea con ENTER"

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "Eliminar pensamientos del historial"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr ""
"No enviar bloques de pensamiento antiguos para modelos de razonamiento a fin "
"de reducir el uso de tokens"

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "Mostrar LaTeX"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "Mostrar fórmulas LaTeX en el chat"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "Invertir orden del chat"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr ""
"Mostrar los chats más recientes en la parte superior de la lista de chats "
"(cambiar de chat para aplicar)"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "Generar nombres de chat automáticamente"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr ""
"Generar nombres de chat automáticamente después de los dos primeros mensajes"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "Número de ofertas"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "Número de sugerencias de mensajes para enviar al chat "

#: src/ui/settings.py:224
msgid "Username"
msgstr "Nombre de usuario"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr ""
"Cambia la etiqueta que aparece antes de tu mensaje\n"
"Esta información no se envía al LLM por defecto\n"
"Puedes añadirla a un prompt usando la variable {USER}"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "Control de red neuronal"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "Ejecutar comandos en una máquina virtual"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "Terminal externo"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr "Elige el terminal externo donde ejecutar los comandos de consola"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "Memoria del programa"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "Cuánto tiempo el programa recuerda el chat "

#: src/ui/settings.py:266
msgid "Developer"
msgstr "Desarrollador"

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr ""
"Monitoriza la salida del programa en tiempo real, útil para depurar y ver el "
"progreso de las descargas"

#: src/ui/settings.py:270
msgid "Open"
msgstr "Abrir"

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr "Eliminar ruta pip"

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr "Eliminar las dependencias adicionales instaladas"

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr ""

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr ""

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "Ejecutar comandos automáticamente"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "Los comandos que el bot escribirá se ejecutarán automáticamente"

#: src/ui/settings.py:313
msgid "Max number of commands"
msgstr "Número máximo de comandos"

#: src/ui/settings.py:313
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr ""
"Número máximo de comandos que el bot escribirá después de una sola solicitud "
"del usuario"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "Navegador"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "Configuración del navegador"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "Usar navegador externo"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr "Usar un navegador externo para abrir enlaces en lugar del integrado"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "Persistir sesión del navegador"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr ""
"Persistir la sesión del navegador entre reinicios. Desactivar esto requiere "
"reiniciar el programa"

#: src/ui/settings.py:361
msgid "Delete browser data"
msgstr "Eliminar datos del navegador"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "Eliminar sesión y datos del navegador"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "Página inicial del navegador"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "La página donde se iniciará el navegador"

#: src/ui/settings.py:375
msgid "Search string"
msgstr "Cadena de búsqueda"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr ""
"La cadena de búsqueda utilizada en el navegador, %s se reemplaza con la "
"consulta"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "Fuentes de documentos (RAG)"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "Incluir contenido de sus documentos en las respuestas"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "Analizador de documentos"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr ""
"El analizador de documentos utiliza múltiples técnicas para extraer "
"información relevante sobre sus documentos"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "Leer documentos si no es compatible"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr ""
"Si el LLM no admite la lectura de documentos, la información relevante sobre "
"los documentos enviados en el chat se proporcionará al LLM utilizando su "
"Analizador de Documentos."

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "Tokens máximos para RAG"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"La cantidad máxima de tokens a utilizar para RAG. Si los documentos no "
"superan este recuento de tokens,\n"
"vuelca todos ellos en el contexto"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "Carpeta de documentos"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr ""
"Coloca los documentos que deseas consultar en tu carpeta de documentos. El "
"analizador de documentos encontrará información relevante en ellos si esta "
"opción está habilitada"

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr "Ponga todos los documentos que desea indexar en esta carpeta"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "Umbral de silencio"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr ""
"Umbral de silencio en segundos, porcentaje del volumen a considerar silencio"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "Tiempo de silencio"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr ""
"Tiempo de silencio en segundos antes de que la grabación se detenga "
"automáticamente"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "Permisos insuficientes"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr ""
"Newelle no tiene suficientes permisos para ejecutar comandos en su sistema, "
"ejecute el siguiente comando"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "Entendido"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr "Ruta de pip eliminada"

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr ""
"La ruta de pip ha sido eliminada, ahora puede reinstalar las dependencias. "
"Esta operación requiere reiniciar la aplicación."

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "API de demostración de Newelle"

#: src/constants.py:26
msgid "GPT4Free"
msgstr "GPT4Free"

#: src/constants.py:34
msgid "Local Model"
msgstr "Modelo local"

#: src/constants.py:35
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr ""
"SIN SOPORTE DE GPU, USE OLLAMA EN SU LUGAR. Ejecute un modelo LLM "
"localmente, más privacidad pero más lento"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "Instancia Ollama"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "Ejecuta fácilmente múltiples modelos LLM en tu propio hardware"

#: src/constants.py:47
msgid "Groq"
msgstr "Groq"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "API de Google Gemini"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "API de OpenAI"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr ""
"API de OpenAI. Puntos de conexión personalizados compatibles. Úselo para "
"proveedores personalizados"

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "Anthropic Claude"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr ""
"APIs oficiales para los modelos de Anthropic Claude, con soporte de imagen y "
"archivo, requiere una clave de API"

#: src/constants.py:73
msgid "Mistral"
msgstr "Mistral"

#: src/constants.py:74
msgid "Mistral API"
msgstr "API de Mistral"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "API de Openrouter.ai, soporta muchos modelos"

#: src/constants.py:87
msgid "Deepseek"
msgstr "Deepseek"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "API de Deepseek, los modelos de código abierto más potentes"

#: src/constants.py:94 src/constants.py:203
msgid "Custom Command"
msgstr "Comando personalizado"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "Usar la salida de un comando personalizado"

#: src/constants.py:104
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr ""
"Funciona sin conexión. Implementación optimizada de Whisper escrita en C++"

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "Funciona sin conexión. Solo se admite el inglés"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "Reconocimiento de voz de Google"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "Reconocimiento de voz de Google en línea"

#: src/constants.py:124
msgid "Groq Speech Recognition"
msgstr "Reconocimiento de voz Groq"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit AI"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr ""
"API gratuita de reconocimiento de voz de wit.ai (idioma elegido en el sitio "
"web)"

#: src/constants.py:137
msgid "Vosk API"
msgstr "API de Vosk"

#: src/constants.py:138
msgid "Works Offline"
msgstr "Funciona sin conexión"

#: src/constants.py:144
msgid "Whisper API"
msgstr "API de Whisper"

#: src/constants.py:145
msgid "Uses OpenAI Whisper API"
msgstr "Utiliza la API de OpenAI Whisper"

#: src/constants.py:151
msgid "Custom command"
msgstr "Comando personalizado"

#: src/constants.py:152
msgid "Runs a custom command"
msgstr "Ejecuta un comando personalizado"

#: src/constants.py:161
msgid "Google TTS"
msgstr "TTS de Google"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "Texto a voz de Google"

#: src/constants.py:167
msgid "Kokoro TTS"
msgstr "Kokoro TTS"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr ""
"Motor TTS de código abierto ligero y rápido. ~3 GB de dependencias, modelo "
"de 400 MB"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "ElevenLabs TTS"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "TTS de sonido natural"

#: src/constants.py:179 src/constants.py:180
msgid "OpenAI TTS"
msgstr "OpenAI TTS"

#: src/constants.py:185
msgid "Groq TTS"
msgstr "Groq TTS"

#: src/constants.py:186
msgid "Groq TTS API"
msgstr "API de Groq TTS"

#: src/constants.py:191 src/constants.py:192
msgid "Custom OpenAI TTS"
msgstr "TTS de OpenAI personalizado"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "Espeak TTS"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "TTS sin conexión"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr ""
"Usa un comando personalizado como TTS, {0} será reemplazado por el texto"

#: src/constants.py:212
msgid "WordLlama"
msgstr "WordLlama"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr ""
"Modelo de incrustación local ligero basado en llama. Funciona sin conexión, "
"uso de recursos muy bajo"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "Embedding de Ollama"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr ""
"Utiliza los modelos de Ollama para Embedding. Funciona sin conexión, uso de "
"recursos muy bajo"

#: src/constants.py:231
msgid "Use Google Gemini API to get embeddings"
msgstr "Usar la API de Google Gemini para obtener embeddings"

#: src/constants.py:239
msgid "User Summary"
msgstr "Resumen de usuario"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "Generar un resumen de la conversación del usuario"

#: src/constants.py:245
msgid "Memoripy"
msgstr "Memoripy"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"Extrae mensajes de conversaciones anteriores utilizando recuperación de "
"memoria contextual, decaimiento de memoria, extracción de conceptos y otras "
"técnicas avanzadas. Realiza 1 llamada llm por mensaje."

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "Resumen de usuario + Memoripy"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "Usar ambas tecnologías para la memoria a largo plazo"

#: src/constants.py:260
msgid "Document reader"
msgstr "Lector de documentos"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr ""
"Enfoque RAG clásico: divide los documentos en fragmentos y los incrusta, "
"luego los compara con la consulta y devuelve los documentos más relevantes"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr "SearXNG - Motor de búsqueda privado y autoalojable"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "Búsqueda de DuckDuckGo"

#: src/constants.py:281
msgid "Tavily"
msgstr "Tavily"

#: src/constants.py:282
msgid "Tavily search"
msgstr "Búsqueda de Tavily"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "Asistente útil"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr ""
"Prompt de propósito general para mejorar las respuestas del LLM y dar más "
"contexto"

#: src/constants.py:384
msgid "Console access"
msgstr "Acceso a la consola"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "¿Puede el programa ejecutar comandos de terminal en la computadora?"

#: src/constants.py:392
msgid "Current directory"
msgstr "Directorio actual"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "¿Cuál es el directorio actual?"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "Permitir al LLM buscar en internet"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "Funcionalidad básica"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "Mostrar tablas y código (*puede funcionar sin ello)"

#: src/constants.py:419
msgid "Graphs access"
msgstr "Acceso a gráficos"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "¿Puede el programa mostrar gráficos?"

#: src/constants.py:428
msgid "Show image"
msgstr "Mostrar imagen"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "Mostrar imagen en el chat"

#: src/constants.py:437
msgid "Custom Prompt"
msgstr "Prompt personalizado"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "Añade tu propio prompt personalizado"

#: src/constants.py:480
msgid "LLM and Secondary LLM settings"
msgstr "Configuración de LLM y LLM secundario"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "TTS"

#: src/constants.py:485
msgid "Text to Speech settings"
msgstr "Configuración de Texto a Voz"

#: src/constants.py:488
msgid "STT"
msgstr "STT"

#: src/constants.py:490
msgid "Speech to Text settings"
msgstr "Configuración de Voz a Texto"

#: src/constants.py:493
msgid "Embedding"
msgstr "Embedding"

#: src/constants.py:495
msgid "Embedding settings"
msgstr "Configuración de Embedding"

#: src/constants.py:498
msgid "Memory"
msgstr "Memoria"

#: src/constants.py:500
msgid "Memory settings"
msgstr "Configuración de la memoria"

#: src/constants.py:503
msgid "Websearch"
msgstr "Búsqueda web"

#: src/constants.py:505
msgid "Websearch settings"
msgstr "Configuración de búsqueda web"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
msgid "Document analyzer settings"
msgstr "Configuración del analizador de documentos"

#: src/constants.py:515
msgid "Extensions settings"
msgstr "Configuración de extensiones"

#: src/constants.py:518
msgid "Inteface"
msgstr "Interfaz"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr "Configuración de interfaz, archivos ocultos, orden inverso, zoom..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr ""
"Configuración general, virtualización, ofertas, duración de la memoria, "
"generación automática de nombres de chat, carpeta actual..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr ""
"Configuración de prompts, prompt extra personalizado, prompts "
"personalizados..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "Chat "

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "Los hilos de terminal siguen ejecutándose en segundo plano"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "Cuando cierre la ventana, se terminarán automáticamente"

#: src/main.py:210
msgid "Close"
msgstr "Cerrar"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "El chat se ha reiniciado"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "La carpeta ha sido reiniciada"

#: src/main.py:254
msgid "Chat is created"
msgstr "El chat ha sido creado"

#: src/window.py:120
msgid "Keyboard shorcuts"
msgstr "Atajos de teclado"

#: src/window.py:121
msgid "About"
msgstr "Acerca de"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "Chat"

#: src/window.py:170
msgid "History"
msgstr "Historial"

#: src/window.py:191
msgid "Create a chat"
msgstr "Crear un chat"

#: src/window.py:196
msgid "Chats"
msgstr "Chats"

#: src/window.py:267
msgid " Stop"
msgstr " Detener"

#: src/window.py:282
msgid " Clear"
msgstr " Limpiar"

#: src/window.py:297
msgid " Continue"
msgstr " Continuar"

#: src/window.py:310
msgid " Regenerate"
msgstr " Regenerar"

#: src/window.py:376
msgid "Send a message..."
msgstr "Enviar un mensaje..."

#: src/window.py:467
msgid "Explorer Tab"
msgstr "Pestaña del explorador"

#: src/window.py:468
msgid "Terminal Tab"
msgstr "Pestaña de terminal"

#: src/window.py:469
msgid "Browser Tab"
msgstr "Pestaña del navegador"

#: src/window.py:589
msgid "Ask about a website"
msgstr "Preguntar sobre un sitio web"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr ""
"Escribe #https://website.com en el chat para pedir información sobre un "
"sitio web"

#: src/window.py:590
msgid "Check out our Extensions!"
msgstr "¡Echa un vistazo a nuestras Extensiones!"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "Tenemos muchas extensiones para diferentes cosas. ¡Compruébalo!"

#: src/window.py:591
msgid "Chat with documents!"
msgstr "¡Chatea con documentos!"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr ""
"¡Añada sus documentos a su carpeta de documentos y chatee utilizando la "
"información contenida en ellos!"

#: src/window.py:592
msgid "Surf the web!"
msgstr "¡Navega por la web!"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr ""
"Habilita la búsqueda web para permitir que el LLM navegue por la web y "
"proporcione respuestas actualizadas"

#: src/window.py:593
msgid "Mini Window"
msgstr "Mini ventana"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "Haz preguntas al instante usando el modo mini ventana"

#: src/window.py:594
msgid "Text to Speech"
msgstr "Texto a voz"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr "¡Newelle soporta texto a voz! Habilítalo en la configuración"

#: src/window.py:595
msgid "Keyboard Shortcuts"
msgstr "Atajos de teclado"

#: src/window.py:595
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "Controla Newelle usando atajos de teclado"

#: src/window.py:596
msgid "Prompt Control"
msgstr "Control de prompts"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr ""
"Newelle le brinda un control del 100% sobre los prompts. Ajuste sus prompts "
"para su uso."

#: src/window.py:597
msgid "Thread Editing"
msgstr "Edición de hilos"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "Comprueba los programas y procesos que ejecutas desde Newelle"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr "Prompts programables"

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr ""
"Puedes añadir prompts dinámicos a Newelle, con condiciones y probabilidades"

#: src/window.py:605
msgid "New Chat"
msgstr "Nuevo chat"

#: src/window.py:623
msgid "Provider Errror"
msgstr "Error del proveedor"

#: src/window.py:646
msgid "Local Documents"
msgstr "Documentos Locales"

#: src/window.py:650
msgid "Web search"
msgstr "Búsqueda web"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "Este proveedor no tiene una lista de modelos"

#: src/window.py:901
msgid " Models"
msgstr " Modelos"

#: src/window.py:904
msgid "Search Models..."
msgstr "Buscar modelos..."

#: src/window.py:1132
msgid "Create new profile"
msgstr "Crear nuevo perfil"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "No se pudo reconocer su voz"

#: src/window.py:1303
msgid "Images"
msgstr "Imágenes"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "Archivos compatibles con LLM"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "Archivos compatibles con RAG"

#: src/window.py:1333
msgid "Supported Files"
msgstr "Archivos compatibles"

#: src/window.py:1337
msgid "All Files"
msgstr "Todos los archivos"

#: src/window.py:1343
msgid "Attach file"
msgstr "Adjuntar archivo"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "El archivo no se puede enviar hasta que el programa termine"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "El archivo no es reconocido"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "Ya no puedes continuar el mensaje."

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "Ya no puedes regenerar el mensaje."

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "El chat ha sido borrado"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "El mensaje fue cancelado y eliminado del historial"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr "El mensaje no se puede enviar hasta que el programa termine"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr "No puedes editar un mensaje mientras el programa está en ejecución."

#: src/window.py:3080
msgid "Prompt content"
msgstr "Contenido del prompt"

#: src/window.py:3339
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"La red neuronal tiene acceso a su computadora y a cualquier dato en este "
"chat, y puede ejecutar comandos. Tenga cuidado, no somos responsables de la "
"red neuronal. No comparta información sensible."

#: src/window.py:3368
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"La red neuronal tiene acceso a cualquier dato en este chat, tenga cuidado, "
"no somos responsables de la red neuronal. No comparta información sensible."

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "Ruta de carpeta incorrecta"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "El hilo no ha sido completado, número de hilo: "

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "No se pudo abrir la carpeta"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "El chat está vacío"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr ""
"Entrena a Newelle para hacer más con extensiones personalizadas y nuevos "
"módulos de IA, dándole a tu chatbot infinitas posibilidades."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "Chatbot de IA"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "Selección rápida de perfil"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
msgid "Message Editing"
msgstr "Edición de mensajes"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "Más de 10 proveedores de IA estándar"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "Corrección de errores"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
#, fuzzy
msgid "Minor bug fixes"
msgstr "Corrección de errores"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "Pequeñas mejoras"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "Mejorar el rendimiento de lectura y carga de documentos locales"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "Añadir opción para enviar con CTRL+Enter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "Mejorar bloques de código"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
msgid "Fix Kokoro TTS"
msgstr "Arreglar Kokoro TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "Eliminar emojis de TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "Configurar claves API como campos de contraseña"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "Añadir soporte de pensamiento para Gemini"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "Traducciones actualizadas"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "Nuevas características añadidas"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr "Lectura de sitios web y búsqueda web con SearXNG, DuckDuckGo y Tavily"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "Renderizado de LaTeX y gestión de documentos mejorados"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "Nuevo Widget de Pensamiento y manejador de OpenRouter"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "Soporte de visión para Llama4 en Groq"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "Nuevas traducciones (chino tradicional, bengalí, hindi)"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "¡Muchos errores corregidos, algunas características añadidas!"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "Soporte para nuevas funciones y corrección de errores"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "Se añadieron muchas nuevas funciones y correcciones de errores"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "Se añadieron nuevas características y correcciones de errores"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr ""
"Se actualizó la biblioteca g4f con versionado, se agregaron guías de "
"usuario, se mejoró la navegación de extensiones y se optimizó el manejo de "
"modelos."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"Se han implementado correcciones de errores y nuevas funciones. Hemos "
"modificado la arquitectura de las extensiones, añadido nuevos modelos e "
"introducido soporte de visión, junto con más capacidades."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "Versión estable"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
msgid "Added extension"
msgstr "Extensión añadida"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
msgid "Blacklist of commands"
msgstr "Lista negra de comandos"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "Localización"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "Rediseño"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "Qwersyk"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "Newelle: Tu chatbot avanzado"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "ia;asistente;chat;chatgpt;gpt;llm;ollama;"

#~ msgid "max Tokens"
#~ msgstr "Tokens máximos"

#~ msgid "Max tokens of the generated text"
#~ msgstr "Tokens máximos del texto generado"
