msgid ""
msgstr ""
"Project-Id-Version: 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-07-03 09:00+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Japanese <<EMAIL>>\n"
"Language: ja\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "APIエンドポイント"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr "APIのベースURLです。推論APIを使用するために変更してください"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid "Automatically Serve"
msgstr "自動的に提供"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"必要に応じて、ollamaが実行されていない場合にバックグラウンドで自動的に起動し"
"ます。killall ollamaで終了できます"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
msgid "Custom Model"
msgstr "カスタムモデル"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
msgid "Use a custom model"
msgstr "カスタムモデルを使用する"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
msgid "Ollama Model"
msgstr "Ollamaモデル"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
msgid "Name of the Ollama Model"
msgstr "Ollamaモデルの名前"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "APIキー"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
msgid "API Key for "
msgstr "APIキー："

#: src/handlers/embeddings/openai_handler.py:38
msgid "API base url, change this to use different APIs"
msgstr "APIのベースURLです。異なるAPIを使用するために変更してください"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
msgid "Use Custom Model"
msgstr "カスタムモデルを使用"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "モデル"

#: src/handlers/embeddings/openai_handler.py:44
msgid "Name of the Embedding Model to use"
msgstr "使用する埋め込みモデルの名前"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
msgid " Model"
msgstr "モデル"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
msgid "The API key to use"
msgstr "使用するAPIキー"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
msgid "The model to use"
msgstr "使用するモデル"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "最大トークン数"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "生成するトークンの最大数"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
msgid "Message Streaming"
msgstr "メッセージストリーミング"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "メッセージ出力を段階的にストリーミングする"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "ボットの出力を取得するために実行するコマンド"

#: src/handlers/llm/custom_handler.py:21
#, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr ""
"ボットの応答を取得するために実行するコマンド。{0}はチャットを含むJSONファイル"
"に、{1}はシステムプロンプトに置き換えられます"

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "ボットの提案を取得するために実行するコマンド"

#: src/handlers/llm/custom_handler.py:22
#, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"チャットの提案を取得するために実行するコマンド。{0}はチャットを含むJSONファイ"
"ルに、{1}は追加のプロンプトに、{2}は生成する提案の数に置き換えられます。提案"
"を文字列として含むJSON配列を返す必要があります"

#: src/handlers/llm/gpt4all_handler.py:67
msgid "RAM Required: "
msgstr "必要なRAM: "

#: src/handlers/llm/gpt4all_handler.py:68
msgid "Parameters: "
msgstr "パラメーター: "

#: src/handlers/llm/gpt4all_handler.py:69
msgid "Size: "
msgstr "サイズ: "

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
msgid "Model to use"
msgstr "使用するモデル"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
msgid "Name of the model to use"
msgstr "使用するモデルの名前"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "Model Manager"
msgstr "モデルマネージャー"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "利用可能なモデルのリスト"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr "G4Fを更新"

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "プライバシーポリシー"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "プライバシーポリシーのウェブサイトを開く"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "思考を有効にする"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr "モデル内での思考を許可します。一部のモデルのみサポートされています"

#: src/handlers/llm/ollama_handler.py:176
msgid "Add custom model"
msgstr "カスタムモデルを追加"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"このリストに任意のモデルを追加するには、name:sizeの形式で記述してください\n"
"hf.co/username/model からggufモデルを追加することもできます"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr "Ollamaを更新"

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "APIキー (必須)"

#: src/handlers/llm/gemini_handler.py:94
msgid "API key got from ai.google.dev"
msgstr "ai.google.devから取得したAPIキー"

#: src/handlers/llm/gemini_handler.py:98
msgid "AI Model to use"
msgstr "使用するAIモデル"

#: src/handlers/llm/gemini_handler.py:103
msgid "Enable System Prompt"
msgstr "システムプロンプトを有効にする"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr ""
"一部のモデルはシステムプロンプト（または開発者向け指示）をサポートしていませ"
"ん。エラーが発生する場合は無効にしてください"

#: src/handlers/llm/gemini_handler.py:107
msgid "Inject system prompt"
msgstr "システムプロンプトを挿入"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr ""
"モデルがシステムプロンプトをサポートしていない場合でも、プロンプトをユーザー"
"メッセージの先頭に配置します"

#: src/handlers/llm/gemini_handler.py:109
msgid "Thinking Settings"
msgstr "思考設定"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "思考モデルに関する設定"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr "思考を表示します。モデルがサポートしていない場合は無効にしてください"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "思考予算を有効にする"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "思考予算を有効にするかどうか"

#: src/handlers/llm/gemini_handler.py:112
msgid "Thinking Budget"
msgstr "思考予算"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "思考に費やす時間"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "画像出力"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr "画像出力を有効にします。gemini-2.0-flash-expのみサポートされています"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "安全設定を有効にする"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr "有害なコンテンツの生成を防ぐため、Googleの安全設定を有効にします"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
msgid "Advanced Parameters"
msgstr "詳細パラメーター"

#: src/handlers/llm/gemini_handler.py:135
msgid "Enable advanced parameters"
msgstr "詳細パラメーターを有効にする"

#: src/handlers/llm/openai_handler.py:81
#, fuzzy
msgid "Include parameters like Top-P, Temperature, etc."
msgstr "最大トークン数、Top-P、温度などのパラメーターを含める"

#: src/handlers/llm/openai_handler.py:84
msgid "Name of the LLM Model to use"
msgstr "使用するLLMモデルの名前"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "Top-P"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr ""
"温度によるサンプリングの代替手段で、ニュークリアスサンプリングと呼ばれます"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "温度"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr "使用するサンプリング温度。高い値にすると出力がよりランダムになります"

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "頻度ペナルティ"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr ""
"-2.0から2.0の間の数値。正の値は、モデルが同じ行をそのまま繰り返す可能性を低く"
"します"

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "存在ペナルティ"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr ""
"-2.0から2.0の間の数値。正の値は、モデルが新しいトピックについて話す可能性を低"
"くします"

#: src/handlers/llm/openai_handler.py:108
#, fuzzy
msgid "Custom Options"
msgstr "カスタムプロンプト"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr ""

#: src/handlers/llm/openrouter_handler.py:14
msgid "Provider Sorting"
msgstr "プロバイダーのソート"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "価格/スループットまたはレイテンシーに基づいてプロバイダーを選択します"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "価格"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "スループット"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "レイテンシー"

#: src/handlers/llm/openrouter_handler.py:15
msgid "Providers Order"
msgstr "プロバイダーの順序"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr ""
"使用するプロバイダーの順序を、コンマで区切って追加します。\n"
"空にすると指定しません"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "フォールバックを許可する"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "他のプロバイダーへのフォールバックを許可する"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "ドキュメントをインデックス化"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"ドキュメントフォルダ内のすべてのドキュメントをインデックス化します。ドキュメ"
"ントの編集/作成、ドキュメントアナライザーの変更、埋め込みモデルの変更を行うた"
"びに、この操作を実行する必要があります"

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "実行するコマンド"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "{0} はモデルのフルパスに置き換えられます"

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr ""
"Google SRのAPIキー。「default」と入力するとデフォルトのものが使用されます"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "言語"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "IETF形式で認識するテキストの言語"

#: src/handlers/stt/groqsr_handler.py:14
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr ""
"Groq SRのAPIキー。「default」と入力するとデフォルトのものが使用されます"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "Groqモデル"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "Groqモデルの名前"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"文字起こしに使用する言語を指定します。ISO 639-1 言語コード（例: 英語の場合は"
"「en」、フランス語の場合は「fr」など）を使用してください。"

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "OpenAIリクエストのエンドポイント"

#: src/handlers/stt/openaisr_handler.py:18
msgid "API Key for OpenAI"
msgstr "OpenAIのAPIキー"

#: src/handlers/stt/openaisr_handler.py:25
msgid "Whisper Model"
msgstr "Whisperモデル"

#: src/handlers/stt/openaisr_handler.py:26
msgid "Name of the OpenAI model"
msgstr "OpenAIモデルの名前"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"オプション：文字起こしに使用する言語を指定します。ISO 639-1 言語コード（例: "
"英語の場合は「en」、フランス語の場合は「fr」など）を使用してください。"

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "モデルパス"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "VOSKモデル（解凍済み）への絶対パス"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
msgid "Name of the Whisper model"
msgstr "Whisperモデルの名前"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "wit.aiのサーバーアクセストークン"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "音声を理解できませんでした"

#: src/handlers/stt/whispercpp_handler.py:47
#, fuzzy
msgid "Language of the recognition. For example en, it..."
msgstr "認識の言語。"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Model Library"
msgstr "モデルライブラリ"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Manage Whisper models"
msgstr "Whisperモデルを管理"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "Advanced Settings"
msgstr "詳細設定"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "More advanced settings"
msgstr "より詳細な設定"

#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature to use"
msgstr "使用する温度"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt for the recognition"
msgstr "認識用のプロンプト"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr "認識に使用するプロンプト"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Endpoint"
msgstr "エンドポイント"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "使用するサービスのカスタムエンドポイント"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "音声"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
msgid "The voice to use"
msgstr "使用する音声"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "指示"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr "音声生成の指示。このフィールドを避けるには空白のままにしてください"

#: src/handlers/tts/custom_handler.py:17
#, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr "{0} はファイルのフルパスに、{1} はテキストに置き換えられます"

#: src/handlers/tts/elevenlabs_handler.py:11
msgid "API Key for ElevenLabs"
msgstr "ElevenLabsのAPIキー"

#: src/handlers/tts/elevenlabs_handler.py:19
msgid "Voice ID to use"
msgstr "使用する音声ID"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "安定性"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "音声の安定性"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "類似性ブースト"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "全体的な音声の明瞭さと話者の類似性を向上させます"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "スタイルの誇張"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr "話すスタイルを誇張する必要がある場合は、高い値をお勧めします"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "好みの音声を選択してください"

#: src/handlers/websearch/tavily.py:20
msgid "Token"
msgstr "トークン"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "Tavily APIキー"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "最大結果数"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "考慮する結果の数"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "検索の深さ"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"検索の深さ。高度な検索は、クエリに最も関連性の高い情報源とコンテンツスニペッ"
"トを取得するために調整されていますが、基本的な検索は各情報源から一般的なコン"
"テンツスニペットを提供します。基本的な検索はAPIクレジット1、高度な検索はAPIク"
"レジット2を消費します。"

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "検索のカテゴリ"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"検索のカテゴリ。ニュースは、特に政治、スポーツ、主要な時事問題など、主流メ"
"ディアで報じられているリアルタイムの情報を取得するのに役立ちます。一般は、幅"
"広い情報源を含む可能性のある、より広範で一般的な目的の検索用です。"

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "ソースごとのチャンク数"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr ""
"各ソースから取得するコンテンツチャンクの数。各チャンクの長さは最大500文字で"
"す。検索深度が高度な場合のみ利用可能です。"

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "現在の日付から何日前にさかのぼって含めるか"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "トピックがニュースの場合のみ利用可能です。"

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "回答を含める"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"提供されたクエリに対するLLM生成の回答を含めます。基本的な検索では簡単な回答が"
"返されます。高度な検索ではより詳細な回答が返されます。"

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "未加工コンテンツを含める"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr "各検索結果のクリーンアップおよび解析されたHTMLコンテンツを含めます。"

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "画像を含める"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr "画像検索を実行し、その結果を応答に含めます。"

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "画像の説明を含める"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr "「画像を含める」が有効な場合、各画像の説明テキストも追加します。"

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "ドメインを含める"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr "検索結果に含める特定のドメインのリスト。"

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "ドメインを除外する"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr "検索結果から除外する特定のドメインのリスト。"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "地域"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "検索結果の地域"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "設定"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "プロファイル名"

#: src/ui/profile.py:58
msgid "Copied Settings"
msgstr "コピーされた設定"

#: src/ui/profile.py:58
msgid "Settings that will be copied to the new profile"
msgstr "新しいプロファイルにコピーされる設定"

#: src/ui/profile.py:70
msgid "Create Profile"
msgstr "プロファイルの作成"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "プロファイルのインポート"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
msgid "Edit Profile"
msgstr "プロファイルの編集"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "プロファイルの書き出し"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "パスワードの書き出し"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "パスワードのようなフィールドも書き出す"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "プロフィールの画像を書き出す"

#: src/ui/profile.py:89
msgid "Also export the profile picture"
msgstr "プロフィールの画像も書き出す"

#: src/ui/profile.py:109 src/ui/explorer.py:692
msgid "Create"
msgstr "作成"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "適用"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr "現在のプロファイルの設定が新しいプロファイルにコピーされます"

#: src/ui/profile.py:122 src/ui/profile.py:128
msgid "Newelle Profiles"
msgstr "Newelleプロファイル"

#: src/ui/profile.py:123
msgid "Export"
msgstr "エクスポート"

#: src/ui/profile.py:129
msgid "Import"
msgstr "インポート"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "プロフィール画像を設定"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "スレッドの編集"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "実行中のスレッドはありません"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "スレッド番号："

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "プロファイルの選択"

#: src/ui/widgets/profilerow.py:53
msgid "Delete Profile"
msgstr "プロファイルの削除"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "思考"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "詳細を表示するには展開してください"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "思考中..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr "LLMが思考しています... 思考プロセスを表示するには展開してください"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "思考プロセスが記録されていません"

#: src/ui/widgets/tipscarousel.py:41
msgid "Newelle Tips"
msgstr "Newelleのヒント"

#: src/ui/explorer.py:192
msgid "Folder is Empty"
msgstr "フォルダは空です"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr "ファイルが見つかりません"

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "新しいタブで開く"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "統合エディタで開く"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "ファイルマネージャーで開く"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
msgid "Rename"
msgstr "名前を変更"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "削除"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "フルパスをコピー"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
msgid "Failed to open file manager"
msgstr "ファイルマネージャーを開けませんでした"

#: src/ui/explorer.py:436
msgid "New name:"
msgstr "新しい名前："

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "キャンセル"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "名前が正常に変更されました"

#: src/ui/explorer.py:476
#, python-brace-format
msgid "Failed to rename: {}"
msgstr "名前の変更に失敗しました: {}"

#: src/ui/explorer.py:497
msgid "Delete File?"
msgstr "ファイルを削除しますか？"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "「{}」を削除してもよろしいですか？"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "正常に削除されました"

#: src/ui/explorer.py:525
#, python-brace-format
msgid "Failed to delete: {}"
msgstr "削除に失敗しました: {}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "パスをクリップボードにコピーしました"

#: src/ui/explorer.py:542
msgid "Failed to copy path"
msgstr "パスのコピーに失敗しました"

#: src/ui/explorer.py:580
msgid "Create new folder"
msgstr "新しいフォルダを作成"

#: src/ui/explorer.py:583
msgid "Create new file"
msgstr "新しいファイルを作成"

#: src/ui/explorer.py:586
msgid "Open Terminal Here"
msgstr "ここでターミナルを開く"

#: src/ui/explorer.py:640
msgid "Create New Folder"
msgstr "新しいフォルダを作成"

#: src/ui/explorer.py:640
msgid "Folder name:"
msgstr "フォルダ名："

#: src/ui/explorer.py:644
msgid "Create New File"
msgstr "新しいファイルを作成"

#: src/ui/explorer.py:644
msgid "File name:"
msgstr "ファイル名："

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "フォルダが正常に作成されました"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "ファイルが正常に作成されました"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "その名前のファイルまたはフォルダは既に存在します"

#: src/ui/explorer.py:728
msgid "folder"
msgstr "フォルダ"

#: src/ui/explorer.py:728
msgid "file"
msgstr "ファイル"

#: src/ui/explorer.py:730
#, python-brace-format
msgid "Failed to create {}: {}"
msgstr "{} の作成に失敗しました: {}"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "ヘルプ"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "ショートカット"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "チャットを再読み込み"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "フォルダを再読み込み"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "新しいタブ"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "画像を貼り付け"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "メッセージボックスにフォーカス"

#: src/ui/shortcuts.py:18
msgid "Start/stop recording"
msgstr "録音の開始/停止"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "保存"

#: src/ui/shortcuts.py:20
msgid "Stop TTS"
msgstr "TTSを停止"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr "拡大"

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr "縮小"

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr "プログラム出力モニター"

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr "出力をクリア"

#: src/ui/stdout_monitor.py:61
msgid "Start/Stop monitoring"
msgstr "監視の開始/停止"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr "監視中：アクティブ"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr "監視中：停止済み"

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr "行数: {}"

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr "行数: 0"

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "拡張機能"

#: src/ui/extension.py:50
msgid "Installed Extensions"
msgstr "インストールされている拡張機能"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "拡張機能のユーザーガイド"

#: src/ui/extension.py:89
msgid "Download new Extensions"
msgstr "新しい拡張機能をダウンロード"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "ファイルから拡張機能をインストール..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "Newelle"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "チャットはミニウィンドウで開かれています"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "Newelleへようこそ"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "究極のバーチャルアシスタント。"

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "Githubページ"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "お気に入りのAI言語モデルを選択してください"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"Newelleは複数のモデルとプロバイダーで使用できます！\n"
"<b>注意：LLMガイドページを読むことを強くお勧めします</b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "LLMガイド"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "ドキュメントとチャット"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr ""
"Newelleは、チャットで送信したドキュメントや自分のファイルから関連情報を取得で"
"きます！クエリに関連する情報はLLMに送信されます。"

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "コマンドの仮想化"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"Newelleはシステム上でコマンドを実行できますが、実行する内容には注意してくださ"
"い！<b>LLMは当社の管理下にないため、悪意のあるコードを生成する可能性がありま"
"す！</b>\n"
"デフォルトでは、コマンドは<b>Flatpak環境で仮想化されます</b>が、注意が必要で"
"す！"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "拡張機能を使ってNewelleの機能を拡張できます！"

#: src/ui/presentation.py:136
msgid "Download extensions"
msgstr "拡張機能をダウンロード"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "権限エラー"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr "Newelleにはシステムでコマンドを実行するのに十分な権限がありません。"

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "アプリの使用を開始する"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "チャットを開始"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "一般"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "LLM"

#: src/ui/settings.py:49 src/constants.py:528
msgid "Prompts"
msgstr "プロンプト"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "知識"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "言語モデル"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "その他のLLM"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "その他の利用可能なLLMプロバイダー"

#: src/ui/settings.py:73
msgid "Advanced LLM Settings"
msgstr "LLM詳細設定"

#: src/ui/settings.py:77
msgid "Secondary Language Model"
msgstr "セカンダリ言語モデル"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr "提案、チャット名、メモリ生成などの二次タスクに使用されるモデル"

#: src/ui/settings.py:94
msgid "Embedding Model"
msgstr "埋め込みモデル"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"埋め込みはテキストをベクトルに変換するために使用されます。長期記憶およびRAGに"
"よって使用されます。これを変更すると、ドキュメントの再インデックス化またはメ"
"モリのリセットが必要になる場合があります。"

#: src/ui/settings.py:105 src/window.py:647
msgid "Long Term Memory"
msgstr "長期記憶"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "以前の会話の記憶を保持する"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "ウェブ検索"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "ウェブで情報を検索する"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "テキスト読み上げプログラム"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "使用するテキスト読み上げを選択してください"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "音声認識エンジン"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "使用したい音声認識エンジンを選択してください"

#: src/ui/settings.py:150
msgid "Automatic Speech To Text"
msgstr "自動音声認識"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr "テキスト/TTSの終了時に自動的に音声認識を再開する"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "プロンプト制御"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "インターフェース"

#: src/ui/settings.py:162
msgid "Interface Size"
msgstr "インターフェースサイズ"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "インターフェースのサイズを調整する"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "エディターのカラースキーム"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "エディターとコードブロックのカラースキームを変更する"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "隠しファイル"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "隠しファイルを表示する"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "ENTERで送信"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"有効にすると、メッセージはENTERで送信され、改行するにはCTRL+ENTERを使用しま"
"す。無効にすると、メッセージはSHIFT+ENTERで送信され、改行はENTERで行われます"

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "思考を履歴から削除"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr "トークン使用量を削減するため、推論モデルの古い思考ブロックは送信しない"

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "LaTeXを表示"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "チャットでLaTeXの数式を表示する"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "チャット順序を逆にする"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr "チャットリストで最も新しいチャットを上部に表示（チャット変更時に適用）"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "チャット名を自動生成"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr "最初の2つのメッセージの後、チャット名を自動的に生成する"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "提案の数"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "チャットに送信するメッセージ提案の数"

#: src/ui/settings.py:224
msgid "Username"
msgstr "ユーザー名"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr ""
"メッセージの前に表示されるラベルを変更します\n"
"この情報はデフォルトではLLMに送信されません\n"
"{USER}変数を使用してプロンプトに追加できます"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "ニューラルネットワーク制御"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "仮想マシンでコマンドを実行する"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "外部ターミナル"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr "コンソールコマンドを実行する外部ターミナルを選択します"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "プログラムメモリ"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "プログラムがチャットを記憶する期間"

#: src/ui/settings.py:266
msgid "Developer"
msgstr "開発者"

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr ""
"プログラムの出力をリアルタイムで監視します。デバッグやダウンロードの進行状況"
"の確認に役立ちます"

#: src/ui/settings.py:270
msgid "Open"
msgstr "開く"

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr "pipパスを削除"

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr "インストールされている追加の依存関係を削除します"

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr ""

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr ""

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "コマンドを自動実行"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "ボットが記述するコマンドは自動的に実行されます"

#: src/ui/settings.py:313
msgid "Max number of commands"
msgstr "最大コマンド数"

#: src/ui/settings.py:313
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr "ボットが1回のユーザーリクエスト後に記述するコマンドの最大数"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "ブラウザ"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "ブラウザの設定"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "外部ブラウザを使用"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr "統合ブラウザではなく、外部ブラウザを使用してリンクを開く"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "ブラウザセッションを保持"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr ""
"再起動後もブラウザセッションを保持します。これをオフにすると、プログラムの再"
"起動が必要です"

#: src/ui/settings.py:361
msgid "Delete browser data"
msgstr "ブラウザデータを削除"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "ブラウザのセッションとデータを削除"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "初期ブラウザページ"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "ブラウザが起動するページ"

#: src/ui/settings.py:375
msgid "Search string"
msgstr "検索文字列"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr "ブラウザで使用される検索文字列。%s はクエリに置き換えられます"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "ドキュメントソース (RAG)"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "応答にドキュメントの内容を含める"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "ドキュメントアナライザー"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr ""
"ドキュメントアナライザーは、複数の技術を使用してドキュメントから関連情報を抽"
"出します"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "サポートされていない場合はドキュメントを読む"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr ""
"LLMがドキュメントの読み取りをサポートしていない場合、チャットで送信されたド"
"キュメントに関する関連情報は、ドキュメントアナライザーを使用してLLMに提供され"
"ます。"

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "RAGの最大トークン数"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"RAGに使用するトークンの最大量。ドキュメントがこのトークン数を超えない場合、\n"
"すべてをコンテキストにダンプします"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "ドキュメントフォルダ"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr ""
"クエリしたいドキュメントをドキュメントフォルダに入れてください。このオプショ"
"ンが有効な場合、ドキュメントアナライザーがそれらの中から関連情報を見つけます"

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr "インデックス化したいすべてのドキュメントをこのフォルダに入れてください"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "無音のしきい値"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr "無音とみなされるボリュームの割合、秒単位での無音のしきい値"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "無音時間"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr "録音が自動的に停止するまでの無音時間（秒）"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "十分な権限がありません"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr ""
"Newelleにはシステムでコマンドを実行するのに十分な権限がありません。以下のコマ"
"ンドを実行してください"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "了解しました"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr "Pipパスを削除しました"

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr ""
"pipパスが削除されました。これで依存関係を再インストールできます。この操作には"
"アプリケーションの再起動が必要です。"

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "NewelleデモAPI"

#: src/constants.py:26
msgid "GPT4Free"
msgstr "GPT4Free"

#: src/constants.py:34
msgid "Local Model"
msgstr "ローカルモデル"

#: src/constants.py:35
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr ""
"GPUはサポートされていません。代わりにOllamaを使用してください。LLMモデルを"
"ローカルで実行します。プライバシーは向上しますが、速度は低下します"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "Ollamaインスタンス"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "自身のハードウェアで複数のLLMモデルを簡単に実行"

#: src/constants.py:47
msgid "Groq"
msgstr "Groq"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "Google Gemini API"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "OpenAI API"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr ""
"OpenAI API。カスタムエンドポイントをサポート。カスタムプロバイダーにはこれを"
"使用してください"

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "Anthropic Claude"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr ""
"Anthropic Claudeモデルの公式API。画像とファイルサポート付きで、APIキーが必要"
"です"

#: src/constants.py:73
msgid "Mistral"
msgstr "Mistral"

#: src/constants.py:74
msgid "Mistral API"
msgstr "Mistral API"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "Openrouter.ai API。多くのモデルをサポート"

#: src/constants.py:87
msgid "Deepseek"
msgstr "Deepseek"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "Deepseek API。最強のオープンソースモデル"

#: src/constants.py:94 src/constants.py:203
msgid "Custom Command"
msgstr "カスタムコマンド"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "カスタムコマンドの出力を利用"

#: src/constants.py:104
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr "オフラインで動作。C++で書かれた最適化されたWhisper実装"

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "オフラインで動作。英語のみサポート"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "Google音声認識"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "Google音声認識オンライン"

#: src/constants.py:124
msgid "Groq Speech Recognition"
msgstr "Groq音声認識"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit AI"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr "wit.ai 音声認識無料API（言語はウェブサイトで選択）"

#: src/constants.py:137
msgid "Vosk API"
msgstr "Vosk API"

#: src/constants.py:138
msgid "Works Offline"
msgstr "オフラインで動作"

#: src/constants.py:144
msgid "Whisper API"
msgstr "Whisper API"

#: src/constants.py:145
msgid "Uses OpenAI Whisper API"
msgstr "OpenAI Whisper APIを使用"

#: src/constants.py:151
msgid "Custom command"
msgstr "カスタムコマンド"

#: src/constants.py:152
msgid "Runs a custom command"
msgstr "カスタムコマンドを実行する"

#: src/constants.py:161
msgid "Google TTS"
msgstr "Google TTS"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "Googleのテキスト読み上げ"

#: src/constants.py:167
msgid "Kokoro TTS"
msgstr "Kokoro TTS"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr "軽量で高速なオープンソースTTSエンジン。~3GBの依存関係、400MBのモデル"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "ElevenLabs TTS"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "自然な音のTTS"

#: src/constants.py:179 src/constants.py:180
msgid "OpenAI TTS"
msgstr "OpenAI TTS"

#: src/constants.py:185
msgid "Groq TTS"
msgstr "Groq TTS"

#: src/constants.py:186
msgid "Groq TTS API"
msgstr "Groq TTS API"

#: src/constants.py:191 src/constants.py:192
msgid "Custom OpenAI TTS"
msgstr "カスタムOpenAI TTS"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "Espeak TTS"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "オフラインTTS"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr "カスタムコマンドをTTSとして使用します。{0}はテキストに置き換えられます"

#: src/constants.py:212
msgid "WordLlama"
msgstr "WordLlama"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr ""
"llamaベースの軽量ローカル埋め込みモデル。オフラインで動作し、非常に低いリソー"
"ス使用量"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "Ollama埋め込み"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr ""
"埋め込みにOllamaモデルを使用します。オフラインで動作し、非常に低いリソース使"
"用量"

#: src/constants.py:231
msgid "Use Google Gemini API to get embeddings"
msgstr "埋め込みを取得するためにGoogle Gemini APIを使用"

#: src/constants.py:239
msgid "User Summary"
msgstr "ユーザー要約"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "ユーザーの会話の要約を生成"

#: src/constants.py:245
msgid "Memoripy"
msgstr "Memoripy"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"コンテキストメモリの取得、メモリ減衰、概念抽出などの高度な技術を使用して、以"
"前の会話からメッセージを抽出します。メッセージごとに1回のLLM呼び出しを行いま"
"す。"

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "ユーザー要約 + Memoripy"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "長期記憶のために両方の技術を使用"

#: src/constants.py:260
msgid "Document reader"
msgstr "ドキュメントリーダー"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr ""
"古典的なRAGアプローチ - ドキュメントをチャンク化して埋め込み、クエリと比較し"
"て最も関連性の高いドキュメントを返します"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr "SearXNG - プライベートでセルフホスト可能な検索エンジン"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "DuckDuckGo検索"

#: src/constants.py:281
msgid "Tavily"
msgstr "Tavily"

#: src/constants.py:282
msgid "Tavily search"
msgstr "Tavily検索"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "役立つアシスタント"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr "LLMの回答を強化し、より多くのコンテキストを提供する汎用プロンプト"

#: src/constants.py:384
msgid "Console access"
msgstr "コンソールアクセス"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "プログラムはコンピュータ上でターミナルコマンドを実行できますか"

#: src/constants.py:392
msgid "Current directory"
msgstr "現在のディレクトリ"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "現在のディレクトリは何ですか"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "LLMがインターネットを検索することを許可する"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "基本機能"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "テーブルとコードの表示（*これなしでも動作可能）"

#: src/constants.py:419
msgid "Graphs access"
msgstr "グラフアクセス"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "プログラムはグラフを表示できますか"

#: src/constants.py:428
msgid "Show image"
msgstr "画像を表示"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "チャットに画像を表示"

#: src/constants.py:437
msgid "Custom Prompt"
msgstr "カスタムプロンプト"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "独自のカスタムプロンプトを追加"

#: src/constants.py:480
msgid "LLM and Secondary LLM settings"
msgstr "LLMおよびセカンダリLLM設定"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "TTS"

#: src/constants.py:485
msgid "Text to Speech settings"
msgstr "テキスト読み上げ設定"

#: src/constants.py:488
msgid "STT"
msgstr "STT"

#: src/constants.py:490
msgid "Speech to Text settings"
msgstr "音声認識設定"

#: src/constants.py:493
msgid "Embedding"
msgstr "埋め込み"

#: src/constants.py:495
msgid "Embedding settings"
msgstr "埋め込み設定"

#: src/constants.py:498
msgid "Memory"
msgstr "メモリ"

#: src/constants.py:500
msgid "Memory settings"
msgstr "メモリ設定"

#: src/constants.py:503
msgid "Websearch"
msgstr "ウェブ検索"

#: src/constants.py:505
msgid "Websearch settings"
msgstr "ウェブ検索設定"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
msgid "Document analyzer settings"
msgstr "ドキュメントアナライザー設定"

#: src/constants.py:515
msgid "Extensions settings"
msgstr "拡張機能設定"

#: src/constants.py:518
msgid "Inteface"
msgstr "インターフェース"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr "インターフェース設定、隠しファイル、逆順、ズーム..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr ""
"一般設定、仮想化、提案、メモリ期間、チャット名の自動生成、現在のフォルダ..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr "プロンプト設定、カスタム追加プロンプト、カスタムプロンプト..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "チャット"

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "ターミナルスレッドがバックグラウンドでまだ実行中です"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "ウィンドウを閉じると、自動的に終了されます"

#: src/main.py:210
msgid "Close"
msgstr "閉じる"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "チャットが再起動されました"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "フォルダが再起動されました"

#: src/main.py:254
msgid "Chat is created"
msgstr "チャットが作成されました"

#: src/window.py:120
msgid "Keyboard shorcuts"
msgstr "キーボードショートカット"

#: src/window.py:121
msgid "About"
msgstr "について"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "チャット"

#: src/window.py:170
msgid "History"
msgstr "履歴"

#: src/window.py:191
msgid "Create a chat"
msgstr "チャットを作成"

#: src/window.py:196
msgid "Chats"
msgstr "チャット"

#: src/window.py:267
msgid " Stop"
msgstr "停止"

#: src/window.py:282
msgid " Clear"
msgstr "クリア"

#: src/window.py:297
msgid " Continue"
msgstr "続ける"

#: src/window.py:310
msgid " Regenerate"
msgstr "再生成"

#: src/window.py:376
msgid "Send a message..."
msgstr "メッセージを送信..."

#: src/window.py:467
msgid "Explorer Tab"
msgstr "エクスプローラータブ"

#: src/window.py:468
msgid "Terminal Tab"
msgstr "ターミナルタブ"

#: src/window.py:469
msgid "Browser Tab"
msgstr "ブラウザタブ"

#: src/window.py:589
msgid "Ask about a website"
msgstr "ウェブサイトについて尋ねる"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr ""
"ウェブサイトに関する情報を尋ねるには、チャットに「#https://website.com」と入"
"力してください"

#: src/window.py:590
msgid "Check out our Extensions!"
msgstr "拡張機能をご覧ください！"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "さまざまな拡張機能があります。ぜひチェックしてください！"

#: src/window.py:591
msgid "Chat with documents!"
msgstr "ドキュメントとチャット！"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr ""
"ドキュメントをドキュメントフォルダに追加して、その中の情報を使ってチャットし"
"ましょう！"

#: src/window.py:592
msgid "Surf the web!"
msgstr "ウェブを閲覧！"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr ""
"ウェブ検索を有効にして、LLMがウェブを閲覧し、最新の回答を提供できるようにしま"
"す"

#: src/window.py:593
msgid "Mini Window"
msgstr "ミニウィンドウ"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "ミニウィンドウモードを使用して、その場で質問できます"

#: src/window.py:594
msgid "Text to Speech"
msgstr "テキスト読み上げ"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr ""
"Newelleはテキスト読み上げをサポートしています！設定で有効にしてください"

#: src/window.py:595
msgid "Keyboard Shortcuts"
msgstr "キーボードショートカット"

#: src/window.py:595
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "キーボードショートカットを使用してNewelleを操作する"

#: src/window.py:596
msgid "Prompt Control"
msgstr "プロンプト制御"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr ""
"Newelleは100%のプロンプト制御を提供します。用途に合わせてプロンプトを調整して"
"ください。"

#: src/window.py:597
msgid "Thread Editing"
msgstr "スレッドの編集"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "Newelleから実行するプログラムとプロセスを確認する"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr "プログラマブルプロンプト"

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr "条件と確率を使用して、動的なプロンプトをNewelleに追加できます"

#: src/window.py:605
msgid "New Chat"
msgstr "新しいチャット"

#: src/window.py:623
msgid "Provider Errror"
msgstr "プロバイダーエラー"

#: src/window.py:646
msgid "Local Documents"
msgstr "ローカルドキュメント"

#: src/window.py:650
msgid "Web search"
msgstr "ウェブ検索"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "このプロバイダーにはモデルリストがありません"

#: src/window.py:901
msgid " Models"
msgstr "モデル"

#: src/window.py:904
msgid "Search Models..."
msgstr "モデルを検索..."

#: src/window.py:1132
msgid "Create new profile"
msgstr "新しいプロファイルの作成"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "あなたの音声を認識できませんでした"

#: src/window.py:1303
msgid "Images"
msgstr "画像"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "LLM対応ファイル"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "RAG対応ファイル"

#: src/window.py:1333
msgid "Supported Files"
msgstr "対応ファイル"

#: src/window.py:1337
msgid "All Files"
msgstr "すべてのファイル"

#: src/window.py:1343
msgid "Attach file"
msgstr "ファイルを添付"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "プログラムが終了するまでファイルを送信できません"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "ファイルが認識されません"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "これ以上メッセージを続けることはできません。"

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "これ以上メッセージを再生成することはできません。"

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "チャットがクリアされました"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "メッセージはキャンセルされ、履歴から削除されました"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr "プログラムが終了するまでメッセージを送信できません"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr "プログラムの実行中はメッセージを編集できません。"

#: src/window.py:3080
msgid "Prompt content"
msgstr "プロンプトの内容"

#: src/window.py:3339
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"ニューラルネットワークはあなたのコンピューターとこのチャット内のあらゆるデー"
"タにアクセスでき、コマンドを実行できます。ニューラルネットワークについて当社"
"は責任を負いませんので、注意してください。機密情報を共有しないでください。"

#: src/window.py:3368
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"ニューラルネットワークはこのチャット内のあらゆるデータにアクセスできます。"
"ニューラルネットワークについて当社は責任を負いませんので、注意してください。"
"機密情報を共有しないでください。"

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "間違ったフォルダパス"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "スレッドが完了していません。スレッド番号："

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "フォルダを開くのに失敗しました"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "チャットは空です"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr ""
"カスタム拡張機能と新しいAIモジュールでNewelleをさらに活用し、チャットボットに"
"無限の可能性を与えます。"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "AIチャットボット"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "クイックプロファイル選択"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
msgid "Message Editing"
msgstr "メッセージ編集"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "10種類以上の標準AIプロバイダー"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "バグ修正"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
#, fuzzy
msgid "Minor bug fixes"
msgstr "バグ修正"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "小さな改善"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "ローカルドキュメントの読み込みとロードのパフォーマンスを改善"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "CTRL+Enterで送信するオプションを追加"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "コードブロックを改善"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
msgid "Fix Kokoro TTS"
msgstr "Kokoro TTSを修正"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "TTSから絵文字を削除"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "APIキーをパスワードフィールドとして設定"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "Geminiの思考サポートを追加"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "翻訳を更新"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "新機能を追加"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr "SearXNG、DuckDuckGo、Tavilyによるウェブサイトの読み取りとウェブ検索"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "LaTeXレンダリングとドキュメント管理を改善"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "新しい思考ウィジェットとOpenRouterハンドラー"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "Groq上のLlama4のビジョンサポート"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "新しい翻訳（繁体字中国語、ベンガル語、ヒンディー語）"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "多くのバグを修正し、いくつかの機能を追加しました！"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "新機能とバグ修正のサポート"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "多くの新機能とバグ修正を追加"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "新機能とバグ修正を追加"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr ""
"g4fライブラリをバージョン管理で更新し、ユーザーガイドを追加し、拡張機能のブラ"
"ウジングを改善し、モデル処理を強化しました。"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"バグ修正と新機能が実装されました。拡張機能のアーキテクチャを変更し、新しいモ"
"デルを追加し、ビジョンサポートをはじめとする多くの機能が導入されました。"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "安定版"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
msgid "Added extension"
msgstr "拡張機能を追加"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
msgid "Blacklist of commands"
msgstr "コマンドのブラックリスト"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "ローカライズ"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "再設計"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "Qwersyk"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "Newelle: あなたの高度なチャットボット"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "AI;アシスタント;チャット;ChatGPT;GPT;LLM;Ollama;"

#~ msgid "max Tokens"
#~ msgstr "最大トークン数"

#~ msgid "Max tokens of the generated text"
#~ msgstr "生成されるテキストの最大トークン数"
