msgid ""
msgstr ""
"Project-Id-Version: 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-07-03 09:00+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Turkish <<EMAIL>>\n"
"Language: tr\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "API Uç Noktası"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr "API temel URL'si, bunu diğer API'ları kullanmak için değiştirin"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid "Automatically Serve"
msgstr "Otomatik Olarak Sun"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"Gerektiğinde ollama'yı arka planda otomatik olarak çalıştırır. killall "
"ollama ile kapatabilirsiniz"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
msgid "Custom Model"
msgstr "Özel Model"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
msgid "Use a custom model"
msgstr "Özel bir model kullan"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
msgid "Ollama Model"
msgstr "Ollama Modeli"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
msgid "Name of the Ollama Model"
msgstr "Ollama Modelinin Adı"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "API Anahtarı"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
msgid "API Key for "
msgstr "API Anahtarı "

#: src/handlers/embeddings/openai_handler.py:38
msgid "API base url, change this to use different APIs"
msgstr "API temel URL'si, bunu farklı API'ları kullanmak için değiştirin"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
msgid "Use Custom Model"
msgstr "Özel Model Kullan"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "Model"

#: src/handlers/embeddings/openai_handler.py:44
msgid "Name of the Embedding Model to use"
msgstr "Kullanılacak Gömme Modelinin Adı"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
msgid " Model"
msgstr " Model"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
msgid "The API key to use"
msgstr "Kullanılacak API anahtarı"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
msgid "The model to use"
msgstr "Kullanılacak model"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "Maksimum Token"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "Oluşturulacak maksimum token sayısı"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
msgid "Message Streaming"
msgstr "Mesaj Akışı"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "Mesaj çıktısını aşamalı olarak akışa alın"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "Bot çıktısını almak için çalıştırılacak komut"

#: src/handlers/llm/custom_handler.py:21
#, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr ""
"Bot yanıtını almak için çalıştırılacak komut, {0} sohbeti içeren bir JSON "
"dosyasıyla, {1} ise sistem istemiyle değiştirilecektir"

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "Botun önerilerini almak için çalıştırılacak komut"

#: src/handlers/llm/custom_handler.py:22
#, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"Sohbet önerilerini almak için çalıştırılacak komut, {0} sohbeti içeren bir "
"JSON dosyasıyla, {1} ek istemlerle, {2} ise oluşturulacak öneri sayısıyla "
"değiştirilecektir. Önerileri dizeler olarak içeren bir JSON dizisi "
"döndürmelidir"

#: src/handlers/llm/gpt4all_handler.py:67
msgid "RAM Required: "
msgstr "Gerekli RAM: "

#: src/handlers/llm/gpt4all_handler.py:68
msgid "Parameters: "
msgstr "Parametreler: "

#: src/handlers/llm/gpt4all_handler.py:69
msgid "Size: "
msgstr "Boyut: "

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
msgid "Model to use"
msgstr "Kullanılacak model"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
msgid "Name of the model to use"
msgstr "Kullanılacak modelin adı"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "Model Manager"
msgstr "Model Yöneticisi"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "Mevcut modellerin listesi"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr "G4F'yi Güncelle"

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "Gizlilik Politikası"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "Gizlilik politikası web sitesini aç"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "Düşünmeyi Etkinleştir"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr "Modelde düşünmeye izin ver, yalnızca bazı modeller desteklenir"

#: src/handlers/llm/ollama_handler.py:176
msgid "Add custom model"
msgstr "Özel model ekle"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"Bu listeye herhangi bir model eklemek için ad:boyut yazın\n"
"Ya da hf'den herhangi bir gguf'u hf.co/kullanıcıadı/model ile ekleyin"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr "Ollama'yı Güncelle"

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "API Anahtarı (gerekli)"

#: src/handlers/llm/gemini_handler.py:94
msgid "API key got from ai.google.dev"
msgstr "ai.google.dev'den alınan API anahtarı"

#: src/handlers/llm/gemini_handler.py:98
msgid "AI Model to use"
msgstr "Kullanılacak Yapay Zeka Modeli"

#: src/handlers/llm/gemini_handler.py:103
msgid "Enable System Prompt"
msgstr "Sistem İstemini Etkinleştir"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr ""
"Bazı modeller sistem istemini (veya geliştirici talimatlarını) desteklemez, "
"eğer bu konuda hatalar alıyorsanız devre dışı bırakın"

#: src/handlers/llm/gemini_handler.py:107
msgid "Inject system prompt"
msgstr "Sistem istemini enjekte et"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr ""
"Model sistem istemlerini desteklemese bile, istemleri kullanıcı mesajının "
"üzerine koy"

#: src/handlers/llm/gemini_handler.py:109
msgid "Thinking Settings"
msgstr "Düşünme Ayarları"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "Düşünme modelleriyle ilgili ayarlar"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr "Düşünmeyi göster, modeliniz desteklemiyorsa bunu devre dışı bırakın"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "Düşünme Bütçesini Etkinleştir"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "Düşünme bütçesini etkinleştirip etkinleştirmeyeceği"

#: src/handlers/llm/gemini_handler.py:112
msgid "Thinking Budget"
msgstr "Düşünme Bütçesi"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "Ne kadar zaman harcanacağı"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "Görsel Çıktısı"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr ""
"Görsel çıktısını etkinleştir, yalnızca gemini-2.0-flash-exp tarafından "
"desteklenir"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "Güvenlik ayarlarını etkinleştir"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr ""
"Zararlı içerik oluşturmayı önlemek için Google güvenlik ayarlarını "
"etkinleştirin"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
msgid "Advanced Parameters"
msgstr "Gelişmiş Parametreler"

#: src/handlers/llm/gemini_handler.py:135
msgid "Enable advanced parameters"
msgstr "Gelişmiş parametreleri etkinleştir"

#: src/handlers/llm/openai_handler.py:81
#, fuzzy
msgid "Include parameters like Top-P, Temperature, etc."
msgstr "Maksimum Token, Top-P, Sıcaklık vb. parametreleri dahil et"

#: src/handlers/llm/openai_handler.py:84
msgid "Name of the LLM Model to use"
msgstr "Kullanılacak LLM Modelinin Adı"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "Top-P"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr "Sıcaklıkla örneklemeye bir alternatif, çekirdek örnekleme denir"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "Sıcaklık"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr ""
"Hangi örnekleme sıcaklığını kullanmalı. Daha yüksek değerler çıktıyı daha "
"rastgele hale getirecektir"

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "Frekans Cezası"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr ""
"-2.0 ile 2.0 arasında bir sayı. Pozitif değerler, modelin aynı satırı kelime "
"kelime tekrarlama olasılığını azaltır"

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "Varoluş Cezası"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr ""
"-2.0 ile 2.0 arasında bir sayı. Pozitif değerler, modelin yeni konular "
"hakkında konuşma olasılığını azaltır"

#: src/handlers/llm/openai_handler.py:108
#, fuzzy
msgid "Custom Options"
msgstr "Özel İstem"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr ""

#: src/handlers/llm/openrouter_handler.py:14
msgid "Provider Sorting"
msgstr "Sağlayıcı Sıralaması"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "Fiyatlandırma/verim veya gecikmeye göre sağlayıcıları seçin"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "Fiyat"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "Verim"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "Gecikme"

#: src/handlers/llm/openrouter_handler.py:15
msgid "Providers Order"
msgstr "Sağlayıcılar Sırası"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr ""
"Kullanılacak sağlayıcıların sırasını ekleyin, adlar virgülle ayrılmıştır.\n"
"Belirtmemek için boş bırakın"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "Yedeklere İzin Ver"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "Diğer sağlayıcılara yedeklemelere izin ver"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "Belgelerinizi dizine ekleyin"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"Belge klasörünüzdeki tüm belgeleri dizine ekleyin. Bu işlemi, bir belgeyi "
"her düzenlediğinizde/oluşturduğunuzda, belge analizcisini değiştirdiğinizde "
"veya gömme modelini değiştirdiğinizde çalıştırmanız gerekir"

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "Çalıştırılacak komut"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "{0} modelin tam yolu ile değiştirilecektir"

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr ""
"Google SR için API Anahtarı, varsayılanı kullanmak için 'default' yazın"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "Dil"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "IETF'de tanınacak metnin dili"

#: src/handlers/stt/groqsr_handler.py:14
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr "Groq SR için API Anahtarı, varsayılanı kullanmak için 'default' yazın"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "Groq Modeli"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "Groq Modelinin Adı"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Deşifre için dili belirtin. ISO 639-1 dil kodlarını kullanın (örn. İngilizce "
"için \"en\", Fransızca için \"fr\" vb.). "

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "OpenAI istekleri için uç nokta"

#: src/handlers/stt/openaisr_handler.py:18
msgid "API Key for OpenAI"
msgstr "OpenAI için API Anahtarı"

#: src/handlers/stt/openaisr_handler.py:25
msgid "Whisper Model"
msgstr "Whisper Modeli"

#: src/handlers/stt/openaisr_handler.py:26
msgid "Name of the OpenAI model"
msgstr "OpenAI modelinin adı"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"İsteğe bağlı: Deşifre için dili belirtin. ISO 639-1 dil kodlarını kullanın "
"(örn. İngilizce için \"en\", Fransızca için \"fr\" vb.). "

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "Model Yolu"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "VOSK modelinin mutlak yolu (açılmış)"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
msgid "Name of the Whisper model"
msgstr "Whisper modelinin adı"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "wit.ai için Sunucu Erişim Tokeni"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "Sesi anlayamadı"

#: src/handlers/stt/whispercpp_handler.py:47
#, fuzzy
msgid "Language of the recognition. For example en, it..."
msgstr "Tanımanın dili."

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Model Library"
msgstr "Model Kütüphanesi"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Manage Whisper models"
msgstr "Whisper modellerini yönet"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "Advanced Settings"
msgstr "Gelişmiş Ayarlar"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "More advanced settings"
msgstr "Daha gelişmiş ayarlar"

#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature to use"
msgstr "Kullanılacak sıcaklık"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt for the recognition"
msgstr "Tanıma için istem"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr "Tanıma için kullanılacak istem"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Endpoint"
msgstr "Uç Nokta"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "Kullanılacak hizmetin özel uç noktası"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "Ses"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
msgid "The voice to use"
msgstr "Kullanılacak ses"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "Talimatlar"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr "Ses oluşturma talimatları. Bu alanı boş bırakın"

#: src/handlers/tts/custom_handler.py:17
#, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr "{0} dosyanın tam yolu ile, {1} metin ile değiştirilecektir"

#: src/handlers/tts/elevenlabs_handler.py:11
msgid "API Key for ElevenLabs"
msgstr "ElevenLabs için API Anahtarı"

#: src/handlers/tts/elevenlabs_handler.py:19
msgid "Voice ID to use"
msgstr "Kullanılacak Ses Kimliği"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "Stabilite"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "sesin stabilite durumu"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "Benzerlik artışı"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "Genel ses netliğini ve konuşmacı benzerliğini artırır"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "Stil abartısı"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr "Konuşma tarzının abartılması gerekiyorsa yüksek değerler önerilir"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "Tercih edilen sesi seçin"

#: src/handlers/websearch/tavily.py:20
msgid "Token"
msgstr "Belirteç"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "Tavily API anahtarı"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "Maksimum Sonuç"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "Dikkate alınacak sonuç sayısı"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "Arama derinliği"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"Arama derinliği. Gelişmiş arama, sorgunuz için en alakalı kaynakları ve "
"içerik parçacıklarını almak üzere tasarlanırken, temel arama her kaynaktan "
"genel içerik parçacıkları sağlar. Temel bir arama 1 API Kredisine mal "
"olurken, gelişmiş bir arama 2 API Kredisine mal olur."

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "Arama kategorisi"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"Arama kategorisi. Haberler, gerçek zamanlı güncellemeleri almak için "
"kullanışlıdır, özellikle siyaset, spor ve ana akım medya kaynakları "
"tarafından kapsanan büyük güncel olaylar hakkında. Genel, daha geniş, daha "
"genel amaçlı aramalar için olup, çok çeşitli kaynakları içerebilir."

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "Kaynak başına parçalar"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr ""
"Her kaynaktan alınacak içerik parçacığı sayısı. Her parçacığın uzunluğu "
"maksimum 500 karakterdir. Yalnızca arama derinliği gelişmiş olduğunda "
"kullanılabilir."

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "Mevcut tarihten geriye doğru kaç günün dahil edileceği"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "Yalnızca konu haber ise kullanılabilir."

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "Yanıtı dahil et"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"Sağlanan sorguya LLM tarafından oluşturulmuş bir yanıt dahil edin. Temel "
"arama hızlı bir yanıt döndürür. Gelişmiş arama daha ayrıntılı bir yanıt "
"döndürür."

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "Ham içeriği dahil et"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr ""
"Her arama sonucunun temizlenmiş ve ayrıştırılmış HTML içeriğini dahil edin."

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "Görselleri dahil et"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr "Bir görsel araması yapın ve sonuçları yanıta dahil edin."

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "Görsel açıklamalarını dahil et"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr ""
"Görselleri Dahil Et etkinleştirildiğinde, her görsel için açıklayıcı bir "
"metin de ekleyin."

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "Alan adlarını dahil et"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr "Arama sonuçlarına özellikle dahil edilecek alan adları listesi."

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "Alan adlarını hariç tut"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr "Arama sonuçlarından özellikle hariç tutulacak alan adları listesi."

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "Bölge"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "Arama sonuçları için bölge"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "Ayarlar"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "Profil Adı"

#: src/ui/profile.py:58
msgid "Copied Settings"
msgstr "Kopyalanan Ayarlar"

#: src/ui/profile.py:58
msgid "Settings that will be copied to the new profile"
msgstr "Yeni profile kopyalanacak ayarlar"

#: src/ui/profile.py:70
msgid "Create Profile"
msgstr "Profil Oluştur"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "Profili İçe Aktar"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
msgid "Edit Profile"
msgstr "Profili Düzenle"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "Profili Dışa Aktar"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "Şifreleri Dışa Aktar"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "Şifre benzeri alanları da dışa aktar"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "Profil Resmini Dışa Aktar"

#: src/ui/profile.py:89
msgid "Also export the profile picture"
msgstr "Profil resmini de dışa aktar"

#: src/ui/profile.py:109 src/ui/explorer.py:692
msgid "Create"
msgstr "Oluştur"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "Uygula"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr "Mevcut profilin ayarları yeni profile kopyalanacaktır"

#: src/ui/profile.py:122 src/ui/profile.py:128
msgid "Newelle Profiles"
msgstr "Newelle Profilleri"

#: src/ui/profile.py:123
msgid "Export"
msgstr "Dışa Aktar"

#: src/ui/profile.py:129
msgid "Import"
msgstr "İçe Aktar"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "Profil resmi ayarla"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "İş Parçacığı Düzenleme"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "Hiçbir iş parçacığı çalışmıyor"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "İş parçacığı numarası: "

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "Profil seç"

#: src/ui/widgets/profilerow.py:53
msgid "Delete Profile"
msgstr "Profili Sil"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "Düşünceler"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "Ayrıntıları görmek için genişlet"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "Düşünüyor..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr "LLM düşünüyor... Düşünme sürecini görmek için genişlet"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "Düşünce süreci kaydedilmedi"

#: src/ui/widgets/tipscarousel.py:41
msgid "Newelle Tips"
msgstr "Newelle İpuçları"

#: src/ui/explorer.py:192
msgid "Folder is Empty"
msgstr "Klasör Boş"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr "Dosya bulunamadı"

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "Yeni sekmede aç"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "Entegre editörde aç"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "Dosya yöneticisinde aç"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
msgid "Rename"
msgstr "Yeniden Adlandır"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "Sil"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "Tam yolu kopyala"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
msgid "Failed to open file manager"
msgstr "Dosya yöneticisi açılamadı"

#: src/ui/explorer.py:436
msgid "New name:"
msgstr "Yeni ad:"

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "İptal"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "Başarıyla yeniden adlandırıldı"

#: src/ui/explorer.py:476
#, python-brace-format
msgid "Failed to rename: {}"
msgstr "Yeniden adlandırılamadı: {}"

#: src/ui/explorer.py:497
msgid "Delete File?"
msgstr "Dosya Silinsin mi?"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "\"{}\" öğesini silmek istediğinizden emin misiniz?"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "Başarıyla silindi"

#: src/ui/explorer.py:525
#, python-brace-format
msgid "Failed to delete: {}"
msgstr "Silinemedi: {}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "Yol panoya kopyalandı"

#: src/ui/explorer.py:542
msgid "Failed to copy path"
msgstr "Yol kopyalanamadı"

#: src/ui/explorer.py:580
msgid "Create new folder"
msgstr "Yeni klasör oluştur"

#: src/ui/explorer.py:583
msgid "Create new file"
msgstr "Yeni dosya oluştur"

#: src/ui/explorer.py:586
msgid "Open Terminal Here"
msgstr "Burada Terminali Aç"

#: src/ui/explorer.py:640
msgid "Create New Folder"
msgstr "Yeni Klasör Oluştur"

#: src/ui/explorer.py:640
msgid "Folder name:"
msgstr "Klasör adı:"

#: src/ui/explorer.py:644
msgid "Create New File"
msgstr "Yeni Dosya Oluştur"

#: src/ui/explorer.py:644
msgid "File name:"
msgstr "Dosya adı:"

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "Klasör başarıyla oluşturuldu"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "Dosya başarıyla oluşturuldu"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "Bu isimde bir dosya veya klasör zaten var"

#: src/ui/explorer.py:728
msgid "folder"
msgstr "klasör"

#: src/ui/explorer.py:728
msgid "file"
msgstr "dosya"

#: src/ui/explorer.py:730
#, python-brace-format
msgid "Failed to create {}: {}"
msgstr "Oluşturulamadı {}: {}"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "Yardım"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "Kısayollar"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "Sohbeti yeniden yükle"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "Klasörü yeniden yükle"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "Yeni sekme"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "Görsel Yapıştır"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "Mesaj kutusuna odaklan"

#: src/ui/shortcuts.py:18
msgid "Start/stop recording"
msgstr "Kaydı başlat/durdur"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "Kaydet"

#: src/ui/shortcuts.py:20
msgid "Stop TTS"
msgstr "Metinden Konuşmaya Durdur"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr "Yakınlaştır"

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr "Uzaklaştır"

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr "Program Çıktı Monitörü"

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr "Çıktıyı temizle"

#: src/ui/stdout_monitor.py:61
msgid "Start/Stop monitoring"
msgstr "İzlemeyi Başlat/Durdur"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr "İzleme: Aktif"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr "İzleme: Durduruldu"

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr "Satırlar: {}"

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr "Satırlar: 0"

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "Uzantılar"

#: src/ui/extension.py:50
msgid "Installed Extensions"
msgstr "Yüklü Uzantılar"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "Uzantılar için kullanıcı rehberi"

#: src/ui/extension.py:89
msgid "Download new Extensions"
msgstr "Yeni Uzantıları İndir"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "Dosyadan uzantı yükle..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "Newelle"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "Sohbet küçük pencerede açıldı"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "Newelle'e Hoş Geldiniz"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "Nihai sanal asistanınız."

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "Github Sayfası"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "Favori Yapay Zeka Dil Modelinizi Seçin"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"Newelle birden fazla model ve sağlayıcı ile kullanılabilir!\n"
"<b>Not: LLM Rehberi sayfasını okumanız şiddetle tavsiye edilir</b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "LLM Rehberi"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "Belgelerinizle sohbet edin"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr ""
"Newelle, sohbette gönderdiğiniz belgelerden veya kendi dosyalarınızdan "
"ilgili bilgileri alabilir! Sorgunuzla ilgili bilgiler LLM'ye gönderilecektir."

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "Komut sanallaştırma"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"Newelle, sisteminizde komut çalıştırmak için kullanılabilir, ancak "
"çalıştırdığınız şeye dikkat edin! <b>LLM bizim kontrolümüzde değildir, bu "
"nedenle kötü amaçlı kod üretebilir!</b>\n"
"Varsayılan olarak, komutlarınız <b>Flatpak ortamında sanallaştırılacaktır</"
"b>, ancak dikkatli olun!"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "Newelle'in işlevselliğini uzantılar kullanarak genişletebilirsiniz!"

#: src/ui/presentation.py:136
msgid "Download extensions"
msgstr "Uzantıları indir"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "İzin Hatası"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr "Newelle, sisteminizde komut çalıştırmak için yeterli izne sahip değil."

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "Uygulamayı kullanmaya başla"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "Sohbete başla"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "Genel"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "LLM"

#: src/ui/settings.py:49 src/constants.py:528
msgid "Prompts"
msgstr "İstemler"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "Bilgi"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "Dil Modeli"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "Diğer LLM'ler"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "Mevcut diğer LLM sağlayıcıları"

#: src/ui/settings.py:73
msgid "Advanced LLM Settings"
msgstr "Gelişmiş LLM Ayarları"

#: src/ui/settings.py:77
msgid "Secondary Language Model"
msgstr "İkincil Dil Modeli"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr ""
"Teklif, sohbet adı ve bellek oluşturma gibi ikincil görevler için kullanılan "
"model"

#: src/ui/settings.py:94
msgid "Embedding Model"
msgstr "Gömme Modeli"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"Gömme, metni vektörlere dönüştürmek için kullanılır. Uzun Süreli Bellek ve "
"RAG tarafından kullanılır. Değiştirmek, belgeleri yeniden dizine eklemenizi "
"veya belleği sıfırlamanızı gerektirebilir."

#: src/ui/settings.py:105 src/window.py:647
msgid "Long Term Memory"
msgstr "Uzun Süreli Bellek"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "Eski konuşmaların belleğini tut"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "Web Araması"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "Web'de bilgi ara"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "Metinden Konuşmaya Programı"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "Kullanılacak metin okuyucuyu seçin"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "Konuşmadan Metne Motoru"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "Hangi konuşma tanıma motorunu istediğinizi seçin"

#: src/ui/settings.py:150
msgid "Automatic Speech To Text"
msgstr "Otomatik Konuşmadan Metne"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr "Metin/TTS'nin sonunda konuşmayı metne otomatik olarak yeniden başlat"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "İstem kontrolü"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "Arayüz"

#: src/ui/settings.py:162
msgid "Interface Size"
msgstr "Arayüz Boyutu"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "Arayüz boyutunu ayarla"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "Düzenleyici renk şemasını değiştir"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "Düzenleyicinin ve kod bloklarının renk şemasını değiştir"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "Gizli dosyalar"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "Gizli dosyaları göster"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "ENTER ile Gönder"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"Etkinleştirilirse, mesajlar ENTER ile gönderilecektir; yeni bir satıra "
"geçmek için CTRL+ENTER kullanın. Devre dışı bırakılırsa, mesajlar "
"SHIFT+ENTER ile gönderilecek ve yeni satıra enter ile geçilecektir"

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "Düşünceleri geçmişten kaldır"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr ""
"Belirteç kullanımını azaltmak için akıl yürütme modelleri için eski düşünce "
"bloklarını göndermeyin"

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "LaTeX Görüntüle"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "Sohbette LaTeX formüllerini görüntüle"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "Sohbet Sırasını Ters Çevir"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr ""
"Sohbet listesinde en yeni sohbetleri üstte göster (uygulamak için sohbeti "
"değiştir)"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "Otomatik Olarak Sohbet Adları Oluştur"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr "İlk iki mesajdan sonra sohbet adlarını otomatik olarak oluştur"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "Teklif sayısı"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "Sohbete gönderilecek mesaj önerisi sayısı "

#: src/ui/settings.py:224
msgid "Username"
msgstr "Kullanıcı Adı"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr ""
"Mesajınızdan önce görünen etiketi değiştirin\n"
"Bu bilgi varsayılan olarak LLM'ye gönderilmez\n"
"{USER} değişkenini kullanarak bir isteme ekleyebilirsiniz"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "Sinir Ağı Kontrolü"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "Komutları sanal bir makinede çalıştır"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "Harici Terminal"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr "Konsol komutlarını çalıştırmak için harici terminali seçin"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "Program belleği"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "Programın sohbeti ne kadar süreyle hatırladığı "

#: src/ui/settings.py:266
msgid "Developer"
msgstr "Geliştirici"

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr ""
"Program çıktısını gerçek zamanlı olarak izleyin, hata ayıklama ve indirme "
"ilerlemesini görmek için kullanışlıdır"

#: src/ui/settings.py:270
msgid "Open"
msgstr "Aç"

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr "pip yolunu sil"

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr "Yüklü ek bağımlılıkları kaldır"

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr ""

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr ""

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "Otomatik çalıştır komutları"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "Botun yazacağı komutlar otomatik olarak çalışacaktır"

#: src/ui/settings.py:313
msgid "Max number of commands"
msgstr "Maksimum komut sayısı"

#: src/ui/settings.py:313
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr ""
"Botun tek bir kullanıcı isteğinden sonra yazacağı maksimum komut sayısı"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "Tarayıcı"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "Tarayıcı ayarları"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "Harici tarayıcı kullan"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr "Bağlantıları entegre olan yerine harici bir tarayıcıda açın"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "Tarayıcı oturumunu kalıcı yap"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr ""
"Yeniden başlatmalar arasında tarayıcı oturumunu kalıcı yap. Bunu kapatmak "
"programın yeniden başlatılmasını gerektirir"

#: src/ui/settings.py:361
msgid "Delete browser data"
msgstr "Tarayıcı verilerini sil"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "Tarayıcı oturumunu ve verilerini sil"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "İlk tarayıcı sayfası"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "Tarayıcının başlayacağı sayfa"

#: src/ui/settings.py:375
msgid "Search string"
msgstr "Arama dizesi"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr "Tarayıcıda kullanılan arama dizesi, %s sorgu ile değiştirilir"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "Belge Kaynakları (RAG)"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "Yanıtlarınıza belgelerinizden içerik ekleyin"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "Belge Çözümleyici"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr ""
"Belge analizörü, belgelerinizle ilgili ilgili bilgileri çıkarmak için birden "
"çok teknik kullanır"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "Desteklenmiyorsa belgeleri oku"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr ""
"LLM belgeleri okumayı desteklemiyorsa, sohbette gönderilen belgelerle ilgili "
"ilgili bilgiler Belge Analizörünüz kullanılarak LLM'ye verilecektir."

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "RAG için maksimum token"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"RAG için kullanılacak maksimum token miktarı. Eğer belgeler bu token "
"miktarını aşmıyorsa,\n"
"hepsini bağlama dökün"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "Belge Klasörü"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr ""
"Sorgulamak istediğiniz belgeleri belge klasörünüze koyun. Bu seçenek etkin "
"ise belge analizörü onlarda ilgili bilgileri bulacaktır"

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr "Dizine eklemek istediğiniz tüm belgeleri bu klasöre koyun"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "Sessizlik eşiği"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr ""
"Saniye cinsinden sessizlik eşiği, hacmin sessizlik olarak kabul edilecek "
"yüzdesi"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "Sessizlik süresi"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr ""
"Kaydın otomatik olarak durmasından önceki saniye cinsinden sessizlik süresi"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "Yeterli izin yok"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr ""
"Newelle'in sisteminizde komut çalıştırmak için yeterli izni yok, lütfen "
"aşağıdaki komutu çalıştırın"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "Anlaşıldı"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr "Pip yolu silindi"

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr ""
"Pip yolu silindi, şimdi bağımlılıkları yeniden yükleyebilirsiniz. Bu işlem "
"uygulamanın yeniden başlatılmasını gerektirir."

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "Newelle Demo API'si"

#: src/constants.py:26
msgid "GPT4Free"
msgstr "GPT4Free"

#: src/constants.py:34
msgid "Local Model"
msgstr "Yerel Model"

#: src/constants.py:35
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr ""
"GPU DESTEĞİ YOK, BUNUN YERİNE OLLAMA KULLANIN. Bir LLM modelini yerel olarak "
"çalıştırın, daha fazla gizlilik ama daha yavaş"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "Ollama Örneği"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "Kendi donanımınızda birden fazla LLM modelini kolayca çalıştırın"

#: src/constants.py:47
msgid "Groq"
msgstr "Groq"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "Google Gemini API'si"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "OpenAI API'si"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr ""
"OpenAI API'si. Özel uç noktalar desteklenir. Özel sağlayıcılar için bunu "
"kullanın"

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "Anthropic Claude"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr ""
"Anthropic Claude'un modelleri için resmi API'ler, görsel ve dosya desteği "
"ile, bir API anahtarı gerektirir"

#: src/constants.py:73
msgid "Mistral"
msgstr "Mistral"

#: src/constants.py:74
msgid "Mistral API"
msgstr "Mistral API'si"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "Openrouter.ai API'si, birçok modeli destekler"

#: src/constants.py:87
msgid "Deepseek"
msgstr "Deepseek"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "Deepseek API'si, en güçlü açık kaynak modelleri"

#: src/constants.py:94 src/constants.py:203
msgid "Custom Command"
msgstr "Özel Komut"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "Özel bir komutun çıktısını kullan"

#: src/constants.py:104
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr ""
"Çevrimdışı çalışır. C++'da yazılmış optimize edilmiş Whisper uygulaması"

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "Çevrimdışı çalışır. Yalnızca İngilizce desteklenir"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "Google Konuşma Tanıma"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "Google Konuşma Tanıma çevrimiçi"

#: src/constants.py:124
msgid "Groq Speech Recognition"
msgstr "Groq Konuşma Tanıma"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit Yapay Zeka"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr "wit.ai konuşma tanıma ücretsiz API (dili web sitesinde seçilir)"

#: src/constants.py:137
msgid "Vosk API"
msgstr "Vosk API'si"

#: src/constants.py:138
msgid "Works Offline"
msgstr "Çevrimdışı Çalışır"

#: src/constants.py:144
msgid "Whisper API"
msgstr "Whisper API'si"

#: src/constants.py:145
msgid "Uses OpenAI Whisper API"
msgstr "OpenAI Whisper API'sini kullanır"

#: src/constants.py:151
msgid "Custom command"
msgstr "Özel komut"

#: src/constants.py:152
msgid "Runs a custom command"
msgstr "Özel bir komut çalıştırır"

#: src/constants.py:161
msgid "Google TTS"
msgstr "Google Metinden Konuşmaya"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "Google'ın metinden konuşmaya"

#: src/constants.py:167
msgid "Kokoro TTS"
msgstr "Kokoro Metinden Konuşmaya"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr ""
"Hafif ve hızlı açık kaynak Metinden Konuşmaya motoru. ~3GB bağımlılıklar, "
"400MB model"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "ElevenLabs Metinden Konuşmaya"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "Doğal ses veren Metinden Konuşmaya"

#: src/constants.py:179 src/constants.py:180
msgid "OpenAI TTS"
msgstr "OpenAI Metinden Konuşmaya"

#: src/constants.py:185
msgid "Groq TTS"
msgstr "Groq Metinden Konuşmaya"

#: src/constants.py:186
msgid "Groq TTS API"
msgstr "Groq Metinden Konuşmaya API'si"

#: src/constants.py:191 src/constants.py:192
msgid "Custom OpenAI TTS"
msgstr "Özel OpenAI Metinden Konuşmaya"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "Espeak Metinden Konuşmaya"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "Çevrimdışı Metinden Konuşmaya"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr ""
"Metin okuma olarak özel bir komut kullanın, {0} metin ile değiştirilecektir"

#: src/constants.py:212
msgid "WordLlama"
msgstr "WordLlama"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr ""
"Llama tabanlı hafif yerel gömme modeli. Çevrimdışı çalışır, çok düşük kaynak "
"kullanımı"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "Ollama Gömme"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr ""
"Gömme için Ollama modellerini kullanın. Çevrimdışı çalışır, çok düşük kaynak "
"kullanımı"

#: src/constants.py:231
msgid "Use Google Gemini API to get embeddings"
msgstr "Gömme elde etmek için Google Gemini API'sini kullanın"

#: src/constants.py:239
msgid "User Summary"
msgstr "Kullanıcı Özeti"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "Kullanıcının konuşmasının özetini oluştur"

#: src/constants.py:245
msgid "Memoripy"
msgstr "Memoripy"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"Önceki konuşmalardan mesajları bağlamsal bellek alma, bellek çürümesi, "
"kavram çıkarma ve diğer gelişmiş teknikler kullanarak çıkarın. Her mesaj "
"başına 1 llm çağrısı yapar."

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "Kullanıcı Özeti + Memoripy"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "Uzun süreli bellek için her iki teknolojiyi de kullanın"

#: src/constants.py:260
msgid "Document reader"
msgstr "Belge okuyucu"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr ""
"Klasik RAG yaklaşımı - belgeleri parçalara ayırın ve gömün, ardından "
"sorguyla karşılaştırın ve en alakalı belgeleri döndürün"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr "SearXNG - Özel ve kendi barındırılabilir arama motoru"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "DuckDuckGo araması"

#: src/constants.py:281
msgid "Tavily"
msgstr "Tavily"

#: src/constants.py:282
msgid "Tavily search"
msgstr "Tavily araması"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "Yardımcı asistan"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr ""
"LLM yanıtlarını geliştirmek ve daha fazla bağlam sağlamak için genel amaçlı "
"istem"

#: src/constants.py:384
msgid "Console access"
msgstr "Konsol erişimi"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "Program bilgisayarda terminal komutları çalıştırabilir mi"

#: src/constants.py:392
msgid "Current directory"
msgstr "Mevcut dizin"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "Mevcut dizin nedir"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "LLM'nin internette arama yapmasına izin ver"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "Temel işlevsellik"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "Tabloları ve kodu gösterme (*olmadan da çalışabilir)"

#: src/constants.py:419
msgid "Graphs access"
msgstr "Grafik erişimi"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "Program grafik görüntüleyebilir mi"

#: src/constants.py:428
msgid "Show image"
msgstr "Görseli göster"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "Sohbette görsel göster"

#: src/constants.py:437
msgid "Custom Prompt"
msgstr "Özel İstem"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "Kendi özel isteminizi ekleyin"

#: src/constants.py:480
msgid "LLM and Secondary LLM settings"
msgstr "LLM ve İkincil LLM ayarları"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "TTS"

#: src/constants.py:485
msgid "Text to Speech settings"
msgstr "Metinden Konuşmaya ayarları"

#: src/constants.py:488
msgid "STT"
msgstr "STT"

#: src/constants.py:490
msgid "Speech to Text settings"
msgstr "Konuşmadan Metne ayarları"

#: src/constants.py:493
msgid "Embedding"
msgstr "Gömme"

#: src/constants.py:495
msgid "Embedding settings"
msgstr "Gömme ayarları"

#: src/constants.py:498
msgid "Memory"
msgstr "Bellek"

#: src/constants.py:500
msgid "Memory settings"
msgstr "Bellek ayarları"

#: src/constants.py:503
msgid "Websearch"
msgstr "Web Araması"

#: src/constants.py:505
msgid "Websearch settings"
msgstr "Web araması ayarları"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
msgid "Document analyzer settings"
msgstr "Belge analizörü ayarları"

#: src/constants.py:515
msgid "Extensions settings"
msgstr "Uzantı ayarları"

#: src/constants.py:518
msgid "Inteface"
msgstr "Arayüz"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr "Arayüz ayarları, gizli dosyalar, ters sıra, yakınlaştırma..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr ""
"Genel ayarlar, sanallaştırma, teklifler, bellek uzunluğu, otomatik sohbet "
"adı oluşturma, mevcut klasör..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr "İstem ayarları, özel ek istem, özel istemler..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "Sohbet "

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "Terminal iş parçacıkları hala arka planda çalışıyor"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "Pencereyi kapattığınızda, otomatik olarak sonlandırılacaklardır"

#: src/main.py:210
msgid "Close"
msgstr "Kapat"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "Sohbet yeniden başlatıldı"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "Klasör yeniden başlatıldı"

#: src/main.py:254
msgid "Chat is created"
msgstr "Sohbet oluşturuldu"

#: src/window.py:120
msgid "Keyboard shorcuts"
msgstr "Klavye kısayolları"

#: src/window.py:121
msgid "About"
msgstr "Hakkında"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "Sohbet"

#: src/window.py:170
msgid "History"
msgstr "Geçmiş"

#: src/window.py:191
msgid "Create a chat"
msgstr "Bir sohbet oluştur"

#: src/window.py:196
msgid "Chats"
msgstr "Sohbetler"

#: src/window.py:267
msgid " Stop"
msgstr " Dur"

#: src/window.py:282
msgid " Clear"
msgstr " Temizle"

#: src/window.py:297
msgid " Continue"
msgstr " Devam Et"

#: src/window.py:310
msgid " Regenerate"
msgstr " Yeniden Oluştur"

#: src/window.py:376
msgid "Send a message..."
msgstr "Bir mesaj gönder..."

#: src/window.py:467
msgid "Explorer Tab"
msgstr "Gezgin Sekmesi"

#: src/window.py:468
msgid "Terminal Tab"
msgstr "Terminal Sekmesi"

#: src/window.py:469
msgid "Browser Tab"
msgstr "Tarayıcı Sekmesi"

#: src/window.py:589
msgid "Ask about a website"
msgstr "Bir web sitesi hakkında soru sor"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr ""
"Bir web sitesi hakkında bilgi sormak için sohbete #https://website.com yazın"

#: src/window.py:590
msgid "Check out our Extensions!"
msgstr "Uzantılarımıza göz atın!"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "Farklı şeyler için birçok uzantımız var. Göz atın!"

#: src/window.py:591
msgid "Chat with documents!"
msgstr "Belgelerle sohbet edin!"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr ""
"Belgelerinizi belge klasörünüze ekleyin ve içerdikleri bilgileri kullanarak "
"sohbet edin!"

#: src/window.py:592
msgid "Surf the web!"
msgstr "İnternette gezin!"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr ""
"LLM'nin web'de gezinmesine ve güncel yanıtlar sağlamasına izin vermek için "
"web aramasını etkinleştirin"

#: src/window.py:593
msgid "Mini Window"
msgstr "Mini Pencere"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "Mini pencere modunu kullanarak anında sorular sorun"

#: src/window.py:594
msgid "Text to Speech"
msgstr "Metinden Konuşmaya"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr "Newelle metinden konuşmaya destekler! Ayarlarda etkinleştirin"

#: src/window.py:595
msgid "Keyboard Shortcuts"
msgstr "Klavye Kısayolları"

#: src/window.py:595
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "Newelle'i Klavye Kısayolları ile kontrol edin"

#: src/window.py:596
msgid "Prompt Control"
msgstr "İstem Kontrolü"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr ""
"Newelle size %100 istem kontrolü sağlar. İstemlerinizi kullanımınıza göre "
"ayarlayın."

#: src/window.py:597
msgid "Thread Editing"
msgstr "İş Parçacığı Düzenleme"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "Newelle'den çalıştırdığınız programları ve süreçleri kontrol edin"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr "Programlanabilir İstemler"

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr "Newelle'e koşullar ve olasılıklarla dinamik istemler ekleyebilirsiniz"

#: src/window.py:605
msgid "New Chat"
msgstr "Yeni Sohbet"

#: src/window.py:623
msgid "Provider Errror"
msgstr "Sağlayıcı Hatası"

#: src/window.py:646
msgid "Local Documents"
msgstr "Yerel Belgeler"

#: src/window.py:650
msgid "Web search"
msgstr "Web araması"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "Bu sağlayıcının bir model listesi yok"

#: src/window.py:901
msgid " Models"
msgstr " Modeller"

#: src/window.py:904
msgid "Search Models..."
msgstr "Model Ara..."

#: src/window.py:1132
msgid "Create new profile"
msgstr "Yeni profil oluştur"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "Sesiniz tanınamadı"

#: src/window.py:1303
msgid "Images"
msgstr "Görseller"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "LLM Desteklenen Dosyalar"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "RAG Desteklenen dosyalar"

#: src/window.py:1333
msgid "Supported Files"
msgstr "Desteklenen Dosyalar"

#: src/window.py:1337
msgid "All Files"
msgstr "Tüm Dosyalar"

#: src/window.py:1343
msgid "Attach file"
msgstr "Dosya ekle"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "Program bitene kadar dosya gönderilemez"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "Dosya tanınmadı"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "Mesajı artık devam ettiremezsiniz."

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "Mesajı artık yeniden oluşturamazsınız."

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "Sohbet temizlendi"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "Mesaj iptal edildi ve geçmişten silindi"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr "Program bitene kadar mesaj gönderilemez"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr "Program çalışırken bir mesajı düzenleyemezsiniz."

#: src/window.py:3080
msgid "Prompt content"
msgstr "İstem içeriği"

#: src/window.py:3339
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"Sinir ağı bilgisayarınıza ve bu sohbetteki tüm verilere erişebilir ve komut "
"çalıştırabilir, dikkatli olun, sinir ağından biz sorumlu değiliz. Hassas "
"bilgileri paylaşmayın."

#: src/window.py:3368
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"Sinir ağı bu sohbetteki herhangi bir veriye erişebilir, dikkatli olun, sinir "
"ağından biz sorumlu değiliz. Hassas bilgileri paylaşmayın."

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "Yanlış klasör yolu"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "İş parçacığı tamamlanmadı, iş parçacığı numarası: "

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "Klasör açılamadı"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "Sohbet boş"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr ""
"Newelle'i özel uzantılar ve yeni yapay zeka modülleriyle daha fazlasını "
"yapması için eğitin, sohbet robotunuza sonsuz olanaklar sunar."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "Yapay zeka sohbet robotu"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "Hızlı profil seçimi"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
msgid "Message Editing"
msgstr "Mesaj Düzenleme"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "10'dan fazla standart yapay zeka sağlayıcısı"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "Hata düzeltmeleri"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
#, fuzzy
msgid "Minor bug fixes"
msgstr "Hata düzeltmeleri"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "Küçük iyileştirmeler"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "Yerel belge okuma ve yükleme performanslarını iyileştir"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "CTRL+Enter ile gönderme seçeneği ekle"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "Kod bloklarını iyileştir"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
msgid "Fix Kokoro TTS"
msgstr "Kokoro TTS düzeltmesi"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "Metinden Konuşmaya'dan emojiyi kaldır"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "API anahtarlarını şifre alanları olarak ayarla"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "Gemini için düşünce desteği ekle"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "Güncellenmiş çeviriler"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "Yeni özellikler eklendi"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr "SearXNG, DuckDuckGo ve Tavily ile web sitesi okuma ve web araması"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "Geliştirilmiş LaTeX oluşturma ve belge yönetimi"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "Yeni Düşünme Pencere Öğesi ve OpenRouter işleyici"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "Groq üzerinde Llama4 için görsel desteği"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "Yeni çeviriler (Geleneksel Çince, Bengalce, Hintçe)"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "Birçok hata düzeltildi, bazı özellikler eklendi!"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "Yeni özellikler ve hata düzeltmeleri için destek"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "Birçok yeni özellik ve hata düzeltmesi eklendi"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "Yeni özellikler ve hata düzeltmeleri eklendi"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr ""
"g4f kütüphanesi sürümlemeyle güncellendi, kullanıcı rehberleri eklendi, "
"uzantı taraması iyileştirildi ve model yönetimi geliştirildi."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"Hata düzeltmeleri ve yeni özellikler uygulandı. Uzantı mimarisini "
"değiştirdik, yeni modeller ekledik ve daha fazla yetenekle birlikte görüş "
"desteği sunduk."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "Kararlı sürüm"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
msgid "Added extension"
msgstr "Uzantı eklendi"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
msgid "Blacklist of commands"
msgstr "Komutların kara listesi"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "Yerelleştirme"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "Yeniden Tasarım"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "Qwersyk"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "Newelle: Gelişmiş sohbet robotunuz"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "yapay zeka;asistan;sohbet;chatgpt;gpt;llm;ollama;"

#~ msgid "max Tokens"
#~ msgstr "maksimum Token"

#~ msgid "Max tokens of the generated text"
#~ msgstr "Oluşturulan metnin maksimum token sayısı"
