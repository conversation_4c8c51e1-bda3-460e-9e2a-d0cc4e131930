msgid ""
msgstr ""
"Project-Id-Version: 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-07-03 09:00+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Chinese Simplified <<EMAIL>>\n"
"Language: zh_Hans\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "API 端点"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr "API 基础 URL，更改此项以使用推理 API"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid "Automatically Serve"
msgstr "自动服务"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"如果 Ollama 未运行，则在需要时自动在后台运行 ollama serve。您可以使用 "
"killall ollama 终止它"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
msgid "Custom Model"
msgstr "自定义模型"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
msgid "Use a custom model"
msgstr "使用自定义模型"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
msgid "Ollama Model"
msgstr "Ollama 模型"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
msgid "Name of the Ollama Model"
msgstr "Ollama 模型名称"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "API 密钥"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
msgid "API Key for "
msgstr "API 密钥，用于 "

#: src/handlers/embeddings/openai_handler.py:38
msgid "API base url, change this to use different APIs"
msgstr "API 基础 URL，更改此项以使用不同的 API"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
msgid "Use Custom Model"
msgstr "使用自定义模型"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "模型"

#: src/handlers/embeddings/openai_handler.py:44
msgid "Name of the Embedding Model to use"
msgstr "要使用的嵌入模型的名称"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
msgid " Model"
msgstr "模型"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
msgid "The API key to use"
msgstr "要使用的 API 密钥"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
msgid "The model to use"
msgstr "要使用的模型"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "最大令牌"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "要生成的最大令牌数"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
msgid "Message Streaming"
msgstr "消息流"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "逐步流式传输消息输出"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "执行命令以获取机器人输出"

#: src/handlers/llm/custom_handler.py:21
#, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr ""
"执行命令以获取机器人响应，{0} 将替换为包含聊天的 JSON 文件，{1} 将替换为系统"
"提示"

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "执行命令以获取机器人的建议"

#: src/handlers/llm/custom_handler.py:22
#, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"执行命令以获取聊天建议，{0} 将替换为包含聊天的 JSON 文件，{1} 将替换为额外提"
"示，{2} 将替换为要生成的建议数量。必须返回一个包含建议字符串的 JSON 数组"

#: src/handlers/llm/gpt4all_handler.py:67
msgid "RAM Required: "
msgstr "所需内存："

#: src/handlers/llm/gpt4all_handler.py:68
msgid "Parameters: "
msgstr "参数："

#: src/handlers/llm/gpt4all_handler.py:69
msgid "Size: "
msgstr "大小："

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
msgid "Model to use"
msgstr "要使用的模型"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
msgid "Name of the model to use"
msgstr "要使用的模型名称"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "Model Manager"
msgstr "模型管理器"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "可用模型列表"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr "更新 G4F"

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "隐私政策"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "打开隐私政策网站"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "启用思考"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr "允许模型进行思考，仅部分模型支持"

#: src/handlers/llm/ollama_handler.py:176
msgid "Add custom model"
msgstr "添加自定义模型"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"通过输入名称:大小将任何模型添加到此列表\n"
"或从 hf.co/username/model 添加任何 gguf 模型"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr "更新 Ollama"

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "API 密钥 (必需)"

#: src/handlers/llm/gemini_handler.py:94
msgid "API key got from ai.google.dev"
msgstr "从 ai.google.dev 获取的 API 密钥"

#: src/handlers/llm/gemini_handler.py:98
msgid "AI Model to use"
msgstr "要使用的 AI 模型"

#: src/handlers/llm/gemini_handler.py:103
msgid "Enable System Prompt"
msgstr "启用系统提示"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr "某些模型不支持系统提示（或开发者指令），如果遇到相关错误，请禁用它"

#: src/handlers/llm/gemini_handler.py:107
msgid "Inject system prompt"
msgstr "注入系统提示"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr "即使模型不支持系统提示，也将提示放在用户消息的顶部"

#: src/handlers/llm/gemini_handler.py:109
msgid "Thinking Settings"
msgstr "思考设置"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "关于思考模型的设置"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr "显示思考过程，如果您的模型不支持，请禁用它"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "启用思考预算"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "是否启用思考预算"

#: src/handlers/llm/gemini_handler.py:112
msgid "Thinking Budget"
msgstr "思考预算"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "思考时间花费多少"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "图像输出"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr "启用图像输出，仅 gemini-2.0-flash-exp 支持"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "启用安全设置"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr "启用 Google 安全设置以避免生成有害内容"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
msgid "Advanced Parameters"
msgstr "高级参数"

#: src/handlers/llm/gemini_handler.py:135
msgid "Enable advanced parameters"
msgstr "启用高级参数"

#: src/handlers/llm/openai_handler.py:81
#, fuzzy
msgid "Include parameters like Top-P, Temperature, etc."
msgstr "包含最大令牌、Top-P、温度等参数"

#: src/handlers/llm/openai_handler.py:84
msgid "Name of the LLM Model to use"
msgstr "要使用的 LLM 模型名称"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "Top-P"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr "一种替代温度采样的方法，称为核采样"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "温度"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr "要使用的采样温度。值越高，输出越随机"

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "频率惩罚"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr "介于 -2.0 和 2.0 之间的数字。正值会降低模型逐字重复相同行的可能性"

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "存在惩罚"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr "介于 -2.0 和 2.0 之间的数字。正值会降低模型谈论新主题的可能性"

#: src/handlers/llm/openai_handler.py:108
#, fuzzy
msgid "Custom Options"
msgstr "自定义提示"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr ""

#: src/handlers/llm/openrouter_handler.py:14
msgid "Provider Sorting"
msgstr "提供商排序"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "根据价格/吞吐量或延迟选择提供商"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "价格"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "吞吐量"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "延迟"

#: src/handlers/llm/openrouter_handler.py:15
msgid "Providers Order"
msgstr "提供商顺序"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr ""
"添加要使用的提供商顺序，名称用逗号分隔。\n"
"留空表示不指定"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "允许回退"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "允许回退到其他提供商"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "索引您的文档"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"索引文档文件夹中的所有文档。每次编辑/创建文档、更改文档分析器或更改嵌入模型"
"时，都必须运行此操作"

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "要执行的命令"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "{0} 将替换为模型完整路径"

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr "Google SR 的 API 密钥，写入“default”以使用默认密钥"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "语言"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "要识别的文本的 IETF 语言"

#: src/handlers/stt/groqsr_handler.py:14
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr "Groq SR 的 API 密钥，写入“default”以使用默认密钥"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "Groq 模型"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "Groq 模型名称"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"指定转录语言。使用 ISO 639-1 语言代码（例如，“en”表示英语，“fr”表示法语等）。"

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "OpenAI 请求的端点"

#: src/handlers/stt/openaisr_handler.py:18
msgid "API Key for OpenAI"
msgstr "OpenAI 的 API 密钥"

#: src/handlers/stt/openaisr_handler.py:25
msgid "Whisper Model"
msgstr "Whisper 模型"

#: src/handlers/stt/openaisr_handler.py:26
msgid "Name of the OpenAI model"
msgstr "OpenAI 模型名称"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"可选：指定转录语言。使用 ISO 639-1 语言代码（例如，“en”表示英语，“fr”表示法语"
"等）。"

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "模型路径"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "VOSK 模型的绝对路径（未解压）"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
msgid "Name of the Whisper model"
msgstr "Whisper 模型名称"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "wit.ai 的服务器访问令牌"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "无法理解音频"

#: src/handlers/stt/whispercpp_handler.py:47
#, fuzzy
msgid "Language of the recognition. For example en, it..."
msgstr "识别语言。"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Model Library"
msgstr "模型库"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Manage Whisper models"
msgstr "管理 Whisper 模型"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "Advanced Settings"
msgstr "高级设置"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "More advanced settings"
msgstr "更多高级设置"

#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature to use"
msgstr "要使用的温度"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt for the recognition"
msgstr "识别提示"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr "用于识别的提示"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Endpoint"
msgstr "端点"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "要使用的服务的自定义端点"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "语音"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
msgid "The voice to use"
msgstr "要使用的语音"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "说明"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr "语音生成的说明。留空以避免此字段"

#: src/handlers/tts/custom_handler.py:17
#, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr "{0} 将替换为文件完整路径，{1} 将替换为文本"

#: src/handlers/tts/elevenlabs_handler.py:11
msgid "API Key for ElevenLabs"
msgstr "ElevenLabs 的 API 密钥"

#: src/handlers/tts/elevenlabs_handler.py:19
msgid "Voice ID to use"
msgstr "要使用的语音 ID"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "稳定性"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "语音的稳定性"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "相似度提升"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "提高整体语音清晰度和说话人相似度"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "风格夸张"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr "如果语音风格需要夸张，建议使用高值"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "选择首选语音"

#: src/handlers/websearch/tavily.py:20
msgid "Token"
msgstr "令牌"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "Tavily API 密钥"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "最大结果数"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "要考虑的结果数量"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "搜索深度"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"搜索深度。高级搜索旨在检索与您的查询最相关的来源和内容片段，而基本搜索则从每"
"个来源提供通用内容片段。基本搜索消耗 1 个 API 信用，高级搜索消耗 2 个 API 信"
"用。"

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "搜索类别"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"搜索类别。新闻对于检索实时更新非常有用，特别是关于主流媒体报道的政治、体育和"
"重大时事。通用适用于更广泛、更通用目的的搜索，可能包括各种来源。"

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "每个来源的块数"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr ""
"从每个源检索的内容块的数量。每个块的最大长度为 500 个字符。仅当搜索深度为高级"
"时可用。"

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "从当前日期回溯包含的天数"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "仅当主题为新闻时可用。"

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "包含答案"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"包含 LLM 为所提供查询生成的答案。基本搜索返回快速答案。高级搜索返回更详细的答"
"案。"

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "包含原始内容"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr "包含每个搜索结果的已清理和解析的 HTML 内容。"

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "包含图片"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr "执行图像搜索并在响应中包含结果。"

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "包含图片描述"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr "启用“包含图片”时，也为每张图片添加描述性文本。"

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "包含域"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr "要在搜索结果中特别包含的域列表。"

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "排除域"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr "要从搜索结果中特别排除的域列表。"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "区域"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "搜索结果的区域"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "设置"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "配置文件名称"

#: src/ui/profile.py:58
msgid "Copied Settings"
msgstr "复制的设置"

#: src/ui/profile.py:58
msgid "Settings that will be copied to the new profile"
msgstr "将复制到新配置文件的设置"

#: src/ui/profile.py:70
msgid "Create Profile"
msgstr "创建配置文件"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "导入配置文件"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
msgid "Edit Profile"
msgstr "编辑配置文件"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "导出配置文件"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "导出密码"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "同时导出类似密码的字段"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "导出头像"

#: src/ui/profile.py:89
msgid "Also export the profile picture"
msgstr "同时导出头像"

#: src/ui/profile.py:109 src/ui/explorer.py:692
msgid "Create"
msgstr "创建"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "应用"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr "当前配置文件的设置将被复制到新配置文件中"

#: src/ui/profile.py:122 src/ui/profile.py:128
msgid "Newelle Profiles"
msgstr "Newelle 配置文件"

#: src/ui/profile.py:123
msgid "Export"
msgstr "导出"

#: src/ui/profile.py:129
msgid "Import"
msgstr "导入"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "设置头像"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "线程编辑"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "没有线程正在运行"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "线程号："

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "选择配置文件"

#: src/ui/widgets/profilerow.py:53
msgid "Delete Profile"
msgstr "删除配置文件"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "思考"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "展开查看详情"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "思考中..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr "LLM 正在思考中... 展开查看思考过程"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "没有记录思考过程"

#: src/ui/widgets/tipscarousel.py:41
msgid "Newelle Tips"
msgstr "Newelle 提示"

#: src/ui/explorer.py:192
msgid "Folder is Empty"
msgstr "文件夹为空"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr "文件未找到"

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "在新标签页中打开"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "在集成编辑器中打开"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "在文件管理器中打开"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
msgid "Rename"
msgstr "重命名"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "删除"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "复制完整路径"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
msgid "Failed to open file manager"
msgstr "未能打开文件管理器"

#: src/ui/explorer.py:436
msgid "New name:"
msgstr "新名称："

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "取消"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "重命名成功"

#: src/ui/explorer.py:476
#, python-brace-format
msgid "Failed to rename: {}"
msgstr "重命名失败：{}"

#: src/ui/explorer.py:497
msgid "Delete File?"
msgstr "删除文件？"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "您确定要删除“{}”吗？"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "删除成功"

#: src/ui/explorer.py:525
#, python-brace-format
msgid "Failed to delete: {}"
msgstr "删除失败：{}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "路径已复制到剪贴板"

#: src/ui/explorer.py:542
msgid "Failed to copy path"
msgstr "复制路径失败"

#: src/ui/explorer.py:580
msgid "Create new folder"
msgstr "创建新文件夹"

#: src/ui/explorer.py:583
msgid "Create new file"
msgstr "创建新文件"

#: src/ui/explorer.py:586
msgid "Open Terminal Here"
msgstr "在此处打开终端"

#: src/ui/explorer.py:640
msgid "Create New Folder"
msgstr "创建新文件夹"

#: src/ui/explorer.py:640
msgid "Folder name:"
msgstr "文件夹名称："

#: src/ui/explorer.py:644
msgid "Create New File"
msgstr "创建新文件"

#: src/ui/explorer.py:644
msgid "File name:"
msgstr "文件名称："

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "文件夹创建成功"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "文件创建成功"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "已存在同名文件或文件夹"

#: src/ui/explorer.py:728
msgid "folder"
msgstr "文件夹"

#: src/ui/explorer.py:728
msgid "file"
msgstr "文件"

#: src/ui/explorer.py:730
#, python-brace-format
msgid "Failed to create {}: {}"
msgstr "创建 {} 失败：{}"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "帮助"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "快捷方式"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "重新加载聊天"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "重新加载文件夹"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "新标签页"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "粘贴图片"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "聚焦消息框"

#: src/ui/shortcuts.py:18
msgid "Start/stop recording"
msgstr "开始/停止录音"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "保存"

#: src/ui/shortcuts.py:20
msgid "Stop TTS"
msgstr "停止文本转语音"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr "放大"

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr "缩小"

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr "程序输出监视器"

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr "清除输出"

#: src/ui/stdout_monitor.py:61
msgid "Start/Stop monitoring"
msgstr "开始/停止监控"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr "监控：活动"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr "监控：已停止"

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr "行数：{}"

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr "行数：0"

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "扩展"

#: src/ui/extension.py:50
msgid "Installed Extensions"
msgstr "已安装的扩展"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "扩展用户指南"

#: src/ui/extension.py:89
msgid "Download new Extensions"
msgstr "下载新扩展"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "从文件安装扩展..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "Newelle"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "聊天已在迷你窗口中打开"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "欢迎使用 Newelle"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "您的终极虚拟助手。"

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "Github 页面"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "选择您最喜欢的 AI 语言模型"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"Newelle 可以与多种模型和提供商一起使用！\n"
"<b>注意：强烈建议阅读 LLM 指南页面</b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "LLM 指南"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "与您的文档聊天"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr ""
"Newelle 可以从您在聊天中发送的文档或您自己的文件中检索相关信息！与您的查询相"
"关的信息将发送给 LLM。"

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "命令虚拟化"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"Newelle 可用于在您的系统上运行命令，但请注意您运行的内容！<b>LLM 不受我们控"
"制，因此它可能会生成恶意代码！</b>\n"
"默认情况下，您的命令将<b>在 Flatpak 环境中虚拟化</b>，但请注意！"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "您可以使用扩展来扩展 Newelle 的功能！"

#: src/ui/presentation.py:136
msgid "Download extensions"
msgstr "下载扩展"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "权限错误"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr "Newelle 没有足够的权限在您的系统上运行命令。"

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "开始使用应用程序"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "开始聊天"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "通用"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "LLM"

#: src/ui/settings.py:49 src/constants.py:528
msgid "Prompts"
msgstr "提示词"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "知识"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "语言模型"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "其他 LLM"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "其他可用的 LLM 提供商"

#: src/ui/settings.py:73
msgid "Advanced LLM Settings"
msgstr "高级 LLM 设置"

#: src/ui/settings.py:77
msgid "Secondary Language Model"
msgstr "辅助语言模型"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr "用于辅助任务的模型，例如建议、聊天名称和记忆生成"

#: src/ui/settings.py:94
msgid "Embedding Model"
msgstr "嵌入模型"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"嵌入用于将文本转换为向量。由长期记忆和 RAG 使用。更改它可能需要您重新索引文档"
"或重置记忆。"

#: src/ui/settings.py:105 src/window.py:647
msgid "Long Term Memory"
msgstr "长期记忆"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "保留旧对话的记忆"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "网络搜索"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "在网络上搜索信息"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "文本转语音程序"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "选择要使用的文本转语音"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "语音转文本引擎"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "选择您想要的语音识别引擎"

#: src/ui/settings.py:150
msgid "Automatic Speech To Text"
msgstr "自动语音转文本"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr "在文本/文本转语音结束时自动重新启动语音转文本"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "提示控制"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "界面"

#: src/ui/settings.py:162
msgid "Interface Size"
msgstr "界面大小"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "调整界面大小"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "编辑器配色方案"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "更改编辑器和代码块的配色方案"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "隐藏文件"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "显示隐藏文件"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "按回车发送"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"如果启用，消息将通过回车键发送，换行请使用 CTRL+回车。如果禁用，消息将通过 "
"SHIFT+回车发送，换行请使用回车"

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "从历史记录中删除思考过程"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr "不发送旧的思考块以减少推理模型的令牌使用"

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "显示 LaTeX"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "在聊天中显示 LaTeX 公式"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "反转聊天顺序"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr "在聊天列表中显示最新聊天（更改聊天以应用）"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "自动生成聊天名称"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr "在前两条消息后自动生成聊天名称"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "建议数量"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "发送到聊天的消息建议数量"

#: src/ui/settings.py:224
msgid "Username"
msgstr "用户名"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr ""
"更改消息前显示的标签\n"
"默认情况下，此信息不会发送到 LLM\n"
"您可以使用 {USER} 变量将其添加到提示中"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "神经网络控制"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "在虚拟机中运行命令"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "外部终端"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr "选择运行控制台命令的外部终端"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "程序内存"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "程序记住聊天的时长"

#: src/ui/settings.py:266
msgid "Developer"
msgstr "开发者"

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr "实时监视程序输出，有助于调试和查看下载进度"

#: src/ui/settings.py:270
msgid "Open"
msgstr "打开"

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr "删除 pip 路径"

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr "删除已安装的额外依赖项"

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr ""

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr ""

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "自动运行命令"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "机器人将编写的命令将自动运行"

#: src/ui/settings.py:313
msgid "Max number of commands"
msgstr "最大命令数"

#: src/ui/settings.py:313
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr "在单个用户请求后，机器人将编写的最大命令数"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "浏览器"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "浏览器设置"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "使用外部浏览器"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr "使用外部浏览器打开链接，而不是集成浏览器"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "持久化浏览器会话"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr "在重新启动之间持久化浏览器会话。关闭此功能需要重新启动程序"

#: src/ui/settings.py:361
msgid "Delete browser data"
msgstr "删除浏览器数据"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "删除浏览器会话和数据"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "初始浏览器页面"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "浏览器将启动的页面"

#: src/ui/settings.py:375
msgid "Search string"
msgstr "搜索字符串"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr "浏览器中使用的搜索字符串，%s 被替换为查询"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "文档来源 (RAG)"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "在响应中包含您文档中的内容"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "文档分析器"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr "文档分析器使用多种技术从您的文档中提取相关信息"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "如果不支持，则读取文档"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr ""
"如果 LLM 不支持读取文档，则通过您的文档分析器将聊天中发送的文档相关信息提供"
"给 LLM。"

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "RAG 的最大令牌数"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"用于 RAG 的最大令牌数。如果文档不超过此令牌计数，则将所有文档转储到上下文中"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "文档文件夹"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr ""
"将您要查询的文档放入文档文件夹。如果启用此选项，文档分析器将找到其中的相关信"
"息"

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr "将所有要索引的文档放入此文件夹"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "静音阈值"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr "静音阈值（秒），被视为静音的音量百分比"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "静音时间"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr "录音自动停止前的静音时间（秒）"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "权限不足"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr "Newelle 没有足够的权限在您的系统上运行命令，请运行以下命令"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "知道了"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr "Pip 路径已删除"

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr "pip 路径已删除，您现在可以重新安装依赖项。此操作需要重新启动应用程序。"

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "Newelle 演示 API"

#: src/constants.py:26
msgid "GPT4Free"
msgstr "GPT4Free"

#: src/constants.py:34
msgid "Local Model"
msgstr "本地模型"

#: src/constants.py:35
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr "不支持 GPU，请改用 OLLAMA。在本地运行 LLM 模型，更注重隐私但速度较慢"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "Ollama 实例"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "在您的硬件上轻松运行多个 LLM 模型"

#: src/constants.py:47
msgid "Groq"
msgstr "Groq"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "Google Gemini API"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "OpenAI API"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr "OpenAI API。支持自定义端点。用于自定义提供商"

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "Anthropic Claude"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr "Anthropic Claude 模型的官方 API，支持图像和文件，需要 API 密钥"

#: src/constants.py:73
msgid "Mistral"
msgstr "Mistral"

#: src/constants.py:74
msgid "Mistral API"
msgstr "Mistral API"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "Openrouter.ai API，支持多种模型"

#: src/constants.py:87
msgid "Deepseek"
msgstr "Deepseek"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "Deepseek API，最强大的开源模型"

#: src/constants.py:94 src/constants.py:203
msgid "Custom Command"
msgstr "自定义命令"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "使用自定义命令的输出"

#: src/constants.py:104
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr "离线工作。用 C++ 编写的优化版 Whisper 实现"

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "离线工作。仅支持英语"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "Google 语音识别"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "Google 在线语音识别"

#: src/constants.py:124
msgid "Groq Speech Recognition"
msgstr "Groq 语音识别"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit AI"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr "wit.ai 免费语音识别 API（网站上选择语言）"

#: src/constants.py:137
msgid "Vosk API"
msgstr "Vosk API"

#: src/constants.py:138
msgid "Works Offline"
msgstr "离线工作"

#: src/constants.py:144
msgid "Whisper API"
msgstr "Whisper API"

#: src/constants.py:145
msgid "Uses OpenAI Whisper API"
msgstr "使用 OpenAI Whisper API"

#: src/constants.py:151
msgid "Custom command"
msgstr "自定义命令"

#: src/constants.py:152
msgid "Runs a custom command"
msgstr "运行自定义命令"

#: src/constants.py:161
msgid "Google TTS"
msgstr "Google 文本转语音"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "Google 的文本转语音"

#: src/constants.py:167
msgid "Kokoro TTS"
msgstr "Kokoro 文本转语音"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr "轻量、快速的开源 TTS 引擎。约 3GB 依赖，400MB 模型"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "ElevenLabs 文本转语音"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "自然发音的文本转语音"

#: src/constants.py:179 src/constants.py:180
msgid "OpenAI TTS"
msgstr "OpenAI 文本转语音"

#: src/constants.py:185
msgid "Groq TTS"
msgstr "Groq 文本转语音"

#: src/constants.py:186
msgid "Groq TTS API"
msgstr "Groq TTS API"

#: src/constants.py:191 src/constants.py:192
msgid "Custom OpenAI TTS"
msgstr "自定义 OpenAI 文本转语音"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "Espeak 文本转语音"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "离线文本转语音"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr "使用自定义命令作为 TTS，{0} 将替换为文本"

#: src/constants.py:212
msgid "WordLlama"
msgstr "WordLlama"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr "基于 llama 的轻量级本地嵌入模型。离线工作，资源占用极低"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "Ollama 嵌入"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr "使用 Ollama 模型进行嵌入。离线工作，资源占用极低"

#: src/constants.py:231
msgid "Use Google Gemini API to get embeddings"
msgstr "使用 Google Gemini API 获取嵌入"

#: src/constants.py:239
msgid "User Summary"
msgstr "用户摘要"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "生成用户对话的摘要"

#: src/constants.py:245
msgid "Memoripy"
msgstr "记忆库"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"使用上下文记忆检索、记忆衰减、概念提取和其他高级技术从之前的对话中提取消息。"
"每条消息执行 1 次 LLM 调用。"

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "用户摘要 + 记忆库"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "同时使用两种技术实现长期记忆"

#: src/constants.py:260
msgid "Document reader"
msgstr "文档阅读器"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr ""
"经典的 RAG 方法 - 将文档分块并嵌入，然后与查询进行比较并返回最相关的文档"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr "SearXNG - 私有且可自托管的搜索引擎"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "DuckDuckGo 搜索"

#: src/constants.py:281
msgid "Tavily"
msgstr "Tavily"

#: src/constants.py:282
msgid "Tavily search"
msgstr "Tavily 搜索"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "有用的助手"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr "通用提示，用于增强 LLM 答案并提供更多上下文"

#: src/constants.py:384
msgid "Console access"
msgstr "控制台访问"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "程序是否可以在计算机上运行终端命令"

#: src/constants.py:392
msgid "Current directory"
msgstr "当前目录"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "当前目录是什么"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "允许 LLM 在互联网上搜索"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "基本功能"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "显示表格和代码（*可以在没有它的情况下工作）"

#: src/constants.py:419
msgid "Graphs access"
msgstr "图表访问"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "程序是否可以显示图表"

#: src/constants.py:428
msgid "Show image"
msgstr "显示图片"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "在聊天中显示图片"

#: src/constants.py:437
msgid "Custom Prompt"
msgstr "自定义提示"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "添加您自己的自定义提示"

#: src/constants.py:480
msgid "LLM and Secondary LLM settings"
msgstr "LLM 和辅助 LLM 设置"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "文本转语音"

#: src/constants.py:485
msgid "Text to Speech settings"
msgstr "文本转语音设置"

#: src/constants.py:488
msgid "STT"
msgstr "语音转文本"

#: src/constants.py:490
msgid "Speech to Text settings"
msgstr "语音转文本设置"

#: src/constants.py:493
msgid "Embedding"
msgstr "嵌入"

#: src/constants.py:495
msgid "Embedding settings"
msgstr "嵌入设置"

#: src/constants.py:498
msgid "Memory"
msgstr "记忆"

#: src/constants.py:500
msgid "Memory settings"
msgstr "记忆设置"

#: src/constants.py:503
msgid "Websearch"
msgstr "网络搜索"

#: src/constants.py:505
msgid "Websearch settings"
msgstr "网络搜索设置"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
msgid "Document analyzer settings"
msgstr "文档分析器设置"

#: src/constants.py:515
msgid "Extensions settings"
msgstr "扩展设置"

#: src/constants.py:518
msgid "Inteface"
msgstr "界面"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr "界面设置、隐藏文件、反向顺序、缩放..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr "通用设置、虚拟化、建议、记忆长度、自动生成聊天名称、当前文件夹..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr "提示设置、自定义额外提示、自定义提示..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "聊天 "

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "终端线程仍在后台运行"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "当您关闭窗口时，它们将自动终止"

#: src/main.py:210
msgid "Close"
msgstr "关闭"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "聊天已重启"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "文件夹已重启"

#: src/main.py:254
msgid "Chat is created"
msgstr "聊天已创建"

#: src/window.py:120
msgid "Keyboard shorcuts"
msgstr "键盘快捷键"

#: src/window.py:121
msgid "About"
msgstr "关于"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "聊天"

#: src/window.py:170
msgid "History"
msgstr "历史记录"

#: src/window.py:191
msgid "Create a chat"
msgstr "创建聊天"

#: src/window.py:196
msgid "Chats"
msgstr "聊天"

#: src/window.py:267
msgid " Stop"
msgstr "停止"

#: src/window.py:282
msgid " Clear"
msgstr "清除"

#: src/window.py:297
msgid " Continue"
msgstr "继续"

#: src/window.py:310
msgid " Regenerate"
msgstr "重新生成"

#: src/window.py:376
msgid "Send a message..."
msgstr "发送消息..."

#: src/window.py:467
msgid "Explorer Tab"
msgstr "文件管理器标签页"

#: src/window.py:468
msgid "Terminal Tab"
msgstr "终端标签页"

#: src/window.py:469
msgid "Browser Tab"
msgstr "浏览器标签页"

#: src/window.py:589
msgid "Ask about a website"
msgstr "询问关于网站的信息"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr "在聊天中输入 #https://website.com 询问关于网站的信息"

#: src/window.py:590
msgid "Check out our Extensions!"
msgstr "查看我们的扩展！"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "我们有许多用于不同功能的扩展。快来查看吧！"

#: src/window.py:591
msgid "Chat with documents!"
msgstr "与文档聊天！"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr "将您的文档添加到文档文件夹中，然后使用其中包含的信息进行聊天！"

#: src/window.py:592
msgid "Surf the web!"
msgstr "网上冲浪！"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr "启用网络搜索，允许 LLM 上网并提供最新答案"

#: src/window.py:593
msgid "Mini Window"
msgstr "迷你窗口"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "使用迷你窗口模式随时提问"

#: src/window.py:594
msgid "Text to Speech"
msgstr "文本转语音"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr "Newelle 支持文本转语音！在设置中启用它"

#: src/window.py:595
msgid "Keyboard Shortcuts"
msgstr "键盘快捷键"

#: src/window.py:595
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "使用键盘快捷键控制 Newelle"

#: src/window.py:596
msgid "Prompt Control"
msgstr "提示控制"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr "Newelle 提供 100% 的提示控制。根据您的使用情况调整提示。"

#: src/window.py:597
msgid "Thread Editing"
msgstr "线程编辑"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "检查您从 Newelle 运行的程序和进程"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr "可编程提示"

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr "您可以为 Newelle 添加动态提示，并带有条件和概率"

#: src/window.py:605
msgid "New Chat"
msgstr "新聊天"

#: src/window.py:623
msgid "Provider Errror"
msgstr "提供商错误"

#: src/window.py:646
msgid "Local Documents"
msgstr "本地文档"

#: src/window.py:650
msgid "Web search"
msgstr "网络搜索"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "此提供商没有模型列表"

#: src/window.py:901
msgid " Models"
msgstr "模型"

#: src/window.py:904
msgid "Search Models..."
msgstr "搜索模型..."

#: src/window.py:1132
msgid "Create new profile"
msgstr "创建新配置文件"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "无法识别您的语音"

#: src/window.py:1303
msgid "Images"
msgstr "图片"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "LLM 支持的文件"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "RAG 支持的文件"

#: src/window.py:1333
msgid "Supported Files"
msgstr "支持的文件"

#: src/window.py:1337
msgid "All Files"
msgstr "所有文件"

#: src/window.py:1343
msgid "Attach file"
msgstr "附件"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "程序完成前无法发送文件"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "文件无法识别"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "您无法再继续此消息。"

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "您无法再重新生成此消息。"

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "聊天已清除"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "消息已取消并从历史记录中删除"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr "程序完成前无法发送消息"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr "程序运行时无法编辑消息。"

#: src/window.py:3080
msgid "Prompt content"
msgstr "提示内容"

#: src/window.py:3339
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"神经网络可以访问您的计算机和此聊天中的任何数据，并可以运行命令，请小心，我们"
"不对神经网络负责。请勿分享任何敏感信息。"

#: src/window.py:3368
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"神经网络可以访问此聊天中的任何数据，请小心，我们不对神经网络负责。请勿分享任"
"何敏感信息。"

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "错误的文件夹路径"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "线程尚未完成，线程号："

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "未能打开文件夹"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "聊天是空的"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr ""
"通过自定义扩展和新的 AI 模块训练 Newelle 完成更多任务，为您的聊天机器人提供无"
"限可能。"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "AI 聊天机器人"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "快速配置文件选择"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
msgid "Message Editing"
msgstr "消息编辑"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "超过 10 家标准 AI 提供商"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "错误修复"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
#, fuzzy
msgid "Minor bug fixes"
msgstr "错误修复"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "小改进"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "改善本地文档阅读和加载性能"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "添加使用 CTRL+Enter 发送的选项"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "改进代码块"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
msgid "Fix Kokoro TTS"
msgstr "修复 Kokoro TTS"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "从 TTS 中移除表情符号"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "将 API 密钥设置为密码字段"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "为 Gemini 添加思考支持"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "更新了翻译"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "新增功能"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr "使用 SearXNG、DuckDuckGo 和 Tavily 进行网站阅读和网络搜索"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "改进了 LaTeX 渲染和文档管理"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "新增思考小部件和 OpenRouter 处理程序"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "Groq 上 Llama4 的视觉支持"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "新翻译（繁体中文、孟加拉语、印地语）"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "修复了许多错误，并添加了一些功能！"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "支持新功能和错误修复"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "新增了许多功能并修复了错误"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "新增功能并修复了错误"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr ""
"更新了 g4f 库并加入了版本控制，增加了用户指南，改进了扩展浏览，并增强了模型处"
"理。"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"已实施错误修复和新功能。我们修改了扩展架构，添加了新模型，并引入了视觉支持，"
"以及更多功能。"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "稳定版"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
msgid "Added extension"
msgstr "添加了扩展"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
msgid "Blacklist of commands"
msgstr "命令黑名单"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "本地化"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "重新设计"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "Qwersyk"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "Newelle：您的智能聊天机器人"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "ai;助手;聊天;chatgpt;gpt;llm;ollama;"

#~ msgid "max Tokens"
#~ msgstr "最大令牌"

#~ msgid "Max tokens of the generated text"
#~ msgstr "生成文本的最大令牌数"
