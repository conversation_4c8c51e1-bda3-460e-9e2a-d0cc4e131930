msgid ""
msgstr ""
"Project-Id-Version: 1.0\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2025-08-11 01:45+0800\n"
"PO-Revision-Date: 2025-07-03 09:00+0200\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Hungarian <<EMAIL>>\n"
"Language: hu\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/embeddings/openai_handler.py:38
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
#: src/handlers/stt/openaisr_handler.py:10
msgid "API Endpoint"
msgstr "API végpont"

#: src/handlers/embeddings/ollama_handler.py:32
#: src/handlers/llm/ollama_handler.py:150 src/handlers/llm/openai_handler.py:75
msgid "API base url, change this to use interference APIs"
msgstr "API alap URL, ezt módosítva használhatók az interferencia API-k"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid "Automatically Serve"
msgstr "Automatikus kiszolgálás"

#: src/handlers/embeddings/ollama_handler.py:33
#: src/handlers/llm/ollama_handler.py:151
msgid ""
"Automatically run ollama serve in background when needed if it's not "
"running. You can kill it with killall ollama"
msgstr ""
"Automatikusan futtatja az ollama kiszolgálót a háttérben, ha szükséges és "
"nem fut. A „killall ollama” paranccsal leállíthatja."

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/llm/ollama_handler.py:153
msgid "Custom Model"
msgstr "Egyéni modell"

#: src/handlers/embeddings/ollama_handler.py:34
#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/claude_handler.py:85 src/handlers/llm/ollama_handler.py:153
#: src/handlers/llm/openai_handler.py:78
msgid "Use a custom model"
msgstr "Egyéni modell használata"

#: src/handlers/embeddings/ollama_handler.py:40
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:159
#: src/handlers/llm/ollama_handler.py:168
msgid "Ollama Model"
msgstr "Ollama modell"

#: src/handlers/embeddings/ollama_handler.py:41
#: src/handlers/embeddings/ollama_handler.py:49
#: src/handlers/llm/ollama_handler.py:160
#: src/handlers/llm/ollama_handler.py:168
msgid "Name of the Ollama Model"
msgstr "Az Ollama modell neve"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/claude_handler.py:84 src/handlers/llm/openai_handler.py:72
#: src/handlers/stt/googlesr_handler.py:13
#: src/handlers/stt/groqsr_handler.py:13
#: src/handlers/stt/openaisr_handler.py:17 src/handlers/stt/witai_handler.py:12
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
#: src/handlers/tts/elevenlabs_handler.py:10
msgid "API Key"
msgstr "API kulcs"

#: src/handlers/embeddings/openai_handler.py:35
#: src/handlers/llm/openai_handler.py:72
msgid "API Key for "
msgstr "API kulcs ehhez: "

#: src/handlers/embeddings/openai_handler.py:38
msgid "API base url, change this to use different APIs"
msgstr "API alap URL, ezt módosítva használhatók más API-k"

#: src/handlers/embeddings/openai_handler.py:41
#: src/handlers/llm/openai_handler.py:78
msgid "Use Custom Model"
msgstr "Egyéni modell használata"

#: src/handlers/embeddings/openai_handler.py:44
#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/llm/g4f_handler.py:44 src/handlers/llm/gemini_handler.py:97
#: src/handlers/llm/openai_handler.py:84 src/handlers/stt/whisper_handler.py:15
#: src/handlers/stt/whispercpp_handler.py:40
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
#: src/handlers/tts/elevenlabs_handler.py:25
msgid "Model"
msgstr "Modell"

#: src/handlers/embeddings/openai_handler.py:44
msgid "Name of the Embedding Model to use"
msgstr "A használni kívánt beágyazási modell neve"

#: src/handlers/embeddings/openai_handler.py:51
#: src/handlers/llm/openai_handler.py:91
msgid " Model"
msgstr " Modell"

#: src/handlers/llm/claude_handler.py:84
#: src/handlers/tts/custom_openai_tts.py:18
#: src/handlers/tts/groq_tts_handler.py:32
#: src/handlers/tts/openai_tts_handler.py:18
msgid "The API key to use"
msgstr "A használni kívánt API kulcs"

#: src/handlers/llm/claude_handler.py:89 src/handlers/llm/claude_handler.py:93
#: src/handlers/tts/custom_openai_tts.py:20
#: src/handlers/tts/groq_tts_handler.py:34
#: src/handlers/tts/openai_tts_handler.py:20
msgid "The model to use"
msgstr "A használni kívánt modell"

#: src/handlers/llm/claude_handler.py:96
msgid "Max Tokens"
msgstr "Max tokenek"

#: src/handlers/llm/claude_handler.py:96
msgid "The maximum number of tokens to generate"
msgstr "A generálandó tokenek maximális száma"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:27
#: src/handlers/llm/gemini_handler.py:119 src/utility/util.py:136
msgid "Message Streaming"
msgstr "Üzenetfolyam"

#: src/handlers/llm/custom_handler.py:20
#: src/handlers/llm/gpt4all_handler.py:153
#: src/handlers/llm/newelle_handler.py:28
#: src/handlers/llm/gemini_handler.py:120 src/utility/util.py:137
msgid "Gradually stream message output"
msgstr "Fokozatosan streameli az üzenetkimenetet"

#: src/handlers/llm/custom_handler.py:21
msgid "Command to execute to get bot output"
msgstr "Futtatandó parancs a bot kimenetének lekéréséhez"

#: src/handlers/llm/custom_handler.py:21
#, python-brace-format
msgid ""
"Command to execute to get bot response, {0} will be replaced with a JSON "
"file containing the chat, {1} with the system prompt"
msgstr ""
"Futtatandó parancs a bot válaszának lekéréséhez, a {0} helyére egy JSON fájl "
"kerül a csevegéssel, az {1} helyére pedig a rendszerüzenet."

#: src/handlers/llm/custom_handler.py:22
msgid "Command to execute to get bot's suggestions"
msgstr "Futtatandó parancs a bot javaslatainak lekéréséhez"

#: src/handlers/llm/custom_handler.py:22
#, python-brace-format
msgid ""
"Command to execute to get chat suggestions, {0} will be replaced with a JSON "
"file containing the chat, {1} with the extra prompts, {2} with the numer of "
"suggestions to generate. Must return a JSON array containing the suggestions "
"as strings"
msgstr ""
"Futtatandó parancs a csevegési javaslatok lekéréséhez, a {0} helyére egy "
"JSON fájl kerül a csevegéssel, az {1} helyére a további üzenetekkel, a {2} "
"helyére pedig a generálandó javaslatok számával. Egy JSON tömböt kell "
"visszaadnia, amely a javaslatokat karakterláncként tartalmazza."

#: src/handlers/llm/gpt4all_handler.py:67
msgid "RAM Required: "
msgstr "Szükséges RAM: "

#: src/handlers/llm/gpt4all_handler.py:68
msgid "Parameters: "
msgstr "Paraméterek: "

#: src/handlers/llm/gpt4all_handler.py:69
msgid "Size: "
msgstr "Méret: "

#: src/handlers/llm/gpt4all_handler.py:154 src/handlers/llm/g4f_handler.py:44
msgid "Model to use"
msgstr "Használandó modell"

#: src/handlers/llm/gpt4all_handler.py:154
#: src/handlers/tts/elevenlabs_handler.py:26
msgid "Name of the model to use"
msgstr "A használandó modell neve"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "Model Manager"
msgstr "Modellkezelő"

#: src/handlers/llm/gpt4all_handler.py:155
#: src/handlers/llm/ollama_handler.py:172
msgid "List of models available"
msgstr "Elérhető modellek listája"

#: src/handlers/llm/g4f_handler.py:45
msgid "Update G4F"
msgstr "G4F frissítése"

#: src/handlers/llm/newelle_handler.py:18
#: src/handlers/llm/gemini_handler.py:131
#: src/handlers/llm/openai_handler.py:112
msgid "Privacy Policy"
msgstr "Adatvédelmi irányelvek"

#: src/handlers/llm/newelle_handler.py:19
#: src/handlers/llm/gemini_handler.py:132
#: src/handlers/llm/openai_handler.py:112
msgid "Open privacy policy website"
msgstr "Adatvédelmi irányelvek webhelyének megnyitása"

#: src/handlers/llm/ollama_handler.py:152
#: src/handlers/llm/gemini_handler.py:110
msgid "Enable Thinking"
msgstr "Gondolkodás engedélyezése"

#: src/handlers/llm/ollama_handler.py:152
msgid "Allow thinking in the model, only some models are supported"
msgstr "Gondolkodás engedélyezése a modellben, csak néhány modell támogatott"

#: src/handlers/llm/ollama_handler.py:176
msgid "Add custom model"
msgstr "Egyéni modell hozzáadása"

#: src/handlers/llm/ollama_handler.py:177
msgid ""
"Add any model to this list by putting name:size\n"
"Or any gguf from hf with hf.co/username/model"
msgstr ""
"Adjon hozzá bármilyen modellt ehhez a listához név:méret formátumban\n"
"Vagy bármely gguf modellt a hf-ről a hf.co/felhasználónév/modell címen"

#: src/handlers/llm/ollama_handler.py:187
msgid "Update Ollama"
msgstr "Ollama frissítése"

#: src/handlers/llm/gemini_handler.py:94
msgid "API Key (required)"
msgstr "API kulcs (kötelező)"

#: src/handlers/llm/gemini_handler.py:94
msgid "API key got from ai.google.dev"
msgstr "Az ai.google.dev-ről szerzett API kulcs"

#: src/handlers/llm/gemini_handler.py:98
msgid "AI Model to use"
msgstr "Használandó AI modell"

#: src/handlers/llm/gemini_handler.py:103
msgid "Enable System Prompt"
msgstr "Rendszerüzenetek engedélyezése"

#: src/handlers/llm/gemini_handler.py:103
msgid ""
"Some models don't support system prompt (or developers instructions), "
"disable it if you get errors about it"
msgstr ""
"Néhány modell nem támogatja a rendszerüzeneteket (vagy a fejlesztői "
"utasításokat), kapcsolja ki, ha hibákat tapasztal."

#: src/handlers/llm/gemini_handler.py:107
msgid "Inject system prompt"
msgstr "Rendszerüzenet beillesztése"

#: src/handlers/llm/gemini_handler.py:107
msgid ""
"Even if the model doesn't support system prompts, put the prompts on top of "
"the user message"
msgstr ""
"Még ha a modell nem is támogatja a rendszerüzeneteket, tegye az üzeneteket a "
"felhasználói üzenet fölé."

#: src/handlers/llm/gemini_handler.py:109
msgid "Thinking Settings"
msgstr "Gondolkodási beállítások"

#: src/handlers/llm/gemini_handler.py:109
msgid "Settings about thinking models"
msgstr "A gondolkodási modellekre vonatkozó beállítások"

#: src/handlers/llm/gemini_handler.py:110
msgid "Show thinking, disable it if your model does not support it"
msgstr "Gondolkodás megjelenítése, kapcsolja ki, ha a modellje nem támogatja"

#: src/handlers/llm/gemini_handler.py:111
msgid "Enable Thinking Budget"
msgstr "Gondolkodási költségvetés engedélyezése"

#: src/handlers/llm/gemini_handler.py:111
msgid "If to enable thinking budget"
msgstr "Gondolkodási költségvetés engedélyezése"

#: src/handlers/llm/gemini_handler.py:112
msgid "Thinking Budget"
msgstr "Gondolkodási költségvetés"

#: src/handlers/llm/gemini_handler.py:112
msgid "How much time to spend thinking"
msgstr "Mennyi időt fordítson a gondolkodásra"

#: src/handlers/llm/gemini_handler.py:116
msgid "Image Output"
msgstr "Képek kimenet"

#: src/handlers/llm/gemini_handler.py:116
msgid "Enable image output, only supported by gemini-2.0-flash-exp"
msgstr "Képkimenet engedélyezése, csak a gemini-2.0-flash-exp támogatja"

#: src/handlers/llm/gemini_handler.py:125
msgid "Enable safety settings"
msgstr "Biztonsági beállítások engedélyezése"

#: src/handlers/llm/gemini_handler.py:126
msgid "Enable google safety settings to avoid generating harmful content"
msgstr ""
"Engedélyezze a Google biztonsági beállításait a káros tartalom generálásának "
"elkerülése érdekében"

#: src/handlers/llm/gemini_handler.py:135 src/handlers/llm/openai_handler.py:81
msgid "Advanced Parameters"
msgstr "Haladó paraméterek"

#: src/handlers/llm/gemini_handler.py:135
msgid "Enable advanced parameters"
msgstr "Haladó paraméterek engedélyezése"

#: src/handlers/llm/openai_handler.py:81
#, fuzzy
msgid "Include parameters like Top-P, Temperature, etc."
msgstr ""
"Tartalmazza az olyan paramétereket, mint a Max Tokens, Top-P, Temperature, "
"stb."

#: src/handlers/llm/openai_handler.py:84
msgid "Name of the LLM Model to use"
msgstr "A használni kívánt LLM modell neve"

#: src/handlers/llm/openai_handler.py:103
msgid "Top-P"
msgstr "Top-P"

#: src/handlers/llm/openai_handler.py:103
msgid "An alternative to sampling with temperature, called nucleus sampling"
msgstr ""
"A hőmérséklettel történő mintavételezés alternatívája, az úgynevezett "
"nucleus mintavételezés"

#: src/handlers/llm/openai_handler.py:104
#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature"
msgstr "Hőmérséklet"

#: src/handlers/llm/openai_handler.py:104
msgid ""
"What sampling temperature to use. Higher values will make the output more "
"random"
msgstr ""
"Milyen mintavételezési hőmérsékletet használjon. Magasabb értékek "
"véletlenszerűbbé teszik a kimenetet."

#: src/handlers/llm/openai_handler.py:105
msgid "Frequency Penalty"
msgstr "Frekvencia büntetés"

#: src/handlers/llm/openai_handler.py:105
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to repeat the same line verbatim"
msgstr ""
"Szám -2,0 és 2,0 között. A pozitív értékek csökkentik annak valószínűségét, "
"hogy a modell szó szerint megismételje ugyanazt a sort."

#: src/handlers/llm/openai_handler.py:106
msgid "Presence Penalty"
msgstr "Jelenlét büntetés"

#: src/handlers/llm/openai_handler.py:106
msgid ""
"Number between -2.0 and 2.0. Positive values decrease the model's likelihood "
"to talk about new topics"
msgstr ""
"Szám -2,0 és 2,0 között. A pozitív értékek csökkentik annak valószínűségét, "
"hogy a modell új témákról beszéljen."

#: src/handlers/llm/openai_handler.py:108
#, fuzzy
msgid "Custom Options"
msgstr "Egyéni üzenet"

#: src/handlers/llm/openai_handler.py:108
msgid "Provide a JSON containing the custom options"
msgstr ""

#: src/handlers/llm/openrouter_handler.py:14
msgid "Provider Sorting"
msgstr "Szolgáltató rendezés"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Choose providers based on pricing/throughput or latency"
msgstr "Válasszon szolgáltatókat ár/átviteli sebesség vagy késleltetés alapján"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Price"
msgstr "Ár"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Throughput"
msgstr "Átviteli sebesség"

#: src/handlers/llm/openrouter_handler.py:14
msgid "Latency"
msgstr "Késleltetés"

#: src/handlers/llm/openrouter_handler.py:15
msgid "Providers Order"
msgstr "Szolgáltatók sorrendje"

#: src/handlers/llm/openrouter_handler.py:15
msgid ""
"Add order of providers to use, names separated by a comma.\n"
"Empty to not specify"
msgstr ""
"Adja meg a használandó szolgáltatók sorrendjét, a neveket vesszővel "
"elválasztva.\n"
"Hagyja üresen, ha nem kívánja megadni."

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow Fallbacks"
msgstr "Visszalépések engedélyezése"

#: src/handlers/llm/openrouter_handler.py:16
msgid "Allow fallbacks to other providers"
msgstr "Más szolgáltatókra való visszalépések engedélyezése"

#: src/handlers/rag/rag_handler.py:104
msgid "Index your documents"
msgstr "Dokumentumok indexelése"

#: src/handlers/rag/rag_handler.py:105
msgid ""
"Index all the documents in your document folder. You have to run this "
"operation every time you edit/create a document, change document analyzer or "
"change embedding model"
msgstr ""
"Indexelje az összes dokumentumot a dokumentum mappájában. Ezt a műveletet "
"minden alkalommal el kell végeznie, amikor módosít vagy létrehoz egy "
"dokumentumot, megváltoztatja a dokumentumelemzőt vagy a beágyazási modellt."

#: src/handlers/stt/custom_handler.py:13 src/handlers/tts/custom_handler.py:17
msgid "Command to execute"
msgstr "Futtatandó parancs"

#: src/handlers/stt/custom_handler.py:14
#, python-brace-format
msgid "{0} will be replaced with the model fullpath"
msgstr "{0} helyére a modell teljes útvonala kerül."

#: src/handlers/stt/googlesr_handler.py:14
msgid "API Key for Google SR, write 'default' to use the default one"
msgstr ""
"API kulcs a Google SR-hez, írja be a „default” szót az alapértelmezett "
"használatához"

#: src/handlers/stt/googlesr_handler.py:21
#: src/handlers/stt/groqsr_handler.py:29
#: src/handlers/stt/openaisr_handler.py:32
#: src/handlers/stt/whispercpp_handler.py:47
msgid "Language"
msgstr "Nyelv"

#: src/handlers/stt/googlesr_handler.py:22
msgid "The language of the text to recgnize in IETF"
msgstr "A felismert szöveg nyelve az IETF szabvány szerint"

#: src/handlers/stt/groqsr_handler.py:14
msgid "API Key for Groq SR, write 'default' to use the default one"
msgstr ""
"API kulcs a Groq SR-hez, írja be a „default” szót az alapértelmezett "
"használatához"

#: src/handlers/stt/groqsr_handler.py:21
msgid "Groq Model"
msgstr "Groq modell"

#: src/handlers/stt/groqsr_handler.py:22
msgid "Name of the Groq Model"
msgstr "A Groq modell neve"

#: src/handlers/stt/groqsr_handler.py:30
msgid ""
"Specify the language for transcription. Use ISO 639-1 language codes (e.g. "
"\"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Adja meg az átírás nyelvét. Használja az ISO 639-1 nyelvkódokat (pl. „en” "
"angolhoz, „fr” franciához stb.). "

#: src/handlers/stt/openaisr_handler.py:11
msgid "Endpoint for OpenAI requests"
msgstr "Végpont az OpenAI kérésekhez"

#: src/handlers/stt/openaisr_handler.py:18
msgid "API Key for OpenAI"
msgstr "API kulcs az OpenAI-hoz"

#: src/handlers/stt/openaisr_handler.py:25
msgid "Whisper Model"
msgstr "Whisper modell"

#: src/handlers/stt/openaisr_handler.py:26
msgid "Name of the OpenAI model"
msgstr "Az OpenAI modell neve"

#: src/handlers/stt/openaisr_handler.py:33
msgid ""
"Optional: Specify the language for transcription. Use ISO 639-1 language "
"codes (e.g. \"en\" for English, \"fr\" for French, etc.). "
msgstr ""
"Opcionális: Adja meg az átírás nyelvét. Használja az ISO 639-1 nyelvkódokat "
"(pl. „en” angolhoz, „fr” franciához stb.). "

#: src/handlers/stt/vosk_handler.py:17
msgid "Model Path"
msgstr "Modellútvonal"

#: src/handlers/stt/vosk_handler.py:18
msgid "Absolute path to the VOSK model (unzipped)"
msgstr "Abszolút elérési út a VOSK modellhez (kicsomagolva)"

#: src/handlers/stt/whisper_handler.py:16
#: src/handlers/stt/whispercpp_handler.py:41
msgid "Name of the Whisper model"
msgstr "A Whisper modell neve"

#: src/handlers/stt/witai_handler.py:13
msgid "Server Access Token for wit.ai"
msgstr "Szerver hozzáférési token a wit.ai-hoz"

#: src/handlers/stt/sphinx_handler.py:19
msgid "Could not understand the audio"
msgstr "Nem sikerült értelmezni a hangot"

#: src/handlers/stt/whispercpp_handler.py:47
#, fuzzy
msgid "Language of the recognition. For example en, it..."
msgstr "A felismerés nyelve."

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Model Library"
msgstr "Modellkönyvtár"

#: src/handlers/stt/whispercpp_handler.py:48
msgid "Manage Whisper models"
msgstr "Whisper modellek kezelése"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "Advanced Settings"
msgstr "Haladó beállítások"

#: src/handlers/stt/whispercpp_handler.py:49
msgid "More advanced settings"
msgstr "További haladó beállítások"

#: src/handlers/stt/whispercpp_handler.py:50
msgid "Temperature to use"
msgstr "Használandó hőmérséklet"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt for the recognition"
msgstr "Felismerési üzenet"

#: src/handlers/stt/whispercpp_handler.py:51
msgid "Prompt to use for the recognition"
msgstr "A felismertetéshez használandó üzenet"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Endpoint"
msgstr "Végpont"

#: src/handlers/tts/custom_openai_tts.py:17
msgid "Custom endpoint of the service to use"
msgstr "A használandó szolgáltatás egyéni végpontja"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
#: src/handlers/tts/elevenlabs_handler.py:18 src/handlers/tts/tts.py:33
#: src/ui/settings.py:129
msgid "Voice"
msgstr "Hang"

#: src/handlers/tts/custom_openai_tts.py:19
#: src/handlers/tts/groq_tts_handler.py:33
#: src/handlers/tts/openai_tts_handler.py:19
msgid "The voice to use"
msgstr "A használandó hang"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid "Instructions"
msgstr "Utasítások"

#: src/handlers/tts/custom_openai_tts.py:21
#: src/handlers/tts/openai_tts_handler.py:21
msgid ""
"Instructions for the voice generation. Leave it blank to avoid this field"
msgstr ""
"Utasítások a hanggeneráláshoz. Hagyja üresen, hogy elkerülje ezt a mezőt"

#: src/handlers/tts/custom_handler.py:17
#, python-brace-format
msgid "{0} will be replaced with the file fullpath, {1} with the text"
msgstr "{0} helyére a fájl teljes elérési útja, {1} helyére a szöveg kerül."

#: src/handlers/tts/elevenlabs_handler.py:11
msgid "API Key for ElevenLabs"
msgstr "API kulcs az ElevenLabs-hez"

#: src/handlers/tts/elevenlabs_handler.py:19
msgid "Voice ID to use"
msgstr "Használandó hangazonosító"

#: src/handlers/tts/elevenlabs_handler.py:33
msgid "Stability"
msgstr "Stabilitás"

#: src/handlers/tts/elevenlabs_handler.py:34
msgid "stability of the voice"
msgstr "A hang stabilitása"

#: src/handlers/tts/elevenlabs_handler.py:43
msgid "Similarity boost"
msgstr "Hasonlóság növelése"

#: src/handlers/tts/elevenlabs_handler.py:44
msgid "Boosts overall voice clarity and speaker similarity"
msgstr "Növeli az általános hangtisztaságot és a beszéd hasonlóságát"

#: src/handlers/tts/elevenlabs_handler.py:53
msgid "Style exaggeration"
msgstr "Stílus túlzása"

#: src/handlers/tts/elevenlabs_handler.py:54
msgid ""
"High values are reccomended if the style of the speech must be exaggerated"
msgstr "Magas értékek javasoltak, ha a beszéd stílusát túlzottá kell tenni"

#: src/handlers/tts/tts.py:34
msgid "Choose the preferred voice"
msgstr "Válassza ki a kívánt hangot"

#: src/handlers/websearch/tavily.py:20
msgid "Token"
msgstr "Token"

#: src/handlers/websearch/tavily.py:20
msgid "Tavily API key"
msgstr "Tavily API kulcs"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Max Results"
msgstr "Max találatok"

#: src/handlers/websearch/tavily.py:21
#: src/handlers/websearch/duckduckgo_handler.py:14
msgid "Number of results to consider"
msgstr "Figyelembe veendő találatok száma"

#: src/handlers/websearch/tavily.py:22
msgid "The depth of the search"
msgstr "A keresés mélysége"

#: src/handlers/websearch/tavily.py:22
msgid ""
"The depth of the search. Advanced search is tailored to retrieve the most "
"relevant sources and content snippets for your query, while basic search "
"provides generic content snippets from each source. A basic search costs 1 "
"API Credit, while an advanced search costs 2 API Credits."
msgstr ""
"A keresés mélysége. Az „Advanced” (haladó) keresés a legrelevánsabb "
"forrásokat és tartalomrészleteket célozza meg a lekérdezéséhez, míg az "
"„Basic” (alap) keresés általános tartalomrészleteket biztosít minden "
"forrásból. Egy alap keresés 1 API kreditbe, míg egy haladó keresés 2 API "
"kreditbe kerül."

#: src/handlers/websearch/tavily.py:23
msgid "The category of the search"
msgstr "A keresés kategóriája"

#: src/handlers/websearch/tavily.py:23
msgid ""
"The category of the search. News is useful for retrieving real-time updates, "
"particularly about politics, sports, and major current events covered by "
"mainstream media sources. General is for broader, more general-purpose "
"searches that may include a wide range of sources."
msgstr ""
"A keresés kategóriája. A „Hírek” hasznos a valós idejű frissítések "
"lekérdezéséhez, különösen a politikáról, sportról és a mainstream média "
"által tárgyalt nagyobb aktuális eseményekről. Az „Általános” szélesebb, "
"általánosabb célú keresésekhez használható, amelyek sokféle forrást "
"tartalmazhatnak."

#: src/handlers/websearch/tavily.py:24
msgid "Chunks per source"
msgstr "Szeletek forrásonként"

#: src/handlers/websearch/tavily.py:24
msgid ""
"The number of content chunks to retrieve from each source. Each chunk's "
"length is maximum 500 characters. Available only when search depth is "
"advanced."
msgstr ""
"A forrásonként lekérdezhető tartalomszeletek száma. Minden szelet maximális "
"hossza 500 karakter. Csak akkor érhető el, ha a keresési mélység „haladó”."

#: src/handlers/websearch/tavily.py:25
msgid "Number of days back from the current date to include"
msgstr "Hány nappal visszamenőleg vegyen figyelembe az aktuális dátumtól"

#: src/handlers/websearch/tavily.py:25
msgid "Available only if topic is news."
msgstr "Csak akkor érhető el, ha a téma „hírek”."

#: src/handlers/websearch/tavily.py:26
msgid "Include answer"
msgstr "Válasz mellékelése"

#: src/handlers/websearch/tavily.py:26
msgid ""
"Include an LLM-generated answer to the provided query. Basic search returns "
"a quick answer. Advanced returns a more detailed answer."
msgstr ""
"Mellékeljen egy LLM által generált választ a megadott lekérdezésre. Az alap "
"keresés gyors választ ad. A haladó keresés részletesebb választ ad."

#: src/handlers/websearch/tavily.py:27
msgid "Include raw content"
msgstr "Nyers tartalom mellékelése"

#: src/handlers/websearch/tavily.py:27
msgid "Include the cleaned and parsed HTML content of each search result."
msgstr ""
"Tartalmazza az egyes keresési eredmények megtisztított és elemzett HTML "
"tartalmát."

#: src/handlers/websearch/tavily.py:28
msgid "Include images"
msgstr "Képek mellékelése"

#: src/handlers/websearch/tavily.py:28
msgid "Perform an image search and include the results in the response."
msgstr "Képes keresés végrehajtása és az eredmények mellékelése a válaszban."

#: src/handlers/websearch/tavily.py:29
msgid "Include image descriptions"
msgstr "Képleírások mellékelése"

#: src/handlers/websearch/tavily.py:29
msgid ""
"When Include images is enabled, also add a descriptive text for each image."
msgstr ""
"Ha a „Képek mellékelése” engedélyezve van, adjon hozzá leíró szöveget is "
"minden képhez."

#: src/handlers/websearch/tavily.py:30
msgid "Include domains"
msgstr "Domainek mellékelése"

#: src/handlers/websearch/tavily.py:30
msgid "A list of domains to specifically include in the search results."
msgstr ""
"Azon domainek listája, amelyeket kifejezetten bele kell foglalni a keresési "
"eredményekbe."

#: src/handlers/websearch/tavily.py:31
msgid "Exclude domains"
msgstr "Domainek kizárása"

#: src/handlers/websearch/tavily.py:31
msgid "A list of domains to specifically exclude from the search results."
msgstr ""
"Azon domainek listája, amelyeket kifejezetten ki kell zárni a keresési "
"eredményekből."

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region"
msgstr "Régió"

#: src/handlers/websearch/duckduckgo_handler.py:15
msgid "Region for the search results"
msgstr "Régió a keresési eredményekhez"

#: src/ui/profile.py:33 src/window.py:119
msgid "Settings"
msgstr "Beállítások"

#: src/ui/profile.py:53
msgid "Profile Name"
msgstr "Profil neve"

#: src/ui/profile.py:58
msgid "Copied Settings"
msgstr "Másolt beállítások"

#: src/ui/profile.py:58
msgid "Settings that will be copied to the new profile"
msgstr "Beállítások, amelyek az új profilba másolódnak"

#: src/ui/profile.py:70
msgid "Create Profile"
msgstr "Profil létrehozása"

#: src/ui/profile.py:72 src/ui/profile.py:74 src/ui/profile.py:129
msgid "Import Profile"
msgstr "Profil importálása"

#: src/ui/profile.py:79 src/ui/widgets/profilerow.py:43
msgid "Edit Profile"
msgstr "Profil szerkesztése"

#: src/ui/profile.py:84 src/ui/profile.py:99 src/ui/profile.py:123
msgid "Export Profile"
msgstr "Profil exportálása"

#: src/ui/profile.py:87
msgid "Export Passwords"
msgstr "Jelszavak exportálása"

#: src/ui/profile.py:87
msgid "Also export password-like fields"
msgstr "Jelszószerű mezők exportálása is"

#: src/ui/profile.py:89
msgid "Export Propic"
msgstr "Profilkép exportálása"

#: src/ui/profile.py:89
msgid "Also export the profile picture"
msgstr "A profilkép exportálása is"

#: src/ui/profile.py:109 src/ui/explorer.py:692
msgid "Create"
msgstr "Létrehozás"

#: src/ui/profile.py:109
msgid "Apply"
msgstr "Alkalmaz"

#: src/ui/profile.py:116
msgid "The settings of the current profile will be copied into the new one"
msgstr "Az aktuális profil beállításai átmásolódnak az újba"

#: src/ui/profile.py:122 src/ui/profile.py:128
msgid "Newelle Profiles"
msgstr "Newelle profilok"

#: src/ui/profile.py:123
msgid "Export"
msgstr "Exportálás"

#: src/ui/profile.py:129
msgid "Import"
msgstr "Importálás"

#: src/ui/profile.py:197
msgid "Set profile picture"
msgstr "Profilkép beállítása"

#: src/ui/thread_editing.py:6 src/window.py:117
msgid "Thread editing"
msgstr "Szál szerkesztése"

#: src/ui/thread_editing.py:36
msgid "No threads are running"
msgstr "Nem fut szál"

#: src/ui/thread_editing.py:42
msgid "Thread number: "
msgstr "Szál száma: "

#: src/ui/widgets/profilerow.py:26
msgid "Select profile"
msgstr "Profil kiválasztása"

#: src/ui/widgets/profilerow.py:53
msgid "Delete Profile"
msgstr "Profil törlése"

#: src/ui/widgets/thinking.py:26
msgid "Thoughts"
msgstr "Gondolatok"

#: src/ui/widgets/thinking.py:27 src/ui/widgets/thinking.py:134
msgid "Expand to see details"
msgstr "Kibontás a részletek megtekintéséhez"

#: src/ui/widgets/thinking.py:122
msgid "Thinking..."
msgstr "Gondolkodás..."

#: src/ui/widgets/thinking.py:123
msgid "The LLM is thinking... Expand to see thought process"
msgstr ""
"Az LLM gondolkodik... Bontsa ki a gondolkodási folyamat megtekintéséhez"

#: src/ui/widgets/thinking.py:136
msgid "No thought process recorded"
msgstr "Nincs rögzített gondolkodási folyamat"

#: src/ui/widgets/tipscarousel.py:41
msgid "Newelle Tips"
msgstr "Newelle tippek"

#: src/ui/explorer.py:192
msgid "Folder is Empty"
msgstr "A mappa üres"

#: src/ui/explorer.py:340 src/window.py:1580
msgid "File not found"
msgstr "Fájl nem található"

#: src/ui/explorer.py:355
msgid "Open in new tab"
msgstr "Megnyitás új lapon"

#: src/ui/explorer.py:357
msgid "Open in integrated editor"
msgstr "Megnyitás beépített szerkesztőben"

#: src/ui/explorer.py:360 src/ui/explorer.py:589
msgid "Open in file manager"
msgstr "Megnyitás fájlkezelőben"

#: src/ui/explorer.py:363 src/ui/explorer.py:451
msgid "Rename"
msgstr "Átnevezés"

#: src/ui/explorer.py:366 src/ui/explorer.py:502 src/ui/settings.py:276
#: src/ui/settings.py:362
msgid "Delete"
msgstr "Törlés"

#: src/ui/explorer.py:369
msgid "Copy full path"
msgstr "Teljes útvonal másolása"

#: src/ui/explorer.py:420 src/ui/explorer.py:657
msgid "Failed to open file manager"
msgstr "Nem sikerült megnyitni a fájlkezelőt"

#: src/ui/explorer.py:436
msgid "New name:"
msgstr "Új név:"

#: src/ui/explorer.py:448 src/ui/explorer.py:501 src/ui/explorer.py:689
#: src/main.py:209
msgid "Cancel"
msgstr "Mégse"

#: src/ui/explorer.py:471
msgid "Renamed successfully"
msgstr "Sikeresen átnevezve"

#: src/ui/explorer.py:476
#, python-brace-format
msgid "Failed to rename: {}"
msgstr "Sikertelen átnevezés: {}"

#: src/ui/explorer.py:497
msgid "Delete File?"
msgstr "Fájl törlése?"

#: src/ui/explorer.py:499
#, python-brace-format
msgid "Are you sure you want to delete \"{}\"?"
msgstr "Biztosan törölni szeretné a „{}” fájlt?"

#: src/ui/explorer.py:520
msgid "Deleted successfully"
msgstr "Sikeresen törölve"

#: src/ui/explorer.py:525
#, python-brace-format
msgid "Failed to delete: {}"
msgstr "Sikertelen törlés: {}"

#: src/ui/explorer.py:538
msgid "Path copied to clipboard"
msgstr "Az útvonal másolva a vágólapra"

#: src/ui/explorer.py:542
msgid "Failed to copy path"
msgstr "Nem sikerült másolni az útvonalat"

#: src/ui/explorer.py:580
msgid "Create new folder"
msgstr "Új mappa létrehozása"

#: src/ui/explorer.py:583
msgid "Create new file"
msgstr "Új fájl létrehozása"

#: src/ui/explorer.py:586
msgid "Open Terminal Here"
msgstr "Terminál megnyitása itt"

#: src/ui/explorer.py:640
msgid "Create New Folder"
msgstr "Új mappa létrehozása"

#: src/ui/explorer.py:640
msgid "Folder name:"
msgstr "Mappa neve:"

#: src/ui/explorer.py:644
msgid "Create New File"
msgstr "Új fájl létrehozása"

#: src/ui/explorer.py:644
msgid "File name:"
msgstr "Fájl neve:"

#: src/ui/explorer.py:713
msgid "Folder created successfully"
msgstr "Mappa sikeresen létrehozva"

#: src/ui/explorer.py:720
msgid "File created successfully"
msgstr "Fájl sikeresen létrehozva"

#: src/ui/explorer.py:725
msgid "A file or folder with that name already exists"
msgstr "Már létezik fájl vagy mappa ezzel a névvel"

#: src/ui/explorer.py:728
msgid "folder"
msgstr "mappa"

#: src/ui/explorer.py:728
msgid "file"
msgstr "fájl"

#: src/ui/explorer.py:730
#, python-brace-format
msgid "Failed to create {}: {}"
msgstr "Sikertelen létrehozás {}: {}"

#: src/ui/shortcuts.py:6
msgid "Help"
msgstr "Súgó"

#: src/ui/shortcuts.py:12
msgid "Shortcuts"
msgstr "Gyorsbillentyűk"

#: src/ui/shortcuts.py:13
msgid "Reload chat"
msgstr "Csevegés újratöltése"

#: src/ui/shortcuts.py:14
msgid "Reload folder"
msgstr "Mappa újratöltése"

#: src/ui/shortcuts.py:15
msgid "New tab"
msgstr "Új lap"

#: src/ui/shortcuts.py:16
msgid "Paste Image"
msgstr "Kép beillesztése"

#: src/ui/shortcuts.py:17
msgid "Focus message box"
msgstr "Üzenetdoboz fókuszálása"

#: src/ui/shortcuts.py:18
msgid "Start/stop recording"
msgstr "Felvétel indítása/leállítása"

#: src/ui/shortcuts.py:19
msgid "Save"
msgstr "Mentés"

#: src/ui/shortcuts.py:20
msgid "Stop TTS"
msgstr "TTS leállítása"

#: src/ui/shortcuts.py:21
msgid "Zoom in"
msgstr "Nagyítás"

#: src/ui/shortcuts.py:22
msgid "Zoom out"
msgstr "Kicsinyítés"

#: src/ui/stdout_monitor.py:31 src/ui/stdout_monitor.py:41
#: src/ui/settings.py:269
msgid "Program Output Monitor"
msgstr "Programkimenet-figyelő"

#: src/ui/stdout_monitor.py:48
msgid "Clear output"
msgstr "Kimenet törlése"

#: src/ui/stdout_monitor.py:61
msgid "Start/Stop monitoring"
msgstr "Figyelés indítása/leállítása"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:182
#: src/ui/stdout_monitor.py:191 src/window.py:3702
msgid "Monitoring: Active"
msgstr "Figyelés: Aktív"

#: src/ui/stdout_monitor.py:109 src/ui/stdout_monitor.py:208
msgid "Monitoring: Stopped"
msgstr "Figyelés: Leállítva"

#: src/ui/stdout_monitor.py:116 src/ui/stdout_monitor.py:252
#, python-brace-format
msgid "Lines: {}"
msgstr "Sorok: {}"

#: src/ui/stdout_monitor.py:267
msgid "Lines: 0"
msgstr "Sorok: 0"

#: src/ui/extension.py:17 src/ui/presentation.py:131 src/constants.py:513
#: src/window.py:118
msgid "Extensions"
msgstr "Bővítmények"

#: src/ui/extension.py:50
msgid "Installed Extensions"
msgstr "Telepített bővítmények"

#: src/ui/extension.py:86
msgid "User guide to Extensions"
msgstr "Felhasználói útmutató a bővítményekhez"

#: src/ui/extension.py:89
msgid "Download new Extensions"
msgstr "Új bővítmények letöltése"

#: src/ui/extension.py:92
msgid "Install extension from file..."
msgstr "Bővítmény telepítése fájlból..."

#: src/ui/mini_window.py:9 data/io.github.qwersyk.Newelle.appdata.xml.in:7
#: data/io.github.qwersyk.Newelle.desktop.in:2
msgid "Newelle"
msgstr "Newelle"

#: src/ui/mini_window.py:20
msgid "Chat is opened in mini window"
msgstr "A csevegés mini ablakban nyílt meg"

#: src/ui/presentation.py:93
msgid "Welcome to Newelle"
msgstr "Üdvözöljük a Newelle-ben"

#: src/ui/presentation.py:94
msgid "Your ultimate virtual assistant."
msgstr "Az Ön kiváló virtuális asszisztense."

#: src/ui/presentation.py:98
msgid "Github Page"
msgstr "Github oldal"

#: src/ui/presentation.py:105
msgid "Choose your favourite AI Language Model"
msgstr "Válassza ki kedvenc AI nyelvi modelljét"

#: src/ui/presentation.py:106
msgid ""
"Newelle can be used with mutiple models and providers!\n"
"<b>Note: It is strongly suggested to read the Guide to LLM page</b>"
msgstr ""
"A Newelle több modellel és szolgáltatóval is használható!\n"
"<b>Megjegyzés: Erősen ajánlott elolvasni az LLM útmutató oldalt</b>"

#: src/ui/presentation.py:110
msgid "Guide to LLM"
msgstr "LLM útmutató"

#: src/ui/presentation.py:117
msgid "Chat with your documents"
msgstr "Csevegés dokumentumokkal"

#: src/ui/presentation.py:118
msgid ""
"Newelle can retrieve relevant information from documents you send in the "
"chat or from your own files! Information relevant to your query will be sent "
"to the LLM."
msgstr ""
"A Newelle képes releváns információkat lekérdezni a csevegésben küldött "
"dokumentumokból vagy a saját fájljaiból! A lekérdezéséhez kapcsolódó "
"információk elküldésre kerülnek az LLM-nek."

#: src/ui/presentation.py:124 src/ui/settings.py:237 src/window.py:649
msgid "Command virtualization"
msgstr "Parancs virtualizálás"

#: src/ui/presentation.py:125
msgid ""
"Newelle can be used to run commands on your system, but pay attention at "
"what you run! <b>The LLM is not under our control, so it might generate "
"malicious code!</b>\n"
"By default, your commands will be <b>virtualized in the Flatpak environment</"
"b>, but pay attention!"
msgstr ""
"A Newelle parancsok futtatására használható a rendszerén, de figyeljen oda, "
"mit futtat! <b>Az LLM nem áll az ellenőrzésünk alatt, ezért rosszindulatú "
"kódot generálhat!</b>\n"
"Alapértelmezés szerint a parancsok <b>virtualizálva lesznek a Flatpak "
"környezetben</b>, de figyeljen oda!"

#: src/ui/presentation.py:132
msgid "You can extend Newelle's functionalities using extensions!"
msgstr "Kibővítheti a Newelle funkcionalitását bővítményekkel!"

#: src/ui/presentation.py:136
msgid "Download extensions"
msgstr "Bővítmények letöltése"

#: src/ui/presentation.py:146
msgid "Permission Error"
msgstr "Engedélyhiba"

#: src/ui/presentation.py:147
msgid ""
"Newelle does not have enough permissions to run commands on your system."
msgstr ""
"A Newelle-nek nincs elegendő engedélye parancsok futtatásához a rendszerén."

#: src/ui/presentation.py:158
msgid "Begin using the app"
msgstr "Az alkalmazás használatának megkezdése"

#: src/ui/presentation.py:163
msgid "Start chatting"
msgstr "Csevegés indítása"

#: src/ui/settings.py:47 src/constants.py:523
msgid "General"
msgstr "Általános"

#: src/ui/settings.py:48 src/constants.py:478
msgid "LLM"
msgstr "LLM"

#: src/ui/settings.py:49 src/constants.py:528
msgid "Prompts"
msgstr "Üzenetek"

#: src/ui/settings.py:50
msgid "Knowledge"
msgstr "Tudás"

#: src/ui/settings.py:54
msgid "Language Model"
msgstr "Nyelvi modell"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other LLMs"
msgstr "Egyéb LLM-ek"

#: src/ui/settings.py:63 src/ui/settings.py:83
msgid "Other available LLM providers"
msgstr "Egyéb elérhető LLM szolgáltatók"

#: src/ui/settings.py:73
msgid "Advanced LLM Settings"
msgstr "Haladó LLM beállítások"

#: src/ui/settings.py:77
msgid "Secondary Language Model"
msgstr "Másodlagos nyelvi modell"

#: src/ui/settings.py:77
msgid ""
"Model used for secondary tasks, like offer, chat name and memory generation"
msgstr ""
"Másodlagos feladatokhoz, például ajánlatokhoz, csevegésnevekhez és memória "
"generáláshoz használt modell"

#: src/ui/settings.py:94
msgid "Embedding Model"
msgstr "Beágyazási modell"

#: src/ui/settings.py:94
msgid ""
"Embedding is used to trasform text into vectors. Used by Long Term Memory "
"and RAG. Changing it might require you to re-index documents or reset memory."
msgstr ""
"A beágyazás szöveg vektorokká alakítására szolgál. A hosszú távú memória és "
"a RAG használja. A módosítása szükségessé teheti a dokumentumok "
"újrainexelését vagy a memória alaphelyzetbe állítását."

#: src/ui/settings.py:105 src/window.py:647
msgid "Long Term Memory"
msgstr "Hosszú távú memória"

#: src/ui/settings.py:105
msgid "Keep memory of old conversations"
msgstr "Régi beszélgetések megőrzése"

#: src/ui/settings.py:117 src/constants.py:401
msgid "Web Search"
msgstr "Webes keresés"

#: src/ui/settings.py:117
msgid "Search information on the Web"
msgstr "Információ keresése a weben"

#: src/ui/settings.py:133
msgid "Text To Speech Program"
msgstr "Szövegfelolvasó program"

#: src/ui/settings.py:133
msgid "Choose which text to speech to use"
msgstr "Válassza ki a használni kívánt szövegfelolvasót"

#: src/ui/settings.py:142
msgid "Speech To Text Engine"
msgstr "Beszédfelismerő motor"

#: src/ui/settings.py:142
msgid "Choose which speech recognition engine you want"
msgstr "Válassza ki a használni kívánt beszédfelismerő motort"

#: src/ui/settings.py:150
msgid "Automatic Speech To Text"
msgstr "Automatikus beszédfelismerés"

#: src/ui/settings.py:150
msgid "Automatically restart speech to text at the end of a text/TTS"
msgstr "Automatikusan újraindítja a beszédfelismerést egy szöveg/TTS végén"

#: src/ui/settings.py:154
msgid "Prompt control"
msgstr "Üzenet vezérlés"

#: src/ui/settings.py:159
msgid "Interface"
msgstr "Felület"

#: src/ui/settings.py:162
msgid "Interface Size"
msgstr "Felület mérete"

#: src/ui/settings.py:162
msgid "Adjust the size of the interface"
msgstr "A felület méretének beállítása"

#: src/ui/settings.py:174
msgid "Editor color scheme"
msgstr "Szerkesztő színvilága"

#: src/ui/settings.py:174
msgid "Change the color scheme of the editor and codeblocks"
msgstr "A szerkesztő és a kódblokkok színvilágának módosítása"

#: src/ui/settings.py:181
msgid "Hidden files"
msgstr "Rejtett fájlok"

#: src/ui/settings.py:181
msgid "Show hidden files"
msgstr "Rejtett fájlok megjelenítése"

#: src/ui/settings.py:187
msgid "Send with ENTER"
msgstr "Küldés ENTER billentyűvel"

#: src/ui/settings.py:187
msgid ""
"If enabled, messages will be sent with ENTER, to go to a new line use "
"CTRL+ENTER. If disabled, messages will be sent with SHIFT+ENTER, and newline "
"with enter"
msgstr ""
"Ha engedélyezve van, az üzeneteket ENTER-rel küldi el, új sorba a CTRL+ENTER "
"billentyűkkel lehet lépni. Ha letiltva van, az üzeneteket SHIFT+ENTER-rel "
"küldi el, és az ENTER új sort hoz létre."

#: src/ui/settings.py:193
msgid "Remove thinking from history"
msgstr "Gondolkodás eltávolítása az előzményekből"

#: src/ui/settings.py:193
msgid ""
"Do not send old thinking blocks for reasoning models in order to reduce "
"token usage"
msgstr ""
"Ne küldjön régi gondolkodási blokkokat a gondolkodó modelleknek a token "
"felhasználás csökkentése érdekében."

#: src/ui/settings.py:199
msgid "Display LaTeX"
msgstr "LaTeX megjelenítése"

#: src/ui/settings.py:199
msgid "Display LaTeX formulas in chat"
msgstr "LaTeX képletek megjelenítése a csevegésben"

#: src/ui/settings.py:205
msgid "Reverse Chat Order"
msgstr "Csevegés sorrendjének megfordítása"

#: src/ui/settings.py:205
msgid "Show most recent chats on top in chat list (change chat to apply)"
msgstr ""
"A legutóbbi csevegések megjelenítése felül a csevegéslistán (a csevegés "
"módosítása után lép érvénybe)"

#: src/ui/settings.py:211
msgid "Automatically Generate Chat Names"
msgstr "Automatikus csevegésnevek generálása"

#: src/ui/settings.py:211
msgid "Generate chat names automatically after the first two messages"
msgstr "A csevegésnevek automatikus generálása az első két üzenet után"

#: src/ui/settings.py:217
msgid "Number of offers"
msgstr "Ajánlatok száma"

#: src/ui/settings.py:217
msgid "Number of message suggestions to send to chat "
msgstr "A csevegéshez küldendő üzenetjavaslatok száma "

#: src/ui/settings.py:224
msgid "Username"
msgstr "Felhasználónév"

#: src/ui/settings.py:224
#, python-brace-format
msgid ""
"Change the label that appears before your message\n"
"This information is not sent to the LLM by default\n"
"You can add it to a prompt using the {USER} variable"
msgstr ""
"Módosítsa az üzenete előtt megjelenő címkét\n"
"Ez az információ alapértelmezés szerint nem kerül elküldésre az LLM-nek\n"
"Hozzáadhatja egy üzenethez a {USER} változó használatával"

#: src/ui/settings.py:234
msgid "Neural Network Control"
msgstr "Neuronhálózati vezérlés"

#: src/ui/settings.py:237
msgid "Run commands in a virtual machine"
msgstr "Parancsok futtatása virtuális gépen"

#: src/ui/settings.py:250
msgid "External Terminal"
msgstr "Külső terminál"

#: src/ui/settings.py:250
msgid "Choose the external terminal where to run the console commands"
msgstr ""
"Válassza ki a külső terminált, ahol a konzolparancsokat futtatni szeretné"

#: src/ui/settings.py:259
msgid "Program memory"
msgstr "Programmemória"

#: src/ui/settings.py:259
msgid "How long the program remembers the chat "
msgstr "Meddig emlékszik a program a csevegésre "

#: src/ui/settings.py:266
msgid "Developer"
msgstr "Fejlesztő"

#: src/ui/settings.py:269
msgid ""
"Monitor the program output in real-time, useful for debugging and seeing "
"downloads progress"
msgstr ""
"Figyelje a program kimenetét valós időben, ami hasznos hibakereséshez és a "
"letöltések előrehaladásának nyomon követéséhez"

#: src/ui/settings.py:270
msgid "Open"
msgstr "Megnyitás"

#: src/ui/settings.py:275
msgid "Delete pip path"
msgstr "Pip útvonal törlése"

#: src/ui/settings.py:275
msgid "Remove the extra dependencies installed"
msgstr "Az extra telepített függőségek eltávolítása"

#: src/ui/settings.py:281
msgid "Install pip module"
msgstr ""

#: src/ui/settings.py:281
msgid "Manually install pip module"
msgstr ""

#: src/ui/settings.py:310
msgid "Auto-run commands"
msgstr "Parancsok automatikus futtatása"

#: src/ui/settings.py:310
msgid "Commands that the bot will write will automatically run"
msgstr "A bot által írt parancsok automatikusan futnak"

#: src/ui/settings.py:313
msgid "Max number of commands"
msgstr "Parancsok maximális száma"

#: src/ui/settings.py:313
msgid ""
"Maximum number of commands that the bot will write after a single user "
"request"
msgstr ""
"A parancsok maximális száma, amelyet a bot egyetlen felhasználói kérés után "
"írni fog"

#: src/ui/settings.py:344
msgid "Browser"
msgstr "Böngésző"

#: src/ui/settings.py:344
msgid "Settings for the browser"
msgstr "Böngésző beállításai"

#: src/ui/settings.py:349
msgid "Use external browser"
msgstr "Külső böngésző használata"

#: src/ui/settings.py:349
msgid "Use an external browser to open links instead of integrated one"
msgstr "Külső böngésző használata a linkek megnyitásához az integrált helyett"

#: src/ui/settings.py:356
msgid "Persist browser session"
msgstr "Böngésző munkamenet megtartása"

#: src/ui/settings.py:356
msgid ""
"Persist browser session between restarts. Turning this off requires "
"restarting the program"
msgstr ""
"Böngésző munkamenet megőrzése újraindítások között. Ennek kikapcsolása a "
"program újraindítását igényli"

#: src/ui/settings.py:361
msgid "Delete browser data"
msgstr "Böngészőadatok törlése"

#: src/ui/settings.py:361
msgid "Delete browser session and data"
msgstr "Böngésző munkamenet és adatok törlése"

#: src/ui/settings.py:368
msgid "Initial browser page"
msgstr "Kezdő böngészőoldal"

#: src/ui/settings.py:368
msgid "The page where the browser will start"
msgstr "Az oldal, ahol a böngésző elindul"

#: src/ui/settings.py:375
msgid "Search string"
msgstr "Keresési karakterlánc"

#: src/ui/settings.py:375
#, python-format
msgid "The search string used in the browser, %s is replaced with the query"
msgstr ""
"A böngészőben használt keresési karakterlánc, a %s helyére a lekérdezés kerül"

#: src/ui/settings.py:396
msgid "Document Sources (RAG)"
msgstr "Dokumentumforrások (RAG)"

#: src/ui/settings.py:396
msgid "Include content from your documents in the responses"
msgstr "Dokumentumok tartalmának mellékelése a válaszokban"

#: src/ui/settings.py:397
msgid "Document Analyzer"
msgstr "Dokumentumelemző"

#: src/ui/settings.py:397
msgid ""
"The document analyzer uses multiple techniques to extract relevant "
"information about your documents"
msgstr ""
"A dokumentumelemző több technikát is használ a releváns információk "
"kinyerésére a dokumentumokból"

#: src/ui/settings.py:408
msgid "Read documents if unsupported"
msgstr "Dokumentumok olvasása, ha nem támogatott"

#: src/ui/settings.py:408
msgid ""
"If the LLM does not support reading documents, relevant information about "
"documents sent in the chat will be given to the LLM using your Document "
"Analyzer."
msgstr ""
"Ha az LLM nem támogatja a dokumentumok olvasását, a csevegésben küldött "
"dokumentumokról releváns információk kerülnek az LLM-hez az Ön "
"dokumentumelemzőjével."

#: src/ui/settings.py:412
msgid "Maximum tokens for RAG"
msgstr "Maximális tokenek RAG-hez"

#: src/ui/settings.py:412
msgid ""
"The maximum amount of tokens to be used for RAG. If the documents do not "
"exceed this token count,\n"
"dump all of them in the context"
msgstr ""
"A RAG-hez használható tokenek maximális száma. Ha a dokumentumok nem "
"haladják meg ezt a tokenszámot,\n"
"az összeset kiürítjük a kontextusba"

#: src/ui/settings.py:429
msgid "Document Folder"
msgstr "Dokumentummappa"

#: src/ui/settings.py:429
msgid ""
"Put the documents you want to query in your document folder. The document "
"analyzer will find relevant information in them if this option is enabled"
msgstr ""
"Helyezze a lekérdezni kívánt dokumentumokat a dokumentum mappájába. A "
"dokumentumelemző releváns információkat talál bennük, ha ez az opció "
"engedélyezve van."

#: src/ui/settings.py:432
msgid "Put all the documents you want to index in this folder"
msgstr "Helyezze ebbe a mappába az összes indexelni kívánt dokumentumot"

#: src/ui/settings.py:468
msgid "Silence threshold"
msgstr "Csend küszöb"

#: src/ui/settings.py:468
msgid ""
"Silence threshold in seconds, percentage of the volume to be considered "
"silence"
msgstr ""
"Csendküszöb másodpercekben, a hangerő százalékos aránya, amelyet csendnek "
"kell tekinteni"

#: src/ui/settings.py:481
msgid "Silence time"
msgstr "Csend időtartama"

#: src/ui/settings.py:481
msgid "Silence time in seconds before recording stops automatically"
msgstr ""
"Csend időtartama másodpercekben, mielőtt a felvétel automatikusan leáll"

#: src/ui/settings.py:1061
msgid "Not enough permissions"
msgstr "Nincs elegendő engedély"

#: src/ui/settings.py:1065
msgid ""
"Newelle does not have enough permissions to run commands on your system, "
"please run the following command"
msgstr ""
"A Newelle-nek nincs elegendő engedélye parancsok futtatásához a rendszerén, "
"kérjük, futtassa a következő parancsot"

#: src/ui/settings.py:1066 src/ui/settings.py:1079
msgid "Understood"
msgstr "Értem"

#: src/ui/settings.py:1078
msgid "Pip path deleted"
msgstr "Pip útvonal törölve"

#: src/ui/settings.py:1078
msgid ""
"The pip path has been deleted, you can now reinstall the dependencies. This "
"operation requires a restart of the application."
msgstr ""
"A pip útvonal törölve lett, most újra telepítheti a függőségeket. Ez a "
"művelet az alkalmazás újraindítását igényli."

#: src/constants.py:20
msgid "Newelle Demo API"
msgstr "Newelle Demó API"

#: src/constants.py:26
msgid "GPT4Free"
msgstr "GPT4Free"

#: src/constants.py:34
msgid "Local Model"
msgstr "Helyi modell"

#: src/constants.py:35
msgid ""
"NO GPU SUPPORT, USE OLLAMA INSTEAD. Run a LLM model locally, more privacy "
"but slower"
msgstr ""
"NINCS GPU TÁMOGATÁS, HASZNÁLJON OLLAMA-t. Futtasson LLM modellt helyben, "
"nagyobb adatvédelem, de lassabb"

#: src/constants.py:40
msgid "Ollama Instance"
msgstr "Ollama példány"

#: src/constants.py:41
msgid "Easily run multiple LLM models on your own hardware"
msgstr "Könnyedén futtathat több LLM modellt saját hardverén"

#: src/constants.py:47
msgid "Groq"
msgstr "Groq"

#: src/constants.py:54 src/constants.py:230
msgid "Google Gemini API"
msgstr "Google Gemini API"

#: src/constants.py:60 src/constants.py:224 src/constants.py:225
msgid "OpenAI API"
msgstr "OpenAI API"

#: src/constants.py:61
msgid "OpenAI API. Custom endpoints supported. Use this for custom providers"
msgstr ""
"OpenAI API. Egyéni végpontok támogatottak. Ezt használja egyéni "
"szolgáltatókhoz."

#: src/constants.py:66
msgid "Anthropic Claude"
msgstr "Anthropic Claude"

#: src/constants.py:67
msgid ""
"Official APIs for Anthropic Claude's models, with image and file support, "
"requires an API key"
msgstr ""
"Az Anthropic Claude modelljeinek hivatalos API-jai, kép- és "
"fájltámogatással, API kulcsot igényelnek"

#: src/constants.py:73
msgid "Mistral"
msgstr "Mistral"

#: src/constants.py:74
msgid "Mistral API"
msgstr "Mistral API"

#: src/constants.py:80
msgid "OpenRouter"
msgstr "OpenRouter"

#: src/constants.py:81
msgid "Openrouter.ai API, supports lots of models"
msgstr "Openrouter.ai API, sok modellt támogat"

#: src/constants.py:87
msgid "Deepseek"
msgstr "Deepseek"

#: src/constants.py:88
msgid "Deepseek API, strongest open source models"
msgstr "Deepseek API, a legerősebb nyílt forráskódú modellek"

#: src/constants.py:94 src/constants.py:203
msgid "Custom Command"
msgstr "Egyéni parancs"

#: src/constants.py:95
msgid "Use the output of a custom command"
msgstr "Egyéni parancs kimenetének használata"

#: src/constants.py:104
msgid "Whisper C++"
msgstr "Whisper C++"

#: src/constants.py:105
msgid "Works offline. Optimized Whisper impelementation written in C++"
msgstr "Offline is működik. Optimalizált Whisper implementáció C++ nyelven."

#: src/constants.py:111
msgid "CMU Sphinx"
msgstr "CMU Sphinx"

#: src/constants.py:112
msgid "Works offline. Only English supported"
msgstr "Offline is működik. Csak angol támogatott"

#: src/constants.py:118
msgid "Google Speech Recognition"
msgstr "Google beszédfelismerés"

#: src/constants.py:119 src/constants.py:125
msgid "Google Speech Recognition online"
msgstr "Google online beszédfelismerés"

#: src/constants.py:124
msgid "Groq Speech Recognition"
msgstr "Groq beszédfelismerés"

#: src/constants.py:130
msgid "Wit AI"
msgstr "Wit AI"

#: src/constants.py:131
msgid "wit.ai speech recognition free API (language chosen on the website)"
msgstr ""
"wit.ai ingyenes beszédfelismerő API (a nyelvet a weboldalon lehet "
"kiválasztani)"

#: src/constants.py:137
msgid "Vosk API"
msgstr "Vosk API"

#: src/constants.py:138
msgid "Works Offline"
msgstr "Offline is működik"

#: src/constants.py:144
msgid "Whisper API"
msgstr "Whisper API"

#: src/constants.py:145
msgid "Uses OpenAI Whisper API"
msgstr "OpenAI Whisper API-t használ"

#: src/constants.py:151
msgid "Custom command"
msgstr "Egyéni parancs"

#: src/constants.py:152
msgid "Runs a custom command"
msgstr "Egyéni parancsot futtat"

#: src/constants.py:161
msgid "Google TTS"
msgstr "Google TTS"

#: src/constants.py:162
msgid "Google's text to speech"
msgstr "A Google szövegfelolvasása"

#: src/constants.py:167
msgid "Kokoro TTS"
msgstr "Kokoro TTS"

#: src/constants.py:168
msgid ""
"Lightweight and fast open source TTS engine. ~3GB dependencies, 400MB model"
msgstr ""
"Könnyű és gyors nyílt forráskódú TTS motor. ~3 GB függőség, 400 MB modell"

#: src/constants.py:173
msgid "ElevenLabs TTS"
msgstr "ElevenLabs TTS"

#: src/constants.py:174
msgid "Natural sounding TTS"
msgstr "Természetes hangzású TTS"

#: src/constants.py:179 src/constants.py:180
msgid "OpenAI TTS"
msgstr "OpenAI TTS"

#: src/constants.py:185
msgid "Groq TTS"
msgstr "Groq TTS"

#: src/constants.py:186
msgid "Groq TTS API"
msgstr "Groq TTS API"

#: src/constants.py:191 src/constants.py:192
msgid "Custom OpenAI TTS"
msgstr "Egyéni OpenAI TTS"

#: src/constants.py:197
msgid "Espeak TTS"
msgstr "Espeak TTS"

#: src/constants.py:198
msgid "Offline TTS"
msgstr "Offline TTS"

#: src/constants.py:204
#, python-brace-format
msgid "Use a custom command as TTS, {0} will be replaced with the text"
msgstr "Egyéni parancs használata TTS-ként, a {0} helyére a szöveg kerül."

#: src/constants.py:212
msgid "WordLlama"
msgstr "WordLlama"

#: src/constants.py:213
msgid ""
"Light local embedding model based on llama. Works offline, very low "
"resources usage"
msgstr ""
"Könnyű, helyi beágyazási modell llama alapokon. Offline is működik, nagyon "
"alacsony erőforrás-felhasználás"

#: src/constants.py:218
msgid "Ollama Embedding"
msgstr "Ollama beágyazás"

#: src/constants.py:219
msgid ""
"Use Ollama models for Embedding. Works offline, very low resources usage"
msgstr ""
"Ollama modellek használata beágyazáshoz. Offline is működik, nagyon alacsony "
"erőforrás-felhasználás"

#: src/constants.py:231
msgid "Use Google Gemini API to get embeddings"
msgstr "A Google Gemini API használata beágyazások lekéréséhez"

#: src/constants.py:239
msgid "User Summary"
msgstr "Felhasználói összefoglaló"

#: src/constants.py:240
msgid "Generate a summary of the user's conversation"
msgstr "A felhasználói beszélgetés összefoglalójának generálása"

#: src/constants.py:245
msgid "Memoripy"
msgstr "Memoripy"

#: src/constants.py:246
msgid ""
"Extract messages from previous conversations using contextual memory "
"retrivial, memory decay, concept extraction and other advanced techniques. "
"Does 1 llm call per message."
msgstr ""
"Üzenetek kinyerése korábbi beszélgetésekből kontextuális memória "
"visszanyerésével, memóriaromlással, koncepciókinyeréssel és egyéb haladó "
"technikákkal. Üzenetenként 1 llm hívást végez."

#: src/constants.py:251
msgid "User Summary + Memoripy"
msgstr "Felhasználói összefoglaló + Memoripy"

#: src/constants.py:252
msgid "Use both technologies for long term memory"
msgstr "Mindkét technológia használata a hosszú távú memóriához"

#: src/constants.py:260
msgid "Document reader"
msgstr "Dokumentumolvasó"

#: src/constants.py:261
msgid ""
"Classic RAG approach - chunk documents and embed them, then compare them to "
"the query and return the most relevant documents"
msgstr ""
"Klasszikus RAG megközelítés – dokumentumok darabolása és beágyazása, majd "
"összehasonlítása a lekérdezéssel, és a legrelevánsabb dokumentumok "
"visszaadása"

#: src/constants.py:269
msgid "SearXNG"
msgstr "SearXNG"

#: src/constants.py:270
msgid "SearXNG - Private and selfhostable search engine"
msgstr "SearXNG – Privát és saját tárhelyű keresőmotor"

#: src/constants.py:275
msgid "DuckDuckGo"
msgstr "DuckDuckGo"

#: src/constants.py:276
msgid "DuckDuckGo search"
msgstr "DuckDuckGo keresés"

#: src/constants.py:281
msgid "Tavily"
msgstr "Tavily"

#: src/constants.py:282
msgid "Tavily search"
msgstr "Tavily keresés"

#: src/constants.py:375
msgid "Helpful assistant"
msgstr "Segítőkész asszisztens"

#: src/constants.py:376
msgid "General purpose prompt to enhance the LLM answers and give more context"
msgstr ""
"Általános célú üzenet az LLM válaszainak javítására és további kontextus "
"biztosítására"

#: src/constants.py:384
msgid "Console access"
msgstr "Konzol hozzáférés"

#: src/constants.py:385
msgid "Can the program run terminal commands on the computer"
msgstr "Futtathat-e a program terminálparancsokat a számítógépen"

#: src/constants.py:392
msgid "Current directory"
msgstr "Aktuális könyvtár"

#: src/constants.py:393
msgid "What is the current directory"
msgstr "Mi az aktuális könyvtár"

#: src/constants.py:402
msgid "Allow the LLM to search on the internet"
msgstr "Engedélyezze az LLM számára az internetes keresést"

#: src/constants.py:410
msgid "Basic functionality"
msgstr "Alapvető funkcionalitás"

#: src/constants.py:411
msgid "Showing tables and code (*can work without it)"
msgstr "Táblázatok és kód megjelenítése (*nélkül is működhet)"

#: src/constants.py:419
msgid "Graphs access"
msgstr "Grafikonok hozzáférés"

#: src/constants.py:420
msgid "Can the program display graphs"
msgstr "Képes-e a program grafikonokat megjeleníteni"

#: src/constants.py:428
msgid "Show image"
msgstr "Kép megjelenítése"

#: src/constants.py:429
msgid "Show image in chat"
msgstr "Kép megjelenítése a csevegésben"

#: src/constants.py:437
msgid "Custom Prompt"
msgstr "Egyéni üzenet"

#: src/constants.py:438
msgid "Add your own custom prompt"
msgstr "Adja hozzá saját egyéni üzenetét"

#: src/constants.py:480
msgid "LLM and Secondary LLM settings"
msgstr "LLM és másodlagos LLM beállítások"

#: src/constants.py:483 src/window.py:648
msgid "TTS"
msgstr "TTS"

#: src/constants.py:485
msgid "Text to Speech settings"
msgstr "Szövegfelolvasó beállítások"

#: src/constants.py:488
msgid "STT"
msgstr "STT"

#: src/constants.py:490
msgid "Speech to Text settings"
msgstr "Beszédfelismerő beállítások"

#: src/constants.py:493
msgid "Embedding"
msgstr "Beágyazás"

#: src/constants.py:495
msgid "Embedding settings"
msgstr "Beágyazási beállítások"

#: src/constants.py:498
msgid "Memory"
msgstr "Memória"

#: src/constants.py:500
msgid "Memory settings"
msgstr "Memória beállítások"

#: src/constants.py:503
msgid "Websearch"
msgstr "Webkeresés"

#: src/constants.py:505
msgid "Websearch settings"
msgstr "Webkeresési beállítások"

#: src/constants.py:508
msgid "RAG"
msgstr "RAG"

#: src/constants.py:510
msgid "Document analyzer settings"
msgstr "Dokumentumelemző beállításai"

#: src/constants.py:515
msgid "Extensions settings"
msgstr "Bővítmények beállításai"

#: src/constants.py:518
msgid "Inteface"
msgstr "Felület"

#: src/constants.py:520
msgid "Interface settings, hidden files, reverse order, zoom..."
msgstr "Felület beállításai, rejtett fájlok, fordított sorrend, nagyítás..."

#: src/constants.py:525
msgid ""
"General settings, virtualization, offers, memory length, automatically "
"generate chat name, current folder..."
msgstr ""
"Általános beállítások, virtualizáció, ajánlatok, memóriahossz, automatikus "
"csevegésnév generálás, aktuális mappa..."

#: src/constants.py:530
msgid "Prompts settings, custom extra prompt, custom prompts..."
msgstr "Üzenet beállítások, egyéni extra üzenet, egyéni üzenetek..."

#: src/controller.py:135 src/window.py:1852
msgid "Chat "
msgstr "Csevegés "

#: src/main.py:205
msgid "Terminal threads are still running in the background"
msgstr "A terminálszálak még mindig futnak a háttérben"

#: src/main.py:206
msgid "When you close the window, they will be automatically terminated"
msgstr "Az ablak bezárásakor automatikusan leállnak"

#: src/main.py:210
msgid "Close"
msgstr "Bezárás"

#: src/main.py:244
msgid "Chat is rebooted"
msgstr "A csevegés újraindult"

#: src/main.py:249
msgid "Folder is rebooted"
msgstr "A mappa újraindult"

#: src/main.py:254
msgid "Chat is created"
msgstr "A csevegés létrejött"

#: src/window.py:120
msgid "Keyboard shorcuts"
msgstr "Billentyűparancsok"

#: src/window.py:121
msgid "About"
msgstr "Névjegy"

#: src/window.py:128 src/window.py:197
msgid "Chat"
msgstr "Csevegés"

#: src/window.py:170
msgid "History"
msgstr "Előzmények"

#: src/window.py:191
msgid "Create a chat"
msgstr "Csevegés létrehozása"

#: src/window.py:196
msgid "Chats"
msgstr "Csevegések"

#: src/window.py:267
msgid " Stop"
msgstr " Leállítás"

#: src/window.py:282
msgid " Clear"
msgstr " Törlés"

#: src/window.py:297
msgid " Continue"
msgstr " Folytatás"

#: src/window.py:310
msgid " Regenerate"
msgstr " Újragenerálás"

#: src/window.py:376
msgid "Send a message..."
msgstr "Üzenet küldése..."

#: src/window.py:467
msgid "Explorer Tab"
msgstr "Fájlkezelő lap"

#: src/window.py:468
msgid "Terminal Tab"
msgstr "Terminál lap"

#: src/window.py:469
msgid "Browser Tab"
msgstr "Böngésző lap"

#: src/window.py:589
msgid "Ask about a website"
msgstr "Kérdezzen egy weboldalról"

#: src/window.py:589
msgid "Write #https://website.com in chat to ask information about a website"
msgstr ""
"Írja be a #https://website.com címet a csevegésbe, hogy információt kérjen "
"egy weboldalról"

#: src/window.py:590
msgid "Check out our Extensions!"
msgstr "Nézze meg bővítményeinket!"

#: src/window.py:590
msgid "We have a lot of extensions for different things. Check it out!"
msgstr "Sok bővítményünk van különböző dolgokhoz. Nézze meg!"

#: src/window.py:591
msgid "Chat with documents!"
msgstr "Csevegés dokumentumokkal!"

#: src/window.py:591
msgid ""
"Add your documents to your documents folder and chat using the information "
"contained in them!"
msgstr ""
"Adja hozzá dokumentumait a dokumentum mappájához, és csevegjen az azokban "
"található információk felhasználásával!"

#: src/window.py:592
msgid "Surf the web!"
msgstr "Böngésszen az interneten!"

#: src/window.py:592
msgid ""
"Enable web search to allow the LLM to surf the web and provide up to date "
"answers"
msgstr ""
"Engedélyezze a webes keresést, hogy az LLM böngészhessen a weben és "
"naprakész válaszokat adjon"

#: src/window.py:593
msgid "Mini Window"
msgstr "Mini ablak"

#: src/window.py:593
msgid "Ask questions on the fly using the mini window mode"
msgstr "Tegyen fel kérdéseket menet közben a mini ablak móddal"

#: src/window.py:594
msgid "Text to Speech"
msgstr "Szövegfelolvasó"

#: src/window.py:594
msgid "Newelle supports text-to-speech! Enable it in the settings"
msgstr "A Newelle támogatja a szövegfelolvasást! Engedélyezze a beállításokban"

#: src/window.py:595
msgid "Keyboard Shortcuts"
msgstr "Billentyűparancsok"

#: src/window.py:595
msgid "Control Newelle using Keyboard Shortcuts"
msgstr "A Newelle vezérlése billentyűparancsokkal"

#: src/window.py:596
msgid "Prompt Control"
msgstr "Üzenet vezérlés"

#: src/window.py:596
msgid "Newelle gives you 100% prompt control. Tune your prompts for your use."
msgstr ""
"A Newelle 100%-os üzenetvezérlést biztosít. Hangolja üzeneteit a "
"felhasználásához."

#: src/window.py:597
msgid "Thread Editing"
msgstr "Szál szerkesztése"

#: src/window.py:597
msgid "Check the programs and processes you run from Newelle"
msgstr "Ellenőrizze a Newelle-ből futtatott programokat és folyamatokat"

#: src/window.py:598
msgid "Programmable Prompts"
msgstr "Programozható üzenetek"

#: src/window.py:598
msgid ""
"You can add dynamic prompts to Newelle, with conditions and probabilities"
msgstr ""
"Dinamikus üzeneteket adhat hozzá a Newelle-hez, feltételekkel és "
"valószínűségekkel"

#: src/window.py:605
msgid "New Chat"
msgstr "Új csevegés"

#: src/window.py:623
msgid "Provider Errror"
msgstr "Szolgáltató hiba"

#: src/window.py:646
msgid "Local Documents"
msgstr "Helyi dokumentumok"

#: src/window.py:650
msgid "Web search"
msgstr "Webes keresés"

#: src/window.py:896
msgid "This provider does not have a model list"
msgstr "Ennek a szolgáltatónak nincs modelllistája"

#: src/window.py:901
msgid " Models"
msgstr " Modellek"

#: src/window.py:904
msgid "Search Models..."
msgstr "Modellek keresése..."

#: src/window.py:1132
msgid "Create new profile"
msgstr "Új profil létrehozása"

#: src/window.py:1266
msgid "Could not recognize your voice"
msgstr "Nem sikerült felismerni a hangját"

#: src/window.py:1303
msgid "Images"
msgstr "Képek"

#: src/window.py:1307
msgid "LLM Supported Files"
msgstr "LLM támogatott fájlok"

#: src/window.py:1315
msgid "RAG Supported files"
msgstr "RAG támogatott fájlok"

#: src/window.py:1333
msgid "Supported Files"
msgstr "Támogatott fájlok"

#: src/window.py:1337
msgid "All Files"
msgstr "Összes fájl"

#: src/window.py:1343
msgid "Attach file"
msgstr "Fájl csatolása"

#: src/window.py:1598
msgid "The file cannot be sent until the program is finished"
msgstr "A fájl nem küldhető el, amíg a program be nem fejeződik"

#: src/window.py:1620
msgid "The file is not recognized"
msgstr "A fájl nem ismerhető fel"

#: src/window.py:1639
msgid "You can no longer continue the message."
msgstr "Nem folytathatja az üzenetet."

#: src/window.py:1664
msgid "You can no longer regenerate the message."
msgstr "Nem generálhatja újra az üzenetet."

#: src/window.py:1896
msgid "Chat is cleared"
msgstr "A csevegés törölve"

#: src/window.py:1921
msgid "The message was canceled and deleted from history"
msgstr "Az üzenet törölve és eltávolítva az előzményekből"

#: src/window.py:1965
msgid "The message cannot be sent until the program is finished"
msgstr "Az üzenet nem küldhető el, amíg a program be nem fejeződik"

#: src/window.py:2954
msgid "You can't edit a message while the program is running."
msgstr "Nem szerkeszthet üzenetet, amíg a program fut."

#: src/window.py:3080
msgid "Prompt content"
msgstr "Üzenet tartalma"

#: src/window.py:3339
msgid ""
"The neural network has access to your computer and any data in this chat and "
"can run commands, be careful, we are not responsible for the neural network. "
"Do not share any sensitive information."
msgstr ""
"A neuronhálózat hozzáfér a számítógépéhez és az ebben a csevegésben lévő "
"bármely adathoz, és parancsokat futtathat, legyen óvatos, nem vagyunk "
"felelősek a neuronhálózatért. Ne osszon meg semmilyen érzékeny információt."

#: src/window.py:3368
msgid ""
"The neural network has access to any data in this chat, be careful, we are "
"not responsible for the neural network. Do not share any sensitive "
"information."
msgstr ""
"A neuronhálózat hozzáfér a csevegésben lévő bármely adathoz, legyen óvatos, "
"nem vagyunk felelősek a neuronhálózatért. Ne osszon meg semmilyen érzékeny "
"információt."

#: src/window.py:3417
msgid "Wrong folder path"
msgstr "Hibás mappaelérési út"

#: src/window.py:3450
msgid "Thread has not been completed, thread number: "
msgstr "A szál nem fejeződött be, szál száma: "

#: src/window.py:3462
msgid "Failed to open the folder"
msgstr "Nem sikerült megnyitni a mappát"

#: src/window.py:3641
msgid "Chat is empty"
msgstr "A csevegés üres"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:9
msgid ""
"Train Newelle to do more with custom extensions and new AI modules, giving "
"your chatbot endless possibilities."
msgstr ""
"Képezze a Newelle-t egyéni bővítményekkel és új AI modulokkal, végtelen "
"lehetőségeket adva a chatbotjának."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:11
msgid "AI chatbot"
msgstr "AI chatbot"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:15
msgid "Quick profile selection"
msgstr "Gyors profilválasztás"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:19
msgid "Message Editing"
msgstr "Üzenet szerkesztése"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:23
msgid "More than 10 standard AI providers"
msgstr "Több mint 10 szabványos AI szolgáltató"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:38
#: data/io.github.qwersyk.Newelle.appdata.xml.in:62
#: data/io.github.qwersyk.Newelle.appdata.xml.in:85
#: data/io.github.qwersyk.Newelle.appdata.xml.in:147
#: data/io.github.qwersyk.Newelle.appdata.xml.in:152
#: data/io.github.qwersyk.Newelle.appdata.xml.in:157
#: data/io.github.qwersyk.Newelle.appdata.xml.in:162
#: data/io.github.qwersyk.Newelle.appdata.xml.in:167
msgid "Bug fixes"
msgstr "Hibajavítások"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:45
msgid ""
"Mini Apps support! Extensions can now show custom mini apps on the sidebar"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:46
msgid ""
"Added integrated browser Mini App: browse the web directly in Newelle and "
"attach web pages"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:47
msgid "Improved integrated file manager, supporting multiple file operations"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:48
msgid "Integrated file editor: edit files and codeblocks directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:49
msgid "Integrated Terminal mini app: open the terminal directly in Newelle"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:50
msgid ""
"Programmable prompts: add dynamic content to prompts with conditionals and "
"random strings"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:51
msgid "Add ability to manually edit chat name"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:52
#, fuzzy
msgid "Minor bug fixes"
msgstr "Hibajavítások"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:53
msgid "Added support for multiple languages for Kokoro TTS and Whisper.CPP"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:54
msgid "Run HTML/CSS/JS websites directly in app"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:55
msgid "New animation on chat change"
msgstr ""

#: data/io.github.qwersyk.Newelle.appdata.xml.in:63
msgid "Small improvements"
msgstr "Apró fejlesztések"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:70
msgid "Improve local documents reading and loading performances"
msgstr "A helyi dokumentumok olvasási és betöltési teljesítményének javítása"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:71
msgid "Add option to send with CTRL+Enter"
msgstr "Hozzáadva a CTRL+Enter billentyűvel való küldés lehetősége"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:72
msgid "Improve codeblocks"
msgstr "Kódblokkok fejlesztése"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:73
msgid "Fix Kokoro TTS"
msgstr "Kokoro TTS javítása"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:74
msgid "Remove emoji from TTS"
msgstr "Hangulatjelek eltávolítása a TTS-ből"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:75
msgid "Set API keys as password fields"
msgstr "API kulcsok jelszómezőként való beállítása"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:76
msgid "Add thinking support for Gemini"
msgstr "Gondolkodási támogatás hozzáadása a Geminihez"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:77
msgid "Updated translations"
msgstr "Frissített fordítások"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:84
msgid "Added new features"
msgstr "Új funkciók hozzáadva"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:92
msgid "Website reading and web search with SearXNG, DuckDuckGo, and Tavily"
msgstr ""
"Weboldal olvasás és webes keresés SearXNG, DuckDuckGo és Tavily segítségével"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:93
msgid "Improved LaTeX rendering and document management"
msgstr "Javított LaTeX renderelés és dokumentumkezelés"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:94
msgid "New Thinking Widget and OpenRouter handler"
msgstr "Új gondolkodási widget és OpenRouter kezelő"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:95
msgid "Vision support for Llama4 on Groq"
msgstr "Látás támogatás a Llama4-hez Groq-on"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:96
msgid "New translations (Traditional Chinese, Bengali, Hindi)"
msgstr "Új fordítások (hagyományos kínai, bengáli, hindi)"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:102
msgid "Fixed many bugs, added some features!"
msgstr "Sok hiba javítva, néhány funkció hozzáadva!"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:107
msgid "Support for new features and bug fixes"
msgstr "Új funkciók és hibajavítások támogatása"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:112
#: data/io.github.qwersyk.Newelle.appdata.xml.in:117
#: data/io.github.qwersyk.Newelle.appdata.xml.in:122
msgid "Added many new features and bug fixes"
msgstr "Sok új funkció és hibajavítás hozzáadva"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:127
#: data/io.github.qwersyk.Newelle.appdata.xml.in:132
msgid "Added new features and bug fixes"
msgstr "Új funkciók és hibajavítások hozzáadva"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:137
msgid ""
"Updated the g4f library with versioning, added user guides, improved "
"extension browsing, and enhanced model handling."
msgstr ""
"Frissült a g4f könyvtár verziózással, felhasználói útmutatók kerültek "
"hozzáadásra, javult a bővítmények böngészése, és a modellkezelés is "
"fejlettebb lett."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:142
msgid ""
"Bug fixes and new features have been implemented. We've modified the "
"extension architecture, added new models, and introduced vision support, "
"along with more capabilities."
msgstr ""
"Hibajavítások és új funkciók kerültek bevezetésre. Módosítottuk a bővítmény "
"architektúráját, új modelleket adtunk hozzá, és bevezettük a látás "
"támogatást, valamint további képességeket."

#: data/io.github.qwersyk.Newelle.appdata.xml.in:172
msgid "Stable version"
msgstr "Stabil verzió"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:177
msgid "Added extension"
msgstr "Hozzáadott bővítmény"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:182
msgid "Blacklist of commands"
msgstr "Parancsok feketelistája"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:187
msgid "Localization"
msgstr "Lokalizáció"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:192
msgid "Redesign"
msgstr "Újratervezés"

#: data/io.github.qwersyk.Newelle.appdata.xml.in:196
msgid "Qwersyk"
msgstr "Qwersyk"

#: data/io.github.qwersyk.Newelle.desktop.in:3
msgid "Newelle: Your advanced chat bot"
msgstr "Newelle: Az Ön fejlett chatbotja"

#: data/io.github.qwersyk.Newelle.desktop.in:10
msgid "ai;assistant;chat;chatgpt;gpt;llm;ollama;"
msgstr "ai;asszisztens;csevegés;chatgpt;gpt;llm;ollama;"

#~ msgid "max Tokens"
#~ msgstr "Max tokenek"

#~ msgid "Max tokens of the generated text"
#~ msgstr "A generált szöveg maximális tokenjei"
